import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useCreateCampagneInventaire, useInitialiserCampagne } from '@/hooks/useInventaire';
import { useBoutiques } from '@/hooks/useBoutiques';
import { useEmployes } from '@/hooks/useEmployes';

const campagneSchema = z.object({
  nom: z.string().min(1, 'Le nom est requis'),
  boutique_id: z.string().min(1, 'La boutique est requise'),
  responsable_id: z.string().optional(),
  commentaires: z.string().optional(),
});

type CampagneFormData = z.infer<typeof campagneSchema>;

interface CampagneInventaireFormProps {
  onClose: () => void;
  boutiqueId?: string;
}

export const CampagneInventaireForm = ({ onClose, boutiqueId }: CampagneInventaireFormProps) => {
  const [isCreating, setIsCreating] = useState(false);
  
  const { data: boutiques } = useBoutiques();
  const { data: employes } = useEmployes();
  const createCampagne = useCreateCampagneInventaire();
  const initialiserCampagne = useInitialiserCampagne();

  const form = useForm<CampagneFormData>({
    resolver: zodResolver(campagneSchema),
    defaultValues: {
      nom: '',
      boutique_id: boutiqueId || '',
      responsable_id: '',
      commentaires: '',
    },
  });

  const onSubmit = async (data: CampagneFormData) => {
    setIsCreating(true);
    try {
      // Créer la campagne
      const campagne = await createCampagne.mutateAsync({
        nom: data.nom,
        boutique_id: data.boutique_id,
        responsable_id: data.responsable_id,
        commentaires: data.commentaires,
      });
      
      // Initialiser automatiquement avec les stocks existants
      await initialiserCampagne.mutateAsync({
        campagneId: campagne.id,
        boutiqueId: data.boutique_id,
      });
      
      onClose();
    } catch (error) {
      console.error('Erreur lors de la création de la campagne:', error);
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Nouvelle campagne d'inventaire</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="nom"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nom de la campagne</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="ex: Inventaire mensuel janvier 2024"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="boutique_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Boutique</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionner une boutique" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {boutiques?.map((boutique) => (
                        <SelectItem key={boutique.id} value={boutique.id}>
                          {boutique.nom}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="responsable_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Responsable (optionnel)</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionner un employé" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {employes?.map((employe) => (
                        <SelectItem key={employe.id} value={employe.id}>
                          {employe.prenom} {employe.nom}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="commentaires"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Commentaires (optionnel)</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Notes ou instructions pour cette campagne..."
                      rows={3}
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Annuler
              </Button>
              <Button 
                type="submit" 
                disabled={isCreating}
              >
                {isCreating ? 'Création...' : 'Créer et initialiser'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};