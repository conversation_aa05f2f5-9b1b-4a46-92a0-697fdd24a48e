import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface Alerte {
  id: string;
  type: 'stock_bas' | 'stock_epuise' | 'transfert_retard' | 'vente_importante' | 'systeme';
  titre: string;
  message: string;
  boutique_id?: string;
  niveau: 'info' | 'warning' | 'error' | 'success';
  lu: boolean;
  created_at: string;
  boutiques?: { nom: string };
}

export const useAlertes = (boutiqueId?: string, nonLues?: boolean) => {
  return useQuery({
    queryKey: ['alertes', boutiqueId, nonLues],
    queryFn: async () => {
      let query = supabase
        .from('alertes')
        .select(`
          *,
          boutiques:boutique_id(nom)
        `);
      
      if (boutiqueId) {
        query = query.eq('boutique_id', boutiqueId);
      }
      
      if (nonLues) {
        query = query.eq('lu', false);
      }
      
      const { data, error } = await query.order('created_at', { ascending: false });
      
      if (error) throw error;
      return data as Alerte[];
    },
    // Alertes importantes, actualisation fréquente
    staleTime: 30 * 1000,     // 30 secondes
    gcTime: 2 * 60 * 1000,    // 2 minutes
    refetchInterval: 60 * 1000, // Actualisation auto toutes les minutes
  });
};

export const useMarquerAlerteLue = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (alerteId: string) => {
      const { data, error } = await supabase
        .from('alertes')
        .update({ lu: true })
        .eq('id', alerteId)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['alertes'] });
    }
  });
};