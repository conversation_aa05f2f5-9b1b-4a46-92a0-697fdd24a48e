import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Plus, Trash2, Search, User, ShoppingCart } from 'lucide-react';
import { useCreateVente } from '@/hooks/useVentes';
import { useCreateVenteDetail } from '@/hooks/useVenteDetails';
import { useBoutiques } from '@/hooks/useBoutiques';
import { useEmployes } from '@/hooks/useEmployes';
import { useClients, useCreateClient } from '@/hooks/useClients';
import { useStocks } from '@/hooks/useStocks';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';

const venteSchema = z.object({
  boutique_id: z.string().min(1, 'Sélectionnez une boutique'),
  employe_id: z.string().min(1, 'Sélectionnez un employé'),
  client_id: z.string().optional(),
  client_nom: z.string().optional(),
  client_telephone: z.string().optional(),
  client_email: z.string().email().optional().or(z.literal('')),
  mode_paiement: z.enum(['especes', 'carte', 'cheque', 'virement', 'mobile_money', 'crypto']),
});

const clientSchema = z.object({
  nom: z.string().min(1, 'Le nom est requis'),
  prenom: z.string().optional(),
  email: z.string().email().optional().or(z.literal('')),
  telephone: z.string().optional(),
  adresse: z.string().optional(),
});

interface ProduitVente {
  produit_id: string;
  nom: string;
  marque: string;
  modele: string;
  prix_vente: number;
  quantite: number;
  remise: number;
  stock_disponible: number;
}

interface VenteFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export const VenteForm: React.FC<VenteFormProps> = ({ onSuccess, onCancel }) => {
  const { toast } = useToast();
  const [produits, setProduits] = useState<ProduitVente[]>([]);
  const [searchClient, setSearchClient] = useState('');
  const [showNewClientDialog, setShowNewClientDialog] = useState(false);
  const [boutique_id, setBoutiqueId] = useState<string>('');

  const { data: boutiques } = useBoutiques();
  const { data: employes } = useEmployes();
  const { data: clients } = useClients(searchClient);
  const { data: stocks } = useStocks(boutique_id);
  
  const createVente = useCreateVente();
  const createVenteDetail = useCreateVenteDetail();
  const createClient = useCreateClient();

  const form = useForm({
    resolver: zodResolver(venteSchema),
    defaultValues: {
      boutique_id: '',
      employe_id: '',
      client_id: '',
      client_nom: '',
      client_telephone: '',
      client_email: '',
      mode_paiement: 'especes' as const,
    },
  });

  const clientForm = useForm({
    resolver: zodResolver(clientSchema),
    defaultValues: {
      nom: '',
      prenom: '',
      email: '',
      telephone: '',
      adresse: '',
    },
  });

  const ajouterProduit = (stock: any) => {
    if (stock.quantite <= 0) {
      toast({
        variant: "destructive",
        title: "Stock insuffisant",
        description: "Ce produit n'est pas en stock."
      });
      return;
    }

    const existant = produits.find(p => p.produit_id === stock.produit_id);
    if (existant) {
      if (existant.quantite >= stock.quantite) {
        toast({
          variant: "destructive",
          title: "Stock insuffisant",
          description: "Quantité demandée supérieure au stock disponible."
        });
        return;
      }
      setProduits(produits.map(p => 
        p.produit_id === stock.produit_id 
          ? { ...p, quantite: p.quantite + 1 }
          : p
      ));
    } else {
      setProduits([...produits, {
        produit_id: stock.produit_id,
        nom: stock.produits?.nom || '',
        marque: stock.produits?.marque || '',
        modele: stock.produits?.modele || '',
        prix_vente: stock.produits?.prix_vente || 0,
        quantite: 1,
        remise: 0,
        stock_disponible: stock.quantite,
      }]);
    }
  };

  const modifierQuantite = (produitId: string, nouvelleQuantite: number) => {
    const produit = produits.find(p => p.produit_id === produitId);
    if (!produit) return;

    if (nouvelleQuantite > produit.stock_disponible) {
      toast({
        variant: "destructive",
        title: "Stock insuffisant",
        description: "Quantité demandée supérieure au stock disponible."
      });
      return;
    }

    if (nouvelleQuantite <= 0) {
      setProduits(produits.filter(p => p.produit_id !== produitId));
    } else {
      setProduits(produits.map(p => 
        p.produit_id === produitId 
          ? { ...p, quantite: nouvelleQuantite }
          : p
      ));
    }
  };

  const modifierRemise = (produitId: string, nouvelleRemise: number) => {
    setProduits(produits.map(p => 
      p.produit_id === produitId 
        ? { ...p, remise: Math.max(0, nouvelleRemise) }
        : p
    ));
  };

  const calculerTotal = () => {
    return produits.reduce((total, produit) => {
      return total + (produit.quantite * produit.prix_vente - produit.remise);
    }, 0);
  };

  const onSubmit = async (data: any) => {
    if (produits.length === 0) {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Ajoutez au moins un produit à la vente."
      });
      return;
    }

    try {
      // Créer la vente
      const vente = await createVente.mutateAsync({
        boutique_id: data.boutique_id,
        employe_id: data.employe_id,
        client_id: data.client_id || undefined,
        client_nom: data.client_nom || undefined,
        client_telephone: data.client_telephone || undefined,
        client_email: data.client_email || undefined,
        montant_total: calculerTotal(),
        montant_tva: calculerTotal() * 0.18, // TVA à 18%
        mode_paiement: data.mode_paiement,
        statut: 'validee',
      });

      // Ajouter les détails de la vente
      for (const produit of produits) {
        await createVenteDetail.mutateAsync({
          vente_id: vente.id,
          produit_id: produit.produit_id,
          quantite: produit.quantite,
          prix_unitaire: produit.prix_vente,
          remise: produit.remise,
        });
      }

      toast({
        title: "Vente créée",
        description: `Vente ${vente.numero_facture} créée avec succès.`
      });

      onSuccess?.();
    } catch (error) {
      console.error('Erreur lors de la création de la vente:', error);
    }
  };

  const creerNouveauClient = async (data: any) => {
    try {
      const client = await createClient.mutateAsync({
        ...data,
        statut: 'actif',
      });
      
      form.setValue('client_id', client.id);
      form.setValue('client_nom', `${client.nom} ${client.prenom || ''}`.trim());
      form.setValue('client_telephone', client.telephone || '');
      form.setValue('client_email', client.email || '');
      
      setShowNewClientDialog(false);
      clientForm.reset();
    } catch (error) {
      console.error('Erreur lors de la création du client:', error);
    }
  };

  const selectionnerClient = (client: any) => {
    form.setValue('client_id', client.id);
    form.setValue('client_nom', `${client.nom} ${client.prenom || ''}`.trim());
    form.setValue('client_telephone', client.telephone || '');
    form.setValue('client_email', client.email || '');
    setSearchClient('');
  };

  useEffect(() => {
    setBoutiqueId(form.watch('boutique_id'));
  }, [form.watch('boutique_id')]);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            Nouvelle Vente
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="boutique_id">Boutique</Label>
                <Select onValueChange={(value) => form.setValue('boutique_id', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionnez une boutique" />
                  </SelectTrigger>
                  <SelectContent>
                    {boutiques?.map((boutique) => (
                      <SelectItem key={boutique.id} value={boutique.id}>
                        {boutique.nom}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="employe_id">Vendeur</Label>
                <Select onValueChange={(value) => form.setValue('employe_id', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionnez un vendeur" />
                  </SelectTrigger>
                  <SelectContent>
                    {employes?.map((employe) => (
                      <SelectItem key={employe.id} value={employe.id}>
                        {employe.nom} {employe.prenom}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="mode_paiement">Mode de paiement</Label>
              <Select onValueChange={(value) => form.setValue('mode_paiement', value as any)}>
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionnez un mode de paiement" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="especes">Espèces</SelectItem>
                  <SelectItem value="carte">Carte bancaire</SelectItem>
                  <SelectItem value="cheque">Chèque</SelectItem>
                  <SelectItem value="virement">Virement</SelectItem>
                  <SelectItem value="mobile_money">Mobile Money</SelectItem>
                  <SelectItem value="crypto">Cryptomonnaie</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Section Client */}
            <div className="space-y-2">
              <Label>Client (optionnel)</Label>
              <div className="flex gap-2">
                <Input
                  placeholder="Rechercher un client..."
                  value={searchClient}
                  onChange={(e) => setSearchClient(e.target.value)}
                />
                <Dialog open={showNewClientDialog} onOpenChange={setShowNewClientDialog}>
                  <DialogTrigger asChild>
                    <Button type="button" variant="outline">
                      <User className="h-4 w-4 mr-2" />
                      Nouveau
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Nouveau Client</DialogTitle>
                    </DialogHeader>
                    <form onSubmit={clientForm.handleSubmit(creerNouveauClient)} className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="nom">Nom *</Label>
                          <Input {...clientForm.register('nom')} />
                        </div>
                        <div>
                          <Label htmlFor="prenom">Prénom</Label>
                          <Input {...clientForm.register('prenom')} />
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="email">Email</Label>
                        <Input type="email" {...clientForm.register('email')} />
                      </div>
                      <div>
                        <Label htmlFor="telephone">Téléphone</Label>
                        <Input {...clientForm.register('telephone')} />
                      </div>
                      <div>
                        <Label htmlFor="adresse">Adresse</Label>
                        <Textarea {...clientForm.register('adresse')} />
                      </div>
                      <div className="flex gap-2 justify-end">
                        <Button type="button" variant="outline" onClick={() => setShowNewClientDialog(false)}>
                          Annuler
                        </Button>
                        <Button type="submit">Créer</Button>
                      </div>
                    </form>
                  </DialogContent>
                </Dialog>
              </div>

              {searchClient && clients && clients.length > 0 && (
                <div className="border rounded-md max-h-40 overflow-y-auto">
                  {clients.map((client) => (
                    <div
                      key={client.id}
                      className="p-2 hover:bg-muted cursor-pointer border-b last:border-b-0"
                      onClick={() => selectionnerClient(client)}
                    >
                      <div className="font-medium">{client.nom} {client.prenom}</div>
                      {client.telephone && <div className="text-sm text-muted-foreground">{client.telephone}</div>}
                    </div>
                  ))}
                </div>
              )}

              {form.watch('client_nom') && (
                <div className="p-2 bg-muted rounded-md">
                  <div className="font-medium">Client sélectionné: {form.watch('client_nom')}</div>
                  <Button
                    type="button"
                    variant="link"
                    size="sm"
                    onClick={() => {
                      form.setValue('client_id', '');
                      form.setValue('client_nom', '');
                      form.setValue('client_telephone', '');
                      form.setValue('client_email', '');
                    }}
                  >
                    Désélectionner
                  </Button>
                </div>
              )}
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Section Produits */}
      <Card>
        <CardHeader>
          <CardTitle>Produits disponibles</CardTitle>
        </CardHeader>
        <CardContent>
          {boutique_id && stocks && stocks.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {stocks
                .filter(stock => stock.quantite > 0)
                .map((stock) => (
                  <div key={stock.id} className="border rounded-lg p-4 hover:bg-muted/50">
                    <div className="font-medium">{stock.produits?.nom}</div>
                    <div className="text-sm text-muted-foreground">
                      {stock.produits?.marque} {stock.produits?.modele}
                    </div>
                    <div className="flex justify-between items-center mt-2">
                      <div>
                        <div className="text-lg font-bold">{stock.produits?.prix_vente}€</div>
                        <Badge variant={stock.quantite <= stock.seuil_alerte ? "destructive" : "secondary"}>
                          Stock: {stock.quantite}
                        </Badge>
                      </div>
                      <Button
                        type="button"
                        size="sm"
                        onClick={() => ajouterProduit(stock)}
                        disabled={stock.quantite <= 0}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
            </div>
          ) : (
            <div className="text-center text-muted-foreground py-8">
              {boutique_id ? "Aucun produit en stock" : "Sélectionnez une boutique pour voir les produits"}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Panier */}
      {produits.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Panier ({produits.length} produits)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {produits.map((produit) => (
                <div key={produit.produit_id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="font-medium">{produit.nom}</div>
                    <div className="text-sm text-muted-foreground">
                      {produit.marque} {produit.modele}
                    </div>
                    <div className="text-sm">Prix unitaire: {produit.prix_vente}€</div>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    <div>
                      <Label className="text-sm">Quantité</Label>
                      <Input
                        type="number"
                        min="1"
                        max={produit.stock_disponible}
                        value={produit.quantite}
                        onChange={(e) => modifierQuantite(produit.produit_id, parseInt(e.target.value) || 0)}
                        className="w-20"
                      />
                    </div>
                    
                    <div>
                      <Label className="text-sm">Remise (€)</Label>
                      <Input
                        type="number"
                        min="0"
                        value={produit.remise}
                        onChange={(e) => modifierRemise(produit.produit_id, parseFloat(e.target.value) || 0)}
                        className="w-20"
                      />
                    </div>
                    
                    <div className="text-right">
                      <div className="font-bold">
                        {(produit.quantite * produit.prix_vente - produit.remise).toFixed(2)}€
                      </div>
                    </div>
                    
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setProduits(produits.filter(p => p.produit_id !== produit.produit_id))}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
              
              <div className="flex justify-between items-center pt-4 border-t">
                <div className="text-xl font-bold">
                  Total: {calculerTotal().toFixed(2)}€
                </div>
                <div className="text-sm text-muted-foreground">
                  TVA (18%): {(calculerTotal() * 0.18).toFixed(2)}€
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Actions */}
      <div className="flex gap-4 justify-end">
        <Button type="button" variant="outline" onClick={onCancel}>
          Annuler
        </Button>
        <Button 
          onClick={form.handleSubmit(onSubmit)}
          disabled={produits.length === 0 || createVente.isPending}
        >
          {createVente.isPending ? 'Création...' : 'Créer la vente'}
        </Button>
      </div>
    </div>
  );
};