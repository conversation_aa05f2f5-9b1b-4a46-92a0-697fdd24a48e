export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.12 (cd3cf9e)"
  }
  public: {
    Tables: {
      alertes: {
        Row: {
          boutique_id: string | null
          created_at: string
          date_expiration: string | null
          employe_id: string | null
          id: string
          lu: boolean
          message: string
          niveau: string
          produit_id: string | null
          titre: string
          type: string
        }
        Insert: {
          boutique_id?: string | null
          created_at?: string
          date_expiration?: string | null
          employe_id?: string | null
          id?: string
          lu?: boolean
          message: string
          niveau?: string
          produit_id?: string | null
          titre: string
          type: string
        }
        Update: {
          boutique_id?: string | null
          created_at?: string
          date_expiration?: string | null
          employe_id?: string | null
          id?: string
          lu?: boolean
          message?: string
          niveau?: string
          produit_id?: string | null
          titre?: string
          type?: string
        }
        Relationships: [
          {
            foreignKeyName: "alertes_boutique_id_fkey"
            columns: ["boutique_id"]
            isOneToOne: false
            referencedRelation: "boutiques"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "alertes_employe_id_fkey"
            columns: ["employe_id"]
            isOneToOne: false
            referencedRelation: "employes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "alertes_produit_id_fkey"
            columns: ["produit_id"]
            isOneToOne: false
            referencedRelation: "produits"
            referencedColumns: ["id"]
          },
        ]
      }
      audit_logs: {
        Row: {
          boutique_id: string | null
          executed_at: string
          id: string
          new_data: Json | null
          old_data: Json | null
          operation: string
          table_name: string
          user_agent: string | null
          user_id: string | null
          user_ip: unknown | null
        }
        Insert: {
          boutique_id?: string | null
          executed_at?: string
          id?: string
          new_data?: Json | null
          old_data?: Json | null
          operation: string
          table_name: string
          user_agent?: string | null
          user_id?: string | null
          user_ip?: unknown | null
        }
        Update: {
          boutique_id?: string | null
          executed_at?: string
          id?: string
          new_data?: Json | null
          old_data?: Json | null
          operation?: string
          table_name?: string
          user_agent?: string | null
          user_id?: string | null
          user_ip?: unknown | null
        }
        Relationships: [
          {
            foreignKeyName: "audit_logs_boutique_id_fkey"
            columns: ["boutique_id"]
            isOneToOne: false
            referencedRelation: "boutiques"
            referencedColumns: ["id"]
          },
        ]
      }
      boutiques: {
        Row: {
          adresse: string
          created_at: string
          email: string | null
          id: string
          manager_id: string | null
          nom: string
          statut: string
          telephone: string | null
          type: string
          updated_at: string
        }
        Insert: {
          adresse: string
          created_at?: string
          email?: string | null
          id?: string
          manager_id?: string | null
          nom: string
          statut?: string
          telephone?: string | null
          type: string
          updated_at?: string
        }
        Update: {
          adresse?: string
          created_at?: string
          email?: string | null
          id?: string
          manager_id?: string | null
          nom?: string
          statut?: string
          telephone?: string | null
          type?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_boutiques_manager"
            columns: ["manager_id"]
            isOneToOne: false
            referencedRelation: "employes"
            referencedColumns: ["id"]
          },
        ]
      }
      campagnes_inventaire: {
        Row: {
          boutique_id: string | null
          commentaires: string | null
          created_at: string
          created_by: string | null
          date_debut: string
          date_fin: string | null
          id: string
          nom: string
          responsable_id: string | null
          statut: string
          updated_at: string
        }
        Insert: {
          boutique_id?: string | null
          commentaires?: string | null
          created_at?: string
          created_by?: string | null
          date_debut?: string
          date_fin?: string | null
          id?: string
          nom: string
          responsable_id?: string | null
          statut?: string
          updated_at?: string
        }
        Update: {
          boutique_id?: string | null
          commentaires?: string | null
          created_at?: string
          created_by?: string | null
          date_debut?: string
          date_fin?: string | null
          id?: string
          nom?: string
          responsable_id?: string | null
          statut?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "campagnes_inventaire_boutique_id_fkey"
            columns: ["boutique_id"]
            isOneToOne: false
            referencedRelation: "boutiques"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campagnes_inventaire_responsable_id_fkey"
            columns: ["responsable_id"]
            isOneToOne: false
            referencedRelation: "employes"
            referencedColumns: ["id"]
          },
        ]
      }
      categories: {
        Row: {
          created_at: string
          description: string | null
          id: string
          nom: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          nom: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          nom?: string
        }
        Relationships: []
      }
      clients: {
        Row: {
          adresse: string | null
          code_postal: string | null
          created_at: string
          date_naissance: string | null
          email: string | null
          id: string
          nom: string
          notes: string | null
          prenom: string | null
          statut: string
          telephone: string | null
          updated_at: string
          ville: string | null
        }
        Insert: {
          adresse?: string | null
          code_postal?: string | null
          created_at?: string
          date_naissance?: string | null
          email?: string | null
          id?: string
          nom: string
          notes?: string | null
          prenom?: string | null
          statut?: string
          telephone?: string | null
          updated_at?: string
          ville?: string | null
        }
        Update: {
          adresse?: string | null
          code_postal?: string | null
          created_at?: string
          date_naissance?: string | null
          email?: string | null
          id?: string
          nom?: string
          notes?: string | null
          prenom?: string | null
          statut?: string
          telephone?: string | null
          updated_at?: string
          ville?: string | null
        }
        Relationships: []
      }
      commande_details: {
        Row: {
          commande_id: string | null
          created_at: string
          id: string
          prix_total: number | null
          prix_unitaire: number
          produit_id: string | null
          quantite_commandee: number
          quantite_livree: number | null
        }
        Insert: {
          commande_id?: string | null
          created_at?: string
          id?: string
          prix_total?: number | null
          prix_unitaire: number
          produit_id?: string | null
          quantite_commandee: number
          quantite_livree?: number | null
        }
        Update: {
          commande_id?: string | null
          created_at?: string
          id?: string
          prix_total?: number | null
          prix_unitaire?: number
          produit_id?: string | null
          quantite_commandee?: number
          quantite_livree?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "commande_details_commande_id_fkey"
            columns: ["commande_id"]
            isOneToOne: false
            referencedRelation: "commandes_fournisseur"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "commande_details_produit_id_fkey"
            columns: ["produit_id"]
            isOneToOne: false
            referencedRelation: "produits"
            referencedColumns: ["id"]
          },
        ]
      }
      commandes_fournisseur: {
        Row: {
          boutique_id: string | null
          commentaires: string | null
          created_at: string
          date_commande: string
          date_livraison_prevue: string | null
          date_livraison_reelle: string | null
          fournisseur_id: string | null
          id: string
          montant_total: number
          montant_tva: number
          numero_commande: string
          responsable_id: string | null
          statut: string
          updated_at: string
        }
        Insert: {
          boutique_id?: string | null
          commentaires?: string | null
          created_at?: string
          date_commande?: string
          date_livraison_prevue?: string | null
          date_livraison_reelle?: string | null
          fournisseur_id?: string | null
          id?: string
          montant_total?: number
          montant_tva?: number
          numero_commande: string
          responsable_id?: string | null
          statut?: string
          updated_at?: string
        }
        Update: {
          boutique_id?: string | null
          commentaires?: string | null
          created_at?: string
          date_commande?: string
          date_livraison_prevue?: string | null
          date_livraison_reelle?: string | null
          fournisseur_id?: string | null
          id?: string
          montant_total?: number
          montant_tva?: number
          numero_commande?: string
          responsable_id?: string | null
          statut?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "commandes_fournisseur_boutique_id_fkey"
            columns: ["boutique_id"]
            isOneToOne: false
            referencedRelation: "boutiques"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "commandes_fournisseur_fournisseur_id_fkey"
            columns: ["fournisseur_id"]
            isOneToOne: false
            referencedRelation: "fournisseurs"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "commandes_fournisseur_responsable_id_fkey"
            columns: ["responsable_id"]
            isOneToOne: false
            referencedRelation: "employes"
            referencedColumns: ["id"]
          },
        ]
      }
      employes: {
        Row: {
          boutique_id: string | null
          created_at: string
          date_embauche: string
          email: string
          id: string
          nom: string
          poste: string
          prenom: string
          salaire: number | null
          statut: string
          telephone: string | null
          updated_at: string
        }
        Insert: {
          boutique_id?: string | null
          created_at?: string
          date_embauche?: string
          email: string
          id?: string
          nom: string
          poste: string
          prenom: string
          salaire?: number | null
          statut?: string
          telephone?: string | null
          updated_at?: string
        }
        Update: {
          boutique_id?: string | null
          created_at?: string
          date_embauche?: string
          email?: string
          id?: string
          nom?: string
          poste?: string
          prenom?: string
          salaire?: number | null
          statut?: string
          telephone?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "employes_boutique_id_fkey"
            columns: ["boutique_id"]
            isOneToOne: false
            referencedRelation: "boutiques"
            referencedColumns: ["id"]
          },
        ]
      }
      fournisseurs: {
        Row: {
          adresse: string | null
          conditions_paiement: string | null
          contact_nom: string | null
          created_at: string
          delai_livraison: number | null
          email: string | null
          id: string
          nom: string
          statut: string
          telephone: string | null
          updated_at: string
        }
        Insert: {
          adresse?: string | null
          conditions_paiement?: string | null
          contact_nom?: string | null
          created_at?: string
          delai_livraison?: number | null
          email?: string | null
          id?: string
          nom: string
          statut?: string
          telephone?: string | null
          updated_at?: string
        }
        Update: {
          adresse?: string | null
          conditions_paiement?: string | null
          contact_nom?: string | null
          created_at?: string
          delai_livraison?: number | null
          email?: string | null
          id?: string
          nom?: string
          statut?: string
          telephone?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      inventaire_details: {
        Row: {
          campagne_id: string | null
          commentaire: string | null
          compte_par: string | null
          created_at: string
          ecart: number | null
          id: string
          produit_id: string | null
          quantite_physique: number | null
          quantite_theorique: number
          stock_id: string | null
          updated_at: string
          valide: boolean | null
        }
        Insert: {
          campagne_id?: string | null
          commentaire?: string | null
          compte_par?: string | null
          created_at?: string
          ecart?: number | null
          id?: string
          produit_id?: string | null
          quantite_physique?: number | null
          quantite_theorique?: number
          stock_id?: string | null
          updated_at?: string
          valide?: boolean | null
        }
        Update: {
          campagne_id?: string | null
          commentaire?: string | null
          compte_par?: string | null
          created_at?: string
          ecart?: number | null
          id?: string
          produit_id?: string | null
          quantite_physique?: number | null
          quantite_theorique?: number
          stock_id?: string | null
          updated_at?: string
          valide?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "inventaire_details_campagne_id_fkey"
            columns: ["campagne_id"]
            isOneToOne: false
            referencedRelation: "campagnes_inventaire"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventaire_details_compte_par_fkey"
            columns: ["compte_par"]
            isOneToOne: false
            referencedRelation: "employes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventaire_details_produit_id_fkey"
            columns: ["produit_id"]
            isOneToOne: false
            referencedRelation: "produits"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventaire_details_stock_id_fkey"
            columns: ["stock_id"]
            isOneToOne: false
            referencedRelation: "stocks"
            referencedColumns: ["id"]
          },
        ]
      }
      mouvements_stock: {
        Row: {
          boutique_id: string | null
          created_at: string
          created_by: string | null
          employe_id: string | null
          id: string
          motif: string | null
          produit_id: string | null
          quantite_apres: number
          quantite_avant: number
          quantite_mouvement: number
          reference_id: string | null
          reference_type: string | null
          type_mouvement: string
        }
        Insert: {
          boutique_id?: string | null
          created_at?: string
          created_by?: string | null
          employe_id?: string | null
          id?: string
          motif?: string | null
          produit_id?: string | null
          quantite_apres: number
          quantite_avant: number
          quantite_mouvement: number
          reference_id?: string | null
          reference_type?: string | null
          type_mouvement: string
        }
        Update: {
          boutique_id?: string | null
          created_at?: string
          created_by?: string | null
          employe_id?: string | null
          id?: string
          motif?: string | null
          produit_id?: string | null
          quantite_apres?: number
          quantite_avant?: number
          quantite_mouvement?: number
          reference_id?: string | null
          reference_type?: string | null
          type_mouvement?: string
        }
        Relationships: [
          {
            foreignKeyName: "mouvements_stock_boutique_id_fkey"
            columns: ["boutique_id"]
            isOneToOne: false
            referencedRelation: "boutiques"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "mouvements_stock_employe_id_fkey"
            columns: ["employe_id"]
            isOneToOne: false
            referencedRelation: "employes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "mouvements_stock_produit_id_fkey"
            columns: ["produit_id"]
            isOneToOne: false
            referencedRelation: "produits"
            referencedColumns: ["id"]
          },
        ]
      }
      pin_sessions: {
        Row: {
          created_at: string
          expires_at: string
          id: string
          pin_hash: string
          user_profile_id: string
        }
        Insert: {
          created_at?: string
          expires_at?: string
          id?: string
          pin_hash: string
          user_profile_id: string
        }
        Update: {
          created_at?: string
          expires_at?: string
          id?: string
          pin_hash?: string
          user_profile_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "pin_sessions_user_profile_id_fkey"
            columns: ["user_profile_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      produits: {
        Row: {
          categorie_id: string | null
          code_produit: string
          couleur: string | null
          created_at: string
          description: string | null
          etat: string
          id: string
          imei: string | null
          marque: string
          modele: string
          nom: string
          prix_achat: number
          prix_vente: number
          stockage: string | null
          updated_at: string
        }
        Insert: {
          categorie_id?: string | null
          code_produit: string
          couleur?: string | null
          created_at?: string
          description?: string | null
          etat?: string
          id?: string
          imei?: string | null
          marque: string
          modele: string
          nom: string
          prix_achat: number
          prix_vente: number
          stockage?: string | null
          updated_at?: string
        }
        Update: {
          categorie_id?: string | null
          code_produit?: string
          couleur?: string | null
          created_at?: string
          description?: string | null
          etat?: string
          id?: string
          imei?: string | null
          marque?: string
          modele?: string
          nom?: string
          prix_achat?: number
          prix_vente?: number
          stockage?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "produits_categorie_id_fkey"
            columns: ["categorie_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      role_permissions: {
        Row: {
          action: string
          id: string
          resource: string
          role: Database["public"]["Enums"]["user_role"]
        }
        Insert: {
          action: string
          id?: string
          resource: string
          role: Database["public"]["Enums"]["user_role"]
        }
        Update: {
          action?: string
          id?: string
          resource?: string
          role?: Database["public"]["Enums"]["user_role"]
        }
        Relationships: []
      }
      stocks: {
        Row: {
          boutique_id: string | null
          created_at: string
          emplacement: string | null
          id: string
          produit_id: string | null
          quantite: number
          seuil_alerte: number
          updated_at: string
        }
        Insert: {
          boutique_id?: string | null
          created_at?: string
          emplacement?: string | null
          id?: string
          produit_id?: string | null
          quantite?: number
          seuil_alerte?: number
          updated_at?: string
        }
        Update: {
          boutique_id?: string | null
          created_at?: string
          emplacement?: string | null
          id?: string
          produit_id?: string | null
          quantite?: number
          seuil_alerte?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "stocks_boutique_id_fkey"
            columns: ["boutique_id"]
            isOneToOne: false
            referencedRelation: "boutiques"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "stocks_produit_id_fkey"
            columns: ["produit_id"]
            isOneToOne: false
            referencedRelation: "produits"
            referencedColumns: ["id"]
          },
        ]
      }
      transfert_details: {
        Row: {
          created_at: string
          id: string
          produit_id: string | null
          quantite: number
          transfert_id: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          produit_id?: string | null
          quantite: number
          transfert_id?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          produit_id?: string | null
          quantite?: number
          transfert_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "transfert_details_produit_id_fkey"
            columns: ["produit_id"]
            isOneToOne: false
            referencedRelation: "produits"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "transfert_details_transfert_id_fkey"
            columns: ["transfert_id"]
            isOneToOne: false
            referencedRelation: "transferts"
            referencedColumns: ["id"]
          },
        ]
      }
      transferts: {
        Row: {
          boutique_destination_id: string | null
          boutique_source_id: string | null
          commentaires: string | null
          created_at: string
          date_expedition: string | null
          date_reception: string | null
          employe_expediteur_id: string | null
          employe_recepteur_id: string | null
          id: string
          statut: string
          updated_at: string
        }
        Insert: {
          boutique_destination_id?: string | null
          boutique_source_id?: string | null
          commentaires?: string | null
          created_at?: string
          date_expedition?: string | null
          date_reception?: string | null
          employe_expediteur_id?: string | null
          employe_recepteur_id?: string | null
          id?: string
          statut?: string
          updated_at?: string
        }
        Update: {
          boutique_destination_id?: string | null
          boutique_source_id?: string | null
          commentaires?: string | null
          created_at?: string
          date_expedition?: string | null
          date_reception?: string | null
          employe_expediteur_id?: string | null
          employe_recepteur_id?: string | null
          id?: string
          statut?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "transferts_boutique_destination_id_fkey"
            columns: ["boutique_destination_id"]
            isOneToOne: false
            referencedRelation: "boutiques"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "transferts_boutique_source_id_fkey"
            columns: ["boutique_source_id"]
            isOneToOne: false
            referencedRelation: "boutiques"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "transferts_employe_expediteur_id_fkey"
            columns: ["employe_expediteur_id"]
            isOneToOne: false
            referencedRelation: "employes"
            referencedColumns: ["id"]
          },
        ]
      }
      user_profiles: {
        Row: {
          created_at: string
          created_by: string | null
          employe_id: string | null
          id: string
          is_active: boolean
          last_login: string | null
          pin_code: string | null
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          employe_id?: string | null
          id?: string
          is_active?: boolean
          last_login?: string | null
          pin_code?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          created_by?: string | null
          employe_id?: string | null
          id?: string
          is_active?: boolean
          last_login?: string | null
          pin_code?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_profiles_employe_id_fkey"
            columns: ["employe_id"]
            isOneToOne: false
            referencedRelation: "employes"
            referencedColumns: ["id"]
          },
        ]
      }
      vente_details: {
        Row: {
          created_at: string
          id: string
          prix_unitaire: number
          produit_id: string | null
          quantite: number
          remise: number | null
          sous_total: number
          vente_id: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          prix_unitaire: number
          produit_id?: string | null
          quantite: number
          remise?: number | null
          sous_total: number
          vente_id?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          prix_unitaire?: number
          produit_id?: string | null
          quantite?: number
          remise?: number | null
          sous_total?: number
          vente_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "vente_details_produit_id_fkey"
            columns: ["produit_id"]
            isOneToOne: false
            referencedRelation: "produits"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "vente_details_vente_id_fkey"
            columns: ["vente_id"]
            isOneToOne: false
            referencedRelation: "ventes"
            referencedColumns: ["id"]
          },
        ]
      }
      ventes: {
        Row: {
          boutique_id: string | null
          client_email: string | null
          client_id: string | null
          client_nom: string | null
          client_telephone: string | null
          created_at: string
          date_vente: string
          employe_id: string | null
          id: string
          mode_paiement: string
          montant_total: number
          montant_tva: number
          numero_facture: string
          statut: string
          updated_at: string
        }
        Insert: {
          boutique_id?: string | null
          client_email?: string | null
          client_id?: string | null
          client_nom?: string | null
          client_telephone?: string | null
          created_at?: string
          date_vente?: string
          employe_id?: string | null
          id?: string
          mode_paiement: string
          montant_total: number
          montant_tva?: number
          numero_facture: string
          statut?: string
          updated_at?: string
        }
        Update: {
          boutique_id?: string | null
          client_email?: string | null
          client_id?: string | null
          client_nom?: string | null
          client_telephone?: string | null
          created_at?: string
          date_vente?: string
          employe_id?: string | null
          id?: string
          mode_paiement?: string
          montant_total?: number
          montant_tva?: number
          numero_facture?: string
          statut?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "ventes_boutique_id_fkey"
            columns: ["boutique_id"]
            isOneToOne: false
            referencedRelation: "boutiques"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ventes_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ventes_employe_id_fkey"
            columns: ["employe_id"]
            isOneToOne: false
            referencedRelation: "employes"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      authenticate_with_pin: {
        Args: { _pin: string }
        Returns: Json
      }
      authenticate_with_pin_secure: {
        Args: { _pin: string; _user_agent?: string; _user_ip?: unknown }
        Returns: Json
      }
      cleanup_old_audit_logs: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_user_with_profile: {
        Args: {
          email: string
          password: string
          user_role?: Database["public"]["Enums"]["user_role"]
          employe_id?: string
          pin_code?: string
        }
        Returns: Json
      }
      generate_numero_facture: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      generate_user_pin: {
        Args: { target_user_id: string }
        Returns: string
      }
      generate_user_pin_secure: {
        Args: { target_user_id: string }
        Returns: string
      }
      get_user_management_data: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          user_id: string
          role: Database["public"]["Enums"]["user_role"]
          employe_id: string
          is_active: boolean
          has_pin: boolean
          created_at: string
          updated_at: string
          last_login: string
          employe_nom: string
          employe_prenom: string
          employe_email: string
          employe_telephone: string
          employe_poste: string
          employe_statut: string
          boutique_id: string
          boutique_nom: string
          created_by_id: string
          created_by_nom: string
          created_by_prenom: string
        }[]
      }
      get_user_profile: {
        Args: { _user_id: string }
        Returns: {
          profile_id: string
          employe_id: string
          role: Database["public"]["Enums"]["user_role"]
          is_active: boolean
          employe_nom: string
          employe_prenom: string
          boutique_id: string
          boutique_nom: string
        }[]
      }
      has_permission: {
        Args: { _user_id: string; _resource: string; _action: string }
        Returns: boolean
      }
      update_user_profile: {
        Args: {
          target_user_id: string
          new_role?: Database["public"]["Enums"]["user_role"]
          new_is_active?: boolean
          new_employe_id?: string
        }
        Returns: Json
      }
    }
    Enums: {
      user_role:
        | "admin_system"
        | "proprietaire"
        | "manager"
        | "vendeur"
        | "caissier"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      user_role: [
        "admin_system",
        "proprietaire",
        "manager",
        "vendeur",
        "caissier",
      ],
    },
  },
} as const
