-- Phase 4: Optimisation Performance - Index de base de données (correction)

-- Index pour les requêtes de ventes par boutique et date (très fréquentes)
CREATE INDEX IF NOT EXISTS idx_ventes_boutique_date 
ON public.ventes(boutique_id, date_vente DESC);

-- Index pour les requêtes de ventes par numéro de facture (recherches)
CREATE INDEX IF NOT EXISTS idx_ventes_numero_facture 
ON public.ventes(numero_facture);

-- Index pour les requêtes de stocks par boutique et seuil (alertes stock bas)
CREATE INDEX IF NOT EXISTS idx_stocks_boutique_seuil 
ON public.stocks(boutique_id, quantite, seuil_alerte);

-- Index pour les requêtes de stocks par produit (gestion stock)
CREATE INDEX IF NOT EXISTS idx_stocks_produit 
ON public.stocks(produit_id);

-- Index pour les alertes non lues par boutique (notifications)
CREATE INDEX IF NOT EXISTS idx_alertes_boutique_lu 
ON public.alertes(boutique_id, lu, created_at DESC);

-- Index pour les alertes par type et niveau (filtrage)
CREATE INDEX IF NOT EXISTS idx_alertes_type_niveau 
ON public.alertes(type, niveau, created_at DESC);

-- Index pour les transferts par boutique source/destination (très important)
CREATE INDEX IF NOT EXISTS idx_transferts_boutiques 
ON public.transferts(boutique_source_id, boutique_destination_id, created_at DESC);

-- Index pour les transferts par statut (filtrage fréquent)
CREATE INDEX IF NOT EXISTS idx_transferts_statut 
ON public.transferts(statut, created_at DESC);

-- Index pour les employés par boutique et statut (gestion RH)
CREATE INDEX IF NOT EXISTS idx_employes_boutique_statut 
ON public.employes(boutique_id, statut);

-- Index pour les produits par catégorie (catalogues)
CREATE INDEX IF NOT EXISTS idx_produits_categorie 
ON public.produits(categorie_id, nom);

-- Index pour les produits par code produit (recherches rapides)
CREATE INDEX IF NOT EXISTS idx_produits_code 
ON public.produits(code_produit);

-- Index composite pour les détails de vente (rapports)
CREATE INDEX IF NOT EXISTS idx_vente_details_vente_produit 
ON public.vente_details(vente_id, produit_id);

-- Index composite pour les détails de transfert (logistique)
CREATE INDEX IF NOT EXISTS idx_transfert_details_transfert_produit 
ON public.transfert_details(transfert_id, produit_id);