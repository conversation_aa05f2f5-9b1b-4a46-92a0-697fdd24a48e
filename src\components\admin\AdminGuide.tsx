import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Users, 
  Mail, 
  Shield, 
  TestTube,
  CheckCircle,
  AlertCircle,
  ExternalLink,
  Settings
} from 'lucide-react';

export const AdminGuide: React.FC = () => {
  const configSteps = [
    {
      id: 1,
      title: "Créer des comptes utilisateurs",
      description: "Utilisez le panel de gestion des utilisateurs pour créer de vrais comptes",
      icon: Users,
      status: "available",
      action: "Créer maintenant",
      path: "/admin"
    },
    {
      id: 2,
      title: "Configurer l'authentification email",
      description: "Configurez les emails d'authentification dans Supabase",
      icon: Mail,
      status: "manual",
      action: "Guide Supabase",
      external: true
    },
    {
      id: 3,
      title: "Personnaliser les rôles",
      description: "Ajustez les permissions selon vos besoins métier",
      icon: Shield,
      status: "manual",
      action: "Voir la documentation",
      external: true
    },
    {
      id: 4,
      title: "Tester les fonctionnalités",
      description: "Vérifiez les ventes, stocks, transferts et autres modules",
      icon: TestTube,
      status: "ready",
      action: "Commencer les tests",
      path: "/"
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "available":
        return <Badge variant="default" className="gap-1"><CheckCircle className="w-3 h-3" /> Disponible</Badge>;
      case "manual":
        return <Badge variant="secondary" className="gap-1"><Settings className="w-3 h-3" /> Configuration manuelle</Badge>;
      case "ready":
        return <Badge variant="outline" className="gap-1"><TestTube className="w-3 h-3" /> Prêt pour test</Badge>;
      default:
        return <Badge variant="destructive" className="gap-1"><AlertCircle className="w-3 h-3" /> En attente</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Guide de Configuration
          </CardTitle>
          <CardDescription>
            Étapes pour configurer complètement votre système de gestion
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Votre système est déjà fonctionnel ! Ces étapes vous permettront de le personnaliser et d'optimiser son utilisation.
            </AlertDescription>
          </Alert>

          <div className="grid gap-4">
            {configSteps.map((step) => {
              const Icon = step.icon;
              return (
                <Card key={step.id} className="relative">
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-primary/10 rounded-lg">
                          <Icon className="h-5 w-5 text-primary" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{step.title}</CardTitle>
                          <CardDescription>{step.description}</CardDescription>
                        </div>
                      </div>
                      {getStatusBadge(step.status)}
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="flex justify-end">
                      <Button 
                        variant={step.status === "available" ? "default" : "outline"}
                        size="sm"
                        className="gap-2"
                        onClick={() => {
                          if (step.path) {
                            window.location.href = step.path;
                          } else if (step.external) {
                            // Ici on pourrait ouvrir des liens vers la documentation
                            console.log(`Ouvrir guide pour: ${step.title}`);
                          }
                        }}
                      >
                        {step.action}
                        {step.external && <ExternalLink className="w-4 h-4" />}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Configuration Email Supabase</CardTitle>
          <CardDescription>
            Instructions pour configurer l'authentification par email
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="prose prose-sm max-w-none">
            <h4 className="font-semibold">Étapes de configuration :</h4>
            <ol className="list-decimal list-inside space-y-2 text-sm text-muted-foreground">
              <li>Accédez à votre tableau de bord Supabase</li>
              <li>Allez dans Authentication → Settings → Auth Providers</li>
              <li>Configurez les providers souhaités (Google, GitHub, etc.)</li>
              <li>Dans Authentication → URL Configuration, définissez vos URLs de redirection</li>
              <li>Testez la connexion avec de nouveaux utilisateurs</li>
            </ol>
          </div>
          
          <Alert>
            <Mail className="h-4 w-4" />
            <AlertDescription>
              N'oubliez pas de désactiver "Confirm email" dans les paramètres pour accélérer les tests de développement.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Système de Permissions</CardTitle>
          <CardDescription>
            Comprendre les rôles et permissions actuels
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h4 className="font-semibold text-sm">Rôles disponibles :</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between p-2 bg-muted rounded">
                  <span className="text-sm">Admin Système</span>
                  <Badge variant="destructive">Accès complet</Badge>
                </div>
                <div className="flex items-center justify-between p-2 bg-muted rounded">
                  <span className="text-sm">Propriétaire</span>
                  <Badge variant="default">Gestion boutiques</Badge>
                </div>
                <div className="flex items-center justify-between p-2 bg-muted rounded">
                  <span className="text-sm">Manager</span>
                  <Badge variant="secondary">Supervision</Badge>
                </div>
                <div className="flex items-center justify-between p-2 bg-muted rounded">
                  <span className="text-sm">Vendeur</span>
                  <Badge variant="outline">Ventes & Stocks</Badge>
                </div>
                <div className="flex items-center justify-between p-2 bg-muted rounded">
                  <span className="text-sm">Caissier</span>
                  <Badge variant="outline">Ventes uniquement</Badge>
                </div>
              </div>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-semibold text-sm">Ressources protégées :</h4>
              <div className="text-sm text-muted-foreground space-y-1">
                <div>• Boutiques & Employés</div>
                <div>• Produits & Catégories</div>
                <div>• Stocks & Inventaires</div>
                <div>• Ventes & Transferts</div>
                <div>• Administration système</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};