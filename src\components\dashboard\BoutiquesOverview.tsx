import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Button } from "@/components/ui/button";
import { 
  Store, 
  Package, 
  Banknote, 
  Users, 
  ArrowRight,
  AlertCircle,
  CheckCircle,
  MapPin
} from "lucide-react";
import { useBoutiques } from "@/hooks/useBoutiques";
import { useStocks } from "@/hooks/useStocks";
import { useVentes } from "@/hooks/useVentes";
import { useEmployes } from "@/hooks/useEmployes";
import { formatCurrency, getCurrentIvorianTime } from "@/lib/formatters";

export const BoutiquesOverview = () => {
  const { data: boutiques, isLoading, error } = useBoutiques();
  const { data: stocks, isLoading: stocksLoading } = useStocks();
  const { data: ventes, isLoading: ventesLoading } = useVentes();
  const { data: employes } = useEmployes();

  if (isLoading || stocksLoading || ventesLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Store className="h-5 w-5 text-primary" />
            Vue d'ensemble des Boutiques
          </CardTitle>
        </CardHeader>
        <CardContent>
          <LoadingSpinner size="lg" text="Chargement des données des boutiques..." />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Store className="h-5 w-5 text-primary" />
            Vue d'ensemble des Boutiques
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4 text-destructive">
            <AlertCircle className="h-8 w-8 mx-auto mb-2" />
            <p>Erreur lors du chargement des boutiques</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getBoutiqueStats = (boutiqueId: string) => {
    const boutiqueStocks = stocks?.filter(s => s.boutique_id === boutiqueId) ?? [];
    const boutiqueVentes = ventes?.filter(v => v.boutique_id === boutiqueId) ?? [];
    const boutiqueEmployes = employes?.filter(e => e.boutique_id === boutiqueId && e.statut === 'actif') ?? [];
    const lowStock = boutiqueStocks.filter(s => s.quantite <= s.seuil_alerte).length;
    const totalStock = boutiqueStocks.reduce((sum, s) => sum + s.quantite, 0);
    const todayVentes = boutiqueVentes
      .filter(v => new Date(v.date_vente).toDateString() === getCurrentIvorianTime().toDateString())
      .reduce((sum, v) => sum + v.montant_total, 0);

    return { totalStock, todayVentes, lowStock, employeCount: boutiqueEmployes.length };
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Store className="h-5 w-5 text-primary" />
          Vue d'ensemble des Boutiques
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {boutiques && boutiques.length > 0 ? boutiques.map((boutique) => {
            const stats = getBoutiqueStats(boutique.id);
            return (
              <div
                key={boutique.id}
                className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-center gap-4">
                  <div className={`h-10 w-10 rounded-lg flex items-center justify-center ${
                    boutique.type === 'centrale' 
                      ? 'bg-primary text-primary-foreground' 
                      : 'bg-secondary text-secondary-foreground'
                  }`}>
                    <Store className="h-5 w-5" />
                  </div>
                  
                  <div>
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold">{boutique.nom}</h3>
                      {boutique.type === 'centrale' && (
                        <Badge variant="default" className="text-xs">
                          CENTRALE
                        </Badge>
                      )}
                      <Badge 
                        variant={boutique.statut === 'active' ? 'default' : 'secondary'}
                        className="text-xs"
                      >
                        {boutique.statut === 'active' ? (
                          <>
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Active
                          </>
                        ) : (
                          <>
                            <AlertCircle className="h-3 w-3 mr-1" />
                            Maintenance
                          </>
                        )}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <MapPin className="h-3 w-3" />
                      {boutique.adresse}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-6 text-sm">
                  <div className="text-center">
                    <div className="flex items-center gap-1 text-muted-foreground">
                      <Package className="h-3 w-3" />
                      Stock
                    </div>
                    <div className="font-semibold">{stats.totalStock}</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="flex items-center gap-1 text-muted-foreground">
                      <Banknote className="h-3 w-3" />
                      Ventes/J
                    </div>
                    <div className="font-semibold">{formatCurrency(stats.todayVentes)}</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="flex items-center gap-1 text-muted-foreground">
                      <Users className="h-3 w-3" />
                      Employés
                    </div>
                    <div className="font-semibold">{stats.employeCount}</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-muted-foreground text-xs">Stock bas</div>
                    <Badge 
                      variant={stats.lowStock > 10 ? "destructive" : stats.lowStock > 5 ? "outline" : "secondary"}
                      className="text-xs"
                    >
                      {stats.lowStock}
                    </Badge>
                  </div>
                  
                  <Button variant="ghost" size="sm">
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            );
          }) : (
            <div className="text-center py-8 text-muted-foreground">
              <Store className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Aucune boutique trouvée</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};