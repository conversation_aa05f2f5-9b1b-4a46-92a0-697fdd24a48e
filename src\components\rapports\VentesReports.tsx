import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { 
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { 
  TrendingUp, 
  DollarSign, 
  ShoppingCart, 
  Users,
  Download,
  Eye
} from 'lucide-react';
import { useVentesReports } from '@/hooks/useVentesReports';
import { formatCurrency, formatNumber } from '@/lib/formatters';
import { useIsMobile } from '@/hooks/use-mobile';

interface VentesReportsProps {
  filters: {
    dateDebut: string;
    dateFin: string;
    boutiqueId: string;
    employeId: string;
    categorieId: string;
  };
}

export const VentesReports = ({ filters }: VentesReportsProps) => {
  const { 
    data: ventesData, 
    isLoading,
    stats,
    evolutionCA,
    topProduits,
    repartitionMoyensPaiement
  } = useVentesReports(filters);
  const isMobile = useIsMobile();

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className={`grid gap-4 ${isMobile ? 'grid-cols-2' : 'grid-cols-1 md:grid-cols-4'}`}>
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className={isMobile ? "p-4" : "p-6"}>
                <Skeleton className="h-8 w-full mb-2" />
                <Skeleton className="h-4 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>
        <div className={`grid gap-6 ${isMobile ? 'grid-cols-1' : 'grid-cols-1 lg:grid-cols-2'}`}>
          <Skeleton className={isMobile ? "h-48" : "h-80"} />
          <Skeleton className={isMobile ? "h-48" : "h-80"} />
        </div>
      </div>
    );
  }

  const COLORS = ['hsl(var(--primary))', 'hsl(var(--secondary))', 'hsl(var(--accent))', 'hsl(var(--muted))'];

  return (
    <div className="space-y-6">
      {/* KPIs principaux */}
      <div className={`grid gap-4 ${isMobile ? 'grid-cols-2' : 'grid-cols-1 md:grid-cols-4'}`}>
        <Card>
          <CardContent className={isMobile ? "p-4" : "p-6"}>
            <div className="flex items-center justify-between">
              <div className="min-w-0 flex-1">
                <p className={`font-medium text-muted-foreground ${isMobile ? 'text-xs' : 'text-sm'}`}>
                  {isMobile ? 'CA Total' : 'Chiffre d\'Affaires'}
                </p>
                <p className={`font-bold ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                  {isMobile ? formatCurrency(stats?.totalCA || 0).replace(' FCFA', '') : formatCurrency(stats?.totalCA || 0)}
                </p>
                <p className={`text-green-600 flex items-center gap-1 ${isMobile ? 'text-xs' : 'text-xs'}`}>
                  <TrendingUp className="h-3 w-3" />
                  +{stats?.croissanceCA || 0}%{!isMobile && ' vs période précédente'}
                </p>
              </div>
              <DollarSign className={`text-primary ${isMobile ? 'h-6 w-6' : 'h-8 w-8'}`} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className={isMobile ? "p-4" : "p-6"}>
            <div className="flex items-center justify-between">
              <div className="min-w-0 flex-1">
                <p className={`font-medium text-muted-foreground ${isMobile ? 'text-xs' : 'text-sm'}`}>
                  {isMobile ? 'Ventes' : 'Nombre de Ventes'}
                </p>
                <p className={`font-bold ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                  {formatNumber(stats?.nombreVentes || 0)}
                </p>
                <p className={`text-blue-600 ${isMobile ? 'text-xs' : 'text-xs'}`}>
                  {stats?.ventesParJour || 0} {isMobile ? '/j' : 'ventes/jour'}
                </p>
              </div>
              <ShoppingCart className={`text-primary ${isMobile ? 'h-6 w-6' : 'h-8 w-8'}`} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className={isMobile ? "p-4" : "p-6"}>
            <div className="flex items-center justify-between">
              <div className="min-w-0 flex-1">
                <p className={`font-medium text-muted-foreground ${isMobile ? 'text-xs' : 'text-sm'}`}>
                  {isMobile ? 'Panier Moy.' : 'Panier Moyen'}
                </p>
                <p className={`font-bold ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                  {isMobile ? formatCurrency(stats?.panierMoyen || 0).replace(' FCFA', '') : formatCurrency(stats?.panierMoyen || 0)}
                </p>
                <p className={`text-purple-600 ${isMobile ? 'text-xs' : 'text-xs'}`}>
                  +{stats?.croissancePanier || 0}%{!isMobile && ' vs période précédente'}
                </p>
              </div>
              <Users className={`text-primary ${isMobile ? 'h-6 w-6' : 'h-8 w-8'}`} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className={isMobile ? "p-4" : "p-6"}>
            <div className="flex items-center justify-between">
              <div className="min-w-0 flex-1">
                <p className={`font-medium text-muted-foreground ${isMobile ? 'text-xs' : 'text-sm'}`}>
                  {isMobile ? 'Conversion' : 'Taux de Conversion'}
                </p>
                <p className={`font-bold ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                  {stats?.tauxConversion || 0}%
                </p>
                <p className={`text-orange-600 ${isMobile ? 'text-xs' : 'text-xs'}`}>
                  {isMobile ? 'Prospects' : 'Prospects convertis'}
                </p>
              </div>
              <TrendingUp className={`text-primary ${isMobile ? 'h-6 w-6' : 'h-8 w-8'}`} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Graphiques principaux */}
      <div className={`grid gap-6 ${isMobile ? 'grid-cols-1' : 'grid-cols-1 lg:grid-cols-2'}`}>
        {/* Évolution du CA */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className={isMobile ? 'text-base' : 'text-lg'}>
              {isMobile ? 'Évolution CA' : 'Évolution du Chiffre d\'Affaires'}
            </CardTitle>
            {!isMobile && (
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            )}
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={isMobile ? 200 : 300}>
              <LineChart data={evolutionCA}>
                <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                <XAxis 
                  dataKey="date" 
                  stroke="hsl(var(--muted-foreground))"
                  fontSize={isMobile ? 10 : 12}
                  angle={isMobile ? -45 : 0}
                  textAnchor={isMobile ? "end" : "middle"}
                />
                <YAxis 
                  stroke="hsl(var(--muted-foreground))"
                  fontSize={isMobile ? 10 : 12}
                  tickFormatter={(value) => isMobile ? `${(value / 1000).toFixed(0)}k` : formatCurrency(value)}
                />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'hsl(var(--background))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px'
                  }}
                  formatter={(value) => [formatCurrency(value as number), 'CA']}
                />
                <Line 
                  type="monotone" 
                  dataKey="ca" 
                  stroke="hsl(var(--primary))" 
                  strokeWidth={2}
                  dot={{ fill: 'hsl(var(--primary))', strokeWidth: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Répartition des moyens de paiement */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className={isMobile ? 'text-base' : 'text-lg'}>
              {isMobile ? 'Paiements' : 'Moyens de Paiement'}
            </CardTitle>
            {!isMobile && (
              <Button variant="outline" size="sm">
                <Eye className="h-4 w-4 mr-2" />
                Détails
              </Button>
            )}
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={isMobile ? 200 : 300}>
              <PieChart>
                <Pie
                  data={repartitionMoyensPaiement}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={isMobile ? false : ({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={isMobile ? 60 : 80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {repartitionMoyensPaiement?.map((_, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'hsl(var(--background))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px'
                  }}
                  formatter={(value) => [formatCurrency(value as number), 'Montant']}
                />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Top produits */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className={isMobile ? 'text-base' : 'text-lg'}>
            {isMobile ? 'Top Produits' : 'Top 10 des Produits les Plus Vendus'}
          </CardTitle>
          {!isMobile && (
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export Excel
            </Button>
          )}
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={isMobile ? 300 : 400}>
            <BarChart data={topProduits} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
              <XAxis 
                type="number"
                stroke="hsl(var(--muted-foreground))"
                fontSize={isMobile ? 10 : 12}
                tickFormatter={(value) => isMobile ? `${(value / 1000).toFixed(0)}k` : formatCurrency(value)}
              />
              <YAxis 
                type="category"
                dataKey="nom"
                stroke="hsl(var(--muted-foreground))"
                fontSize={isMobile ? 9 : 12}
                width={isMobile ? 100 : 150}
              />
              <Tooltip 
                contentStyle={{
                  backgroundColor: 'hsl(var(--background))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '6px'
                }}
                formatter={(value, name) => [
                  name === 'ca' ? formatCurrency(value as number) : formatNumber(value as number),
                  name === 'ca' ? 'CA' : 'Quantité'
                ]}
              />
              <Bar dataKey="ca" fill="hsl(var(--primary))" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
};