import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, Save, Check, AlertTriangle, Loader2 } from 'lucide-react';
import { useInventaireDetails, useUpdateInventaireDetail, useValiderEcarts } from '@/hooks/useInventaire';
import { DataTable } from '@/components/ui/data-table';
import { InventaireStats } from './InventaireStats';

interface InventaireDetailProps {
  campagneId: string;
  onBack: () => void;
}

export const InventaireDetail = ({ campagneId, onBack }: InventaireDetailProps) => {
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [quantitePhysique, setQuantitePhysique] = useState<number>(0);
  const [commentaire, setCommentaire] = useState<string>('');

  const { data: details, isLoading } = useInventaireDetails(campagneId);
  const updateDetail = useUpdateInventaireDetail();
  const validerEcarts = useValiderEcarts();

  const handleEdit = (detail: any) => {
    setEditingItem(detail.id);
    setQuantitePhysique(detail.quantite_physique || detail.quantite_theorique);
    setCommentaire(detail.commentaire || '');
  };

  const handleSave = async () => {
    if (!editingItem) return;
    
    await updateDetail.mutateAsync({
      id: editingItem,
      quantite_physique: quantitePhysique,
      commentaire: commentaire,
    });
    
    setEditingItem(null);
  };

  const handleValiderInventaire = async () => {
    await validerEcarts.mutateAsync(campagneId);
    onBack();
  };

  const getEcartVariant = (ecart: number) => {
    if (ecart === 0) return 'default';
    if (ecart > 0) return 'secondary';
    return 'destructive';
  };

  const columns = [
    {
      key: 'produit',
      label: 'Produit',
      render: (detail: any) => (
        <div>
          <div className="font-medium">
            {detail.produits?.nom} - {detail.produits?.marque}
          </div>
          <div className="text-sm text-muted-foreground">
            {detail.produits?.code_produit}
          </div>
        </div>
      ),
    },
    {
      key: 'emplacement',
      label: 'Emplacement',
      render: (detail: any) => detail.stocks?.emplacement || 'Non défini',
    },
    {
      key: 'quantite_theorique',
      label: 'Qté Théorique',
      render: (detail: any) => (
        <Badge variant="outline">
          {detail.quantite_theorique}
        </Badge>
      ),
    },
    {
      key: 'quantite_physique',
      label: 'Qté Physique',
      render: (detail: any) => {
        if (editingItem === detail.id) {
          return (
            <Input
              type="number"
              value={quantitePhysique}
              onChange={(e) => setQuantitePhysique(Number(e.target.value))}
              className="w-20"
              min="0"
            />
          );
        }
        
        return detail.quantite_physique !== null ? (
          <Badge variant="secondary">
            {detail.quantite_physique}
          </Badge>
        ) : (
          <span className="text-muted-foreground">Non compté</span>
        );
      },
    },
    {
      key: 'ecart',
      label: 'Écart',
      render: (detail: any) => {
        if (detail.quantite_physique === null) return '-';
        
        const ecart = detail.quantite_physique - detail.quantite_theorique;
        return (
          <Badge variant={getEcartVariant(ecart)}>
            {ecart > 0 ? '+' : ''}{ecart}
          </Badge>
        );
      },
    },
    {
      key: 'commentaire',
      label: 'Commentaire',
      render: (detail: any) => {
        if (editingItem === detail.id) {
          return (
            <Textarea
              value={commentaire}
              onChange={(e) => setCommentaire(e.target.value)}
              placeholder="Commentaire optionnel..."
              rows={2}
              className="w-48"
            />
          );
        }
        
        return detail.commentaire || '-';
      },
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (detail: any) => {
        if (editingItem === detail.id) {
          return (
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={handleSave}
                disabled={updateDetail.isPending}
              >
                <Save className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setEditingItem(null)}
              >
                Annuler
              </Button>
            </div>
          );
        }
        
        return (
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleEdit(detail)}
            disabled={!!editingItem}
          >
            Compter
          </Button>
        );
      },
    },
  ];

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-10" />
          <Skeleton className="h-8 w-64" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-4 w-32" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center justify-between p-4 border rounded">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-48" />
                  <Skeleton className="h-3 w-32" />
                </div>
                <Skeleton className="h-8 w-20" />
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    );
  }

  const totalEcarts = details?.filter(d => 
    d.quantite_physique !== null && d.quantite_physique !== d.quantite_theorique
  ).length || 0;

  const articlesComptes = details?.filter(d => d.quantite_physique !== null).length || 0;
  const totalArticles = details?.length || 0;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Détails de l'inventaire</h1>
            <p className="text-muted-foreground">
              {articlesComptes} / {totalArticles} articles comptés
            </p>
          </div>
        </div>
        
        {totalEcarts > 0 && (
          <Button 
            onClick={handleValiderInventaire}
            disabled={validerEcarts.isPending}
            className="bg-gradient-primary hover:opacity-90"
          >
            {validerEcarts.isPending ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Check className="h-4 w-4 mr-2" />
            )}
            Valider les écarts ({totalEcarts})
          </Button>
        )}
      </div>

      {/* Statistiques de l'inventaire */}
      <InventaireStats details={details || []} />

      {totalEcarts > 0 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              <div>
                <p className="font-medium text-orange-900">
                  {totalEcarts} écart(s) détecté(s)
                </p>
                <p className="text-sm text-orange-700">
                  Vérifiez les quantités et validez pour appliquer les ajustements de stock.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-3">
          <DataTable
            data={details || []}
            columns={columns}
            title="Articles à inventorier"
            emptyMessage="Aucun article à inventorier"
          />
        </div>
      </div>
    </div>
  );
};