/**
 * Hook React pour la gestion des imprimantes thermiques
 */

import { useState, useEffect, useCallback } from 'react';
import { thermalReceiptService, ThermalPrinter } from '@/services/thermalReceiptService';
import { toast } from '@/hooks/use-toast';

export const useThermalPrinter = (boutiqueId?: string) => {
  const [printers, setPrinters] = useState<ThermalPrinter[]>([]);
  const [isDetecting, setIsDetecting] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isPrinting, setIsPrinting] = useState(false);

  // Détection automatique des imprimantes au démarrage
  useEffect(() => {
    detectPrinters();
  }, []);

  const detectPrinters = useCallback(async () => {
    setIsDetecting(true);
    try {
      const detectedPrinters = await thermalReceiptService.detectPrinters();
      setPrinters(detectedPrinters);
      
      toast({
        title: 'Détection terminée',
        description: `${detectedPrinters.length} imprimante(s) détectée(s)`,
      });
    } catch (error) {
      console.error('Erreur détection:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de détecter les imprimantes',
        variant: 'destructive',
      });
    } finally {
      setIsDetecting(false);
    }
  }, []);

  const connectPrinter = useCallback(async (printerId: string) => {
    setIsConnecting(true);
    try {
      const success = await thermalReceiptService.connectPrinter(printerId);
      if (success) {
        setPrinters(prev => prev.map(p => 
          p.id === printerId ? { ...p, connected: true } : p
        ));
        
        toast({
          title: 'Connexion réussie',
          description: 'Imprimante connectée avec succès',
        });
        
        return true;
      } else {
        toast({
          title: 'Échec de connexion',
          description: 'Impossible de se connecter à l\'imprimante',
          variant: 'destructive',
        });
        return false;
      }
    } catch (error) {
      console.error('Erreur connexion:', error);
      toast({
        title: 'Erreur',
        description: 'Erreur lors de la connexion',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsConnecting(false);
    }
  }, []);

  const disconnectPrinter = useCallback(async (printerId: string) => {
    try {
      await thermalReceiptService.disconnectPrinter(printerId);
      setPrinters(prev => prev.map(p => 
        p.id === printerId ? { ...p, connected: false } : p
      ));
      
      toast({
        title: 'Déconnexion',
        description: 'Imprimante déconnectée',
      });
    } catch (error) {
      console.error('Erreur déconnexion:', error);
    }
  }, []);

  const testPrint = useCallback(async (printerId: string) => {
    setIsPrinting(true);
    try {
      await thermalReceiptService.testPrint(printerId);
      toast({
        title: 'Test d\'impression',
        description: 'Reçu de test envoyé à l\'imprimante',
      });
      return true;
    } catch (error: any) {
      console.error('Erreur test:', error);
      toast({
        title: 'Erreur test',
        description: error.message || 'Échec du test d\'impression',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsPrinting(false);
    }
  }, []);

  const setDefaultPrinter = useCallback((printerId: string) => {
    const success = thermalReceiptService.setDefaultPrinter(printerId);
    if (success) {
      toast({
        title: 'Imprimante par défaut',
        description: 'Imprimante définie par défaut',
      });
    }
    return success;
  }, []);

  const updatePrinterWidth = useCallback((printerId: string, width: 57 | 58 | 80) => {
    setPrinters(prev => prev.map(p => 
      p.id === printerId ? { ...p, width } : p
    ));

    // Sauvegarder la préférence si boutiqueId disponible
    if (boutiqueId) {
      thermalReceiptService.savePrinterPreference(boutiqueId, printerId, width);
    }
  }, [boutiqueId]);

  const openCashDrawer = useCallback(async (printerId?: string) => {
    try {
      const success = await thermalReceiptService.openCashDrawer(printerId);
      if (success) {
        toast({
          title: 'Tiroir-caisse',
          description: 'Tiroir-caisse ouvert',
        });
      }
      return success;
    } catch (error) {
      console.error('Erreur ouverture tiroir:', error);
      return false;
    }
  }, []);

  // État dérivé
  const connectedPrinters = printers.filter(p => p.connected);
  const defaultPrinter = thermalReceiptService.getDefaultPrinter();
  const hasConnectedPrinter = connectedPrinters.length > 0;

  // Préférence pour la boutique actuelle
  const printerPreference = boutiqueId ? 
    thermalReceiptService.getPrinterPreference(boutiqueId) : null;

  return {
    // État
    printers,
    connectedPrinters,
    defaultPrinter,
    hasConnectedPrinter,
    printerPreference,
    
    // Indicateurs de chargement
    isDetecting,
    isConnecting,
    isPrinting,
    
    // Actions
    detectPrinters,
    connectPrinter,
    disconnectPrinter,
    testPrint,
    setDefaultPrinter,
    updatePrinterWidth,
    openCashDrawer,
    
    // Service direct (pour des usages avancés)
    thermalService: thermalReceiptService
  };
};