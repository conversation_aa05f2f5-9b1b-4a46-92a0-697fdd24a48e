import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

// Types d'erreurs spécifiques pour une meilleure gestion
interface VenteError {
  type: 'VALIDATION' | 'STOCK_INSUFFICIENT' | 'DATABASE' | 'NETWORK';
  message: string;
  details?: any;
}

// Utilitaire pour categoriser les erreurs
const categorizeError = (error: any): VenteError => {
  const errorMessage = error?.message || 'Erreur inconnue';
  
  if (errorMessage.includes('Stock insuffisant')) {
    return { type: 'STOCK_INSUFFICIENT', message: errorMessage };
  }
  if (errorMessage.includes('violates') || errorMessage.includes('invalid') || errorMessage.includes('Format')) {
    return { type: 'VALIDATION', message: 'Données invalides: ' + errorMessage };
  }
  if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
    return { type: 'NETWORK', message: 'Problème de connexion. Vérifiez votre réseau.' };
  }
  return { type: 'DATABASE', message: errorMessage, details: error };
};

export interface Vente {
  id: string;
  numero_facture: string;
  boutique_id: string;
  employe_id: string;
  client_id?: string;
  client_nom?: string;
  client_telephone?: string;
  client_email?: string;
  montant_total: number;
  montant_tva: number;
  mode_paiement: 'especes' | 'carte' | 'cheque' | 'virement' | 'mobile_money' | 'crypto';
  statut: 'validee' | 'annulee' | 'remboursee' | 'en_attente';
  date_vente: string;
  created_at: string;
  updated_at: string;
  boutiques?: { nom: string };
  employes?: { nom: string; prenom: string };
  clients?: { nom: string; prenom?: string; email?: string; telephone?: string };
}

export const useVentes = (boutiqueId?: string, dateDebut?: string, dateFin?: string) => {
  return useQuery({
    queryKey: ['ventes', boutiqueId, dateDebut, dateFin],
    queryFn: async () => {
      let query = supabase
        .from('ventes')
        .select(`
          *,
          boutiques:boutique_id(nom),
          employes:employe_id(nom, prenom),
          clients:client_id(nom, prenom, email, telephone)
        `);
      
      if (boutiqueId) {
        query = query.eq('boutique_id', boutiqueId);
      }
      
      if (dateDebut) {
        query = query.gte('date_vente', dateDebut);
      }
      
      if (dateFin) {
        query = query.lte('date_vente', dateFin + 'T23:59:59');
      }
      
      const { data, error } = await query.order('date_vente', { ascending: false });
      
      if (error) throw error;
      return data as Vente[];
    },
    // Ventes changent souvent, cache court mais pas trop pour éviter surcharge
    staleTime: 1 * 60 * 1000,  // 1 minute
    gcTime: 3 * 60 * 1000,     // 3 minutes
    enabled: true, // Toujours actif
  });
};

export const useCreateVente = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (venteData: {
      boutique_id: string;
      employe_id: string;
      client_id?: string;
      client_nom?: string;
      client_telephone?: string;
      client_email?: string;
      montant_total: number;
      montant_tva: number;
      mode_paiement: string;
      statut: string;
    }) => {
      const { data, error } = await supabase
        .from('ventes')
        .insert({
          ...venteData,
          numero_facture: 'TEMP' // Will be overridden by trigger
        })
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['ventes'] });
      toast({
        title: "Vente enregistrée",
        description: "La vente a été enregistrée avec succès."
      });
    },
    onError: (error: any) => {
      const categorizedError = categorizeError(error);
      console.error('Erreur création vente:', { error, type: categorizedError.type });
      
      toast({
        variant: "destructive",
        title: categorizedError.type === 'STOCK_INSUFFICIENT' ? "Stock insuffisant" : "Erreur",
        description: categorizedError.message
      });
    }
  });
};

export const useUpdateVente = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, ...vente }: { id: string } & Partial<Vente>) => {
      const { data, error } = await supabase
        .from('ventes')
        .update(vente)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['ventes'] });
      toast({
        title: "Vente mise à jour",
        description: "La vente a été mise à jour avec succès."
      });
    },
    onError: (error: any) => {
      const categorizedError = categorizeError(error);
      console.error('Erreur mise à jour vente:', { error, type: categorizedError.type });
      
      toast({
        variant: "destructive", 
        title: "Erreur de mise à jour",
        description: categorizedError.message
      });
    }
  });
};

export const useDeleteVente = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('ventes')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['ventes'] });
      toast({
        title: "Vente supprimée",
        description: "La vente a été supprimée avec succès."
      });
    },
    onError: (error: any) => {
      const categorizedError = categorizeError(error);
      console.error('Erreur suppression vente:', { error, type: categorizedError.type });
      
      toast({
        variant: "destructive",
        title: "Erreur de suppression", 
        description: categorizedError.message
      });
    }
  });
};