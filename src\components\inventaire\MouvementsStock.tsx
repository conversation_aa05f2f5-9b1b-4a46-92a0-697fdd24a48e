import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useMouvementsStock } from '@/hooks/useInventaire';
import { DataTable } from '@/components/ui/data-table';
import { ArrowUp, ArrowDown, RotateCcw, Package, TrendingUp, TrendingDown } from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

interface MouvementsStockProps {
  boutiqueId?: string;
}

export const MouvementsStock = ({ boutiqueId }: MouvementsStockProps) => {
  const [filters, setFilters] = useState({
    produitId: '',
    typeMouvement: 'all',
    dateDebut: '',
    dateFin: '',
  });

  const { data: mouvements, isLoading } = useMouvementsStock({
    boutiqueId,
    ...filters,
  });

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'entree':
      case 'transfert_entree':
        return <ArrowUp className="h-4 w-4 text-green-600" />;
      case 'sortie':
      case 'vente':
      case 'transfert_sortie':
        return <ArrowDown className="h-4 w-4 text-red-600" />;
      case 'ajustement':
      case 'inventaire':
        return <RotateCcw className="h-4 w-4 text-blue-600" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  const getTypeLabel = (type: string) => {
    const labels = {
      entree: 'Entrée',
      sortie: 'Sortie',
      ajustement: 'Ajustement',
      transfert_sortie: 'Transfert sortie',
      transfert_entree: 'Transfert entrée',
      vente: 'Vente',
      inventaire: 'Inventaire',
    };
    return labels[type as keyof typeof labels] || type;
  };

  const getTypeVariant = (type: string) => {
    switch (type) {
      case 'entree':
      case 'transfert_entree':
        return 'secondary' as const;
      case 'sortie':
      case 'vente':
      case 'transfert_sortie':
        return 'destructive' as const;
      case 'ajustement':
      case 'inventaire':
        return 'default' as const;
      default:
        return 'outline' as const;
    }
  };

  const columns = [
    {
      key: 'date',
      label: 'Date',
      render: (mouvement: any) => (
        <div className="text-sm">
          {format(new Date(mouvement.created_at), 'dd MMM yyyy', { locale: fr })}
          <div className="text-xs text-muted-foreground">
            {format(new Date(mouvement.created_at), 'HH:mm')}
          </div>
        </div>
      ),
    },
    {
      key: 'produit',
      label: 'Produit',
      render: (mouvement: any) => (
        <div>
          <div className="font-medium">
            {mouvement.produits?.nom} - {mouvement.produits?.marque}
          </div>
          <div className="text-sm text-muted-foreground">
            {mouvement.produits?.code_produit}
          </div>
        </div>
      ),
    },
    {
      key: 'boutique',
      label: 'Boutique',
      render: (mouvement: any) => mouvement.boutiques?.nom || '-',
    },
    {
      key: 'type',
      label: 'Type',
      render: (mouvement: any) => (
        <div className="flex items-center gap-2">
          {getTypeIcon(mouvement.type_mouvement)}
          <Badge variant={getTypeVariant(mouvement.type_mouvement)}>
            {getTypeLabel(mouvement.type_mouvement)}
          </Badge>
        </div>
      ),
    },
    {
      key: 'quantites',
      label: 'Quantités',
      render: (mouvement: any) => (
        <div className="text-sm">
          <div className="flex items-center gap-2">
            <span>{mouvement.quantite_avant}</span>
            <span>→</span>
            <span className="font-medium">{mouvement.quantite_apres}</span>
          </div>
          <div className="flex items-center gap-1 text-xs">
            {mouvement.quantite_mouvement > 0 ? (
              <TrendingUp className="h-3 w-3 text-green-600" />
            ) : (
              <TrendingDown className="h-3 w-3 text-red-600" />
            )}
            <span className={mouvement.quantite_mouvement > 0 ? 'text-green-600' : 'text-red-600'}>
              {mouvement.quantite_mouvement > 0 ? '+' : ''}{mouvement.quantite_mouvement}
            </span>
          </div>
        </div>
      ),
    },
    {
      key: 'motif',
      label: 'Motif',
      render: (mouvement: any) => mouvement.motif || '-',
    },
    {
      key: 'employe',
      label: 'Employé',
      render: (mouvement: any) => 
        mouvement.employes ? 
          `${mouvement.employes.prenom} ${mouvement.employes.nom}` : 
          '-',
    },
  ];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Filtres</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Type de mouvement</label>
              <Select 
                value={filters.typeMouvement} 
                onValueChange={(value) => setFilters(prev => ({ ...prev, typeMouvement: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Tous les types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les types</SelectItem>
                  <SelectItem value="entree">Entrée</SelectItem>
                  <SelectItem value="sortie">Sortie</SelectItem>
                  <SelectItem value="ajustement">Ajustement</SelectItem>
                  <SelectItem value="transfert_entree">Transfert entrée</SelectItem>
                  <SelectItem value="transfert_sortie">Transfert sortie</SelectItem>
                  <SelectItem value="vente">Vente</SelectItem>
                  <SelectItem value="inventaire">Inventaire</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">Date début</label>
              <Input
                type="date"
                value={filters.dateDebut}
                onChange={(e) => setFilters(prev => ({ ...prev, dateDebut: e.target.value }))}
              />
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">Date fin</label>
              <Input
                type="date"
                value={filters.dateFin}
                onChange={(e) => setFilters(prev => ({ ...prev, dateFin: e.target.value }))}
              />
            </div>
            
            <div className="flex items-end">
              <button
                onClick={() => setFilters({ produitId: '', typeMouvement: 'all', dateDebut: '', dateFin: '' })}
                className="text-sm text-muted-foreground hover:text-foreground"
              >
                Réinitialiser
              </button>
            </div>
          </div>
        </CardContent>
      </Card>

      <DataTable
        data={mouvements || []}
        columns={columns}
        title="Historique des mouvements"
        subtitle={`${mouvements?.length || 0} mouvement(s) trouvé(s)`}
        isLoading={isLoading}
        emptyMessage="Aucun mouvement de stock trouvé"
      />
    </div>
  );
};