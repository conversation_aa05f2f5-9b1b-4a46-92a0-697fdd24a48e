/**
 * Interface de configuration des imprimantes thermiques
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/hooks/use-toast';
import { 
  Printer, 
  Usb, 
  Bluetooth, 
  Wifi, 
  CheckCircle, 
  XCircle, 
  RefreshCw,
  Settings,
  TestTube
} from 'lucide-react';

import { thermalReceiptService, ThermalPrinter } from '@/services/thermalReceiptService';
import { ThermalTestTemplate } from '@/components/receipts/ThermalReceiptTemplate';

interface PrinterSettingsProps {
  boutiqueId?: string;
}

export const PrinterSettings: React.FC<PrinterSettingsProps> = ({ boutiqueId }) => {
  const [printers, setPrinters] = useState<ThermalPrinter[]>([]);
  const [loading, setLoading] = useState(false);
  const [connecting, setConnecting] = useState<string | null>(null);
  const [testing, setTesting] = useState<string | null>(null);
  const [selectedWidth, setSelectedWidth] = useState<{ [key: string]: 57 | 58 | 80 }>({});

  useEffect(() => {
    detectPrinters();
  }, []);

  const detectPrinters = async () => {
    setLoading(true);
    try {
      const detectedPrinters = await thermalReceiptService.detectPrinters();
      setPrinters(detectedPrinters);
      
      // Charger les préférences sauvegardées
      if (boutiqueId) {
        const preference = thermalReceiptService.getPrinterPreference(boutiqueId);
        if (preference) {
          setSelectedWidth({ [preference.printerId]: preference.width });
        }
      }
      
      toast({
        title: 'Détection terminée',
        description: `${detectedPrinters.length} imprimante(s) détectée(s)`,
      });
    } catch (error) {
      console.error('Erreur détection:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de détecter les imprimantes',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const connectPrinter = async (printerId: string) => {
    setConnecting(printerId);
    try {
      const success = await thermalReceiptService.connectPrinter(printerId);
      if (success) {
        setPrinters(prev => prev.map(p => 
          p.id === printerId ? { ...p, connected: true } : p
        ));
        toast({
          title: 'Connexion réussie',
          description: 'Imprimante connectée avec succès',
        });
      } else {
        toast({
          title: 'Échec de connexion',
          description: 'Impossible de se connecter à l\'imprimante',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Erreur connexion:', error);
      toast({
        title: 'Erreur',
        description: 'Erreur lors de la connexion',
        variant: 'destructive',
      });
    } finally {
      setConnecting(null);
    }
  };

  const disconnectPrinter = async (printerId: string) => {
    try {
      await thermalReceiptService.disconnectPrinter(printerId);
      setPrinters(prev => prev.map(p => 
        p.id === printerId ? { ...p, connected: false } : p
      ));
      toast({
        title: 'Déconnexion',
        description: 'Imprimante déconnectée',
      });
    } catch (error) {
      console.error('Erreur déconnexion:', error);
    }
  };

  const testPrint = async (printerId: string) => {
    setTesting(printerId);
    try {
      await thermalReceiptService.testPrint(printerId);
      toast({
        title: 'Test d\'impression',
        description: 'Reçu de test envoyé à l\'imprimante',
      });
    } catch (error) {
      console.error('Erreur test:', error);
      toast({
        title: 'Erreur test',
        description: 'Échec du test d\'impression',
        variant: 'destructive',
      });
    } finally {
      setTesting(null);
    }
  };

  const setDefaultPrinter = (printerId: string) => {
    const success = thermalReceiptService.setDefaultPrinter(printerId);
    if (success) {
      toast({
        title: 'Imprimante par défaut',
        description: 'Imprimante définie par défaut',
      });
    }
  };

  const updatePrinterWidth = (printerId: string, width: 57 | 58 | 80) => {
    setSelectedWidth(prev => ({ ...prev, [printerId]: width }));
    setPrinters(prev => prev.map(p => 
      p.id === printerId ? { ...p, width } : p
    ));

    // Sauvegarder la préférence si boutiqueId disponible
    if (boutiqueId) {
      thermalReceiptService.savePrinterPreference(boutiqueId, printerId, width);
    }
  };

  const getConnectionIcon = (type: 'usb' | 'bluetooth' | 'wifi') => {
    switch (type) {
      case 'usb':
        return <Usb className="h-4 w-4" />;
      case 'bluetooth':
        return <Bluetooth className="h-4 w-4" />;
      case 'wifi':
        return <Wifi className="h-4 w-4" />;
    }
  };

  const getConnectionColor = (type: 'usb' | 'bluetooth' | 'wifi') => {
    switch (type) {
      case 'usb':
        return 'bg-blue-500';
      case 'bluetooth':
        return 'bg-blue-600';
      case 'wifi':
        return 'bg-green-500';
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Printer className="h-5 w-5" />
              Configuration des Imprimantes Thermiques
            </CardTitle>
            <CardDescription>
              Gestion des imprimantes de reçus 57mm, 58mm et 80mm
            </CardDescription>
          </div>
          <Button
            onClick={detectPrinters}
            disabled={loading}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Détecter
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Permissions Web API */}
        <Alert>
          <Settings className="h-4 w-4" />
          <AlertDescription>
            Pour utiliser les imprimantes USB et Bluetooth, autorisez l'accès aux périphériques 
            dans votre navigateur. Chrome/Edge recommandés pour une meilleure compatibilité.
          </AlertDescription>
        </Alert>

        <Separator />

        {/* Liste des imprimantes */}
        {printers.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            {loading ? 'Détection en cours...' : 'Aucune imprimante détectée'}
          </div>
        ) : (
          <div className="space-y-4">
            {printers.map((printer) => (
              <Card key={printer.id} className="relative">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg text-white ${getConnectionColor(printer.type)}`}>
                        {getConnectionIcon(printer.type)}
                      </div>
                      <div>
                        <h3 className="font-medium">{printer.name}</h3>
                        <p className="text-sm text-muted-foreground">
                          Connexion {printer.type.toUpperCase()}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {printer.connected ? (
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Connectée
                        </Badge>
                      ) : (
                        <Badge variant="outline">
                          <XCircle className="h-3 w-3 mr-1" />
                          Déconnectée
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    {/* Format papier */}
                    <div>
                      <label className="text-sm font-medium mb-1 block">
                        Format papier
                      </label>
                      <Select
                        value={selectedWidth[printer.id]?.toString() || printer.width.toString()}
                        onValueChange={(value) => updatePrinterWidth(printer.id, parseInt(value) as 57 | 58 | 80)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="57">57mm</SelectItem>
                          <SelectItem value="58">58mm</SelectItem>
                          <SelectItem value="80">80mm</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Actions de connexion */}
                    <div>
                      <label className="text-sm font-medium mb-1 block">
                        Connexion
                      </label>
                      {printer.connected ? (
                        <Button
                          onClick={() => disconnectPrinter(printer.id)}
                          variant="outline"
                          className="w-full"
                        >
                          Déconnecter
                        </Button>
                      ) : (
                        <Button
                          onClick={() => connectPrinter(printer.id)}
                          disabled={connecting === printer.id}
                          className="w-full"
                        >
                          {connecting === printer.id ? 'Connexion...' : 'Connecter'}
                        </Button>
                      )}
                    </div>

                    {/* Actions de test */}
                    <div>
                      <label className="text-sm font-medium mb-1 block">
                        Test
                      </label>
                      <div className="flex gap-2">
                        <Button
                          onClick={() => testPrint(printer.id)}
                          disabled={!printer.connected || testing === printer.id}
                          variant="outline"
                          size="sm"
                          className="flex-1"
                        >
                          <TestTube className="h-3 w-3 mr-1" />
                          {testing === printer.id ? 'Test...' : 'Test'}
                        </Button>
                        
                        {printer.connected && (
                          <Button
                            onClick={() => setDefaultPrinter(printer.id)}
                            variant="outline"
                            size="sm"
                            className="flex-1"
                          >
                            Par défaut
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Aperçu du reçu */}
        {printers.some(p => p.connected) && (
          <>
            <Separator />
            <div>
              <h3 className="font-medium mb-3">Aperçu du Reçu</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">Format 57mm</h4>
                  <div className="border rounded-lg p-2 bg-gray-50 overflow-hidden">
                    <ThermalTestTemplate width={57} />
                  </div>
                </div>
                <div>
                  <h4 className="text-sm font-medium mb-2">Format 58mm</h4>
                  <div className="border rounded-lg p-2 bg-gray-50 overflow-hidden">
                    <ThermalTestTemplate width={58} />
                  </div>
                </div>
                <div>
                  <h4 className="text-sm font-medium mb-2">Format 80mm</h4>
                  <div className="border rounded-lg p-2 bg-gray-50 overflow-hidden">
                    <ThermalTestTemplate width={80} />
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};