import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useIsMobile } from '@/hooks/use-mobile';
import { 
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  ComposedChart,
  Area
} from 'recharts';
import { 
  DollarSign, 
  TrendingUp, 
  Percent, 
  Calculator,
  Download,
  FileText,
  AlertCircle,
  Banknote
} from 'lucide-react';
import { useFinancialReports } from '@/hooks/useFinancialReports';
import { formatCurrency, formatNumber } from '@/lib/formatters';

interface FinancialReportsProps {
  filters: {
    dateDebut: string;
    dateFin: string;
    boutiqueId: string;
    employeId: string;
    categorieId: string;
  };
}

export const FinancialReports = ({ filters }: FinancialReportsProps) => {
  const { 
    data: financialData, 
    isLoading,
    margesAnalyse,
    rentabilite,
    tvaAnalyse,
    coutOperationnels,
    evolutionMarges,
    repartitionCharges
  } = useFinancialReports(filters);
  const isMobile = useIsMobile();

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-8 w-full mb-2" />
                <Skeleton className="h-4 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Skeleton className="h-80" />
          <Skeleton className="h-80" />
        </div>
      </div>
    );
  }

  const COLORS = ['hsl(var(--primary))', 'hsl(var(--secondary))', 'hsl(var(--accent))', 'hsl(var(--destructive))'];

  const getMargeStatus = (marge: number) => {
    if (marge >= 30) return { color: 'text-green-600', badge: 'Excellente' };
    if (marge >= 20) return { color: 'text-blue-600', badge: 'Bonne' };
    if (marge >= 10) return { color: 'text-yellow-600', badge: 'Correcte' };
    return { color: 'text-red-600', badge: 'Faible' };
  };

  return (
    <div className="space-y-6">
      {/* KPIs financiers */}
      <div className={`grid gap-4 ${isMobile ? 'grid-cols-2' : 'grid-cols-1 md:grid-cols-4'}`}>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Marge Brute</p>
                <p className="text-2xl font-bold">{margesAnalyse?.margeBruteGlobale || 0}%</p>
                <p className={`text-xs ${getMargeStatus(margesAnalyse?.margeBruteGlobale || 0).color}`}>
                  {formatCurrency(margesAnalyse?.montantMargeBrute || 0)}
                </p>
              </div>
              <Percent className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Rentabilité Nette</p>
                <p className="text-2xl font-bold">{rentabilite?.rentabiliteNette || 0}%</p>
                <p className="text-xs text-green-600">
                  {formatCurrency(rentabilite?.beneficeNet || 0)} de bénéfice
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">TVA à Déclaré</p>
                <p className="text-2xl font-bold">{formatCurrency(tvaAnalyse?.tvaADeclarer || 0)}</p>
                <p className="text-xs text-blue-600">
                  Période courante
                </p>
              </div>
              <Calculator className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Charges Totales</p>
                <p className="text-2xl font-bold">{formatCurrency(coutOperationnels?.chargesTotales || 0)}</p>
                <p className="text-xs text-orange-600">
                  {coutOperationnels?.pourcentageCA || 0}% du CA
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Graphiques financiers */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Évolution des marges */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Évolution des Marges</CardTitle>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <ComposedChart data={evolutionMarges}>
                <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                <XAxis 
                  dataKey="mois" 
                  stroke="hsl(var(--muted-foreground))"
                  fontSize={12}
                />
                <YAxis 
                  yAxisId="left"
                  stroke="hsl(var(--muted-foreground))"
                  fontSize={12}
                  tickFormatter={(value) => formatCurrency(value)}
                />
                <YAxis 
                  yAxisId="right"
                  orientation="right"
                  stroke="hsl(var(--muted-foreground))"
                  fontSize={12}
                  tickFormatter={(value) => `${value}%`}
                />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'hsl(var(--background))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px'
                  }}
                  formatter={(value, name) => [
                    name === 'margePourcentage' ? `${value}%` : formatCurrency(value as number),
                    name === 'margePourcentage' ? 'Marge %' : 'Marge FCFA'
                  ]}
                />
                <Bar 
                  yAxisId="left"
                  dataKey="margeMontant" 
                  fill="hsl(var(--primary))" 
                  name="Marge FCFA"
                  opacity={0.8}
                />
                <Line 
                  yAxisId="right"
                  type="monotone" 
                  dataKey="margePourcentage" 
                  stroke="hsl(var(--destructive))" 
                  strokeWidth={2}
                  name="Marge %"
                />
              </ComposedChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Répartition des charges */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Répartition des Charges</CardTitle>
            <Button variant="outline" size="sm">
              <Banknote className="h-4 w-4 mr-2" />
              Détail
            </Button>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={repartitionCharges}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="montant"
                >
                  {repartitionCharges?.map((_, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'hsl(var(--background))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px'
                  }}
                  formatter={(value) => [formatCurrency(value as number), 'Montant']}
                />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Analyses détaillées */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Analyse par catégorie */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Percent className="h-5 w-5 text-primary" />
              Marges par Catégorie
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-80 overflow-y-auto">
              {margesAnalyse?.margesParCategorie?.map((categorie, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium text-sm">{categorie.nom}</p>
                    <p className="text-xs text-muted-foreground">
                      {formatCurrency(categorie.ca)} de CA
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-sm">{categorie.marge}%</p>
                    <Badge variant="outline" className="text-xs">
                      {getMargeStatus(categorie.marge).badge}
                    </Badge>
                  </div>
                </div>
              )) || (
                <p className="text-muted-foreground text-center py-4">
                  Aucune donnée disponible
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* TVA détaillée */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5 text-blue-500" />
              Analyse TVA
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-primary/5 rounded-lg">
                <span className="text-sm font-medium">TVA Collectée</span>
                <span className="font-bold">{formatCurrency(tvaAnalyse?.tvaCollectee || 0)}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-secondary/5 rounded-lg">
                <span className="text-sm font-medium">TVA Déductible</span>
                <span className="font-bold">{formatCurrency(tvaAnalyse?.tvaDeductible || 0)}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-accent/5 rounded-lg border-l-4 border-accent">
                <span className="text-sm font-medium">TVA à Déclarer</span>
                <span className="font-bold text-accent">{formatCurrency(tvaAnalyse?.tvaADeclarer || 0)}</span>
              </div>
              <Button variant="outline" className="w-full mt-4">
                <FileText className="h-4 w-4 mr-2" />
                Générer Déclaration
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Alertes financières */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-warning" />
              Alertes Financières
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[]?.map((alerte, index) => (
                <div key={index} className={`p-3 rounded-lg border-l-4 ${
                  alerte.niveau === 'critique' ? 'border-destructive bg-destructive/5' :
                  alerte.niveau === 'attention' ? 'border-warning bg-warning/5' :
                  'border-blue-500 bg-blue-500/5'
                }`}>
                  <p className="font-medium text-sm">{alerte.titre}</p>
                  <p className="text-xs text-muted-foreground mt-1">{alerte.message}</p>
                </div>
              )) || (
                <>
                  <div className="p-3 rounded-lg border-l-4 border-warning bg-warning/5">
                    <p className="font-medium text-sm">Marge faible</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Catégorie "Accessoires" sous le seuil de 15%
                    </p>
                  </div>
                  <div className="p-3 rounded-lg border-l-4 border-blue-500 bg-blue-500/5">
                    <p className="font-medium text-sm">Tendance positive</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Rentabilité en hausse de 3% ce mois
                    </p>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};