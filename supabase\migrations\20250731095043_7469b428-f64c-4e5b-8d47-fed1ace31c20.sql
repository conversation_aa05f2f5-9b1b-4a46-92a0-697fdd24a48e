-- PHASE 2: Activation complète des triggers et fonctions

-- 1. Triggers pour mise à jour automatique des timestamps
CREATE TRIGGER update_boutiques_updated_at
    BEFORE UPDATE ON public.boutiques
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_stocks_updated_at
    BEFORE UPDATE ON public.stocks
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_employes_updated_at
    BEFORE UPDATE ON public.employes
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_produits_updated_at
    BEFORE UPDATE ON public.produits
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_transferts_updated_at
    BEFORE UPDATE ON public.transferts
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_ventes_updated_at
    BEFORE UPDATE ON public.ventes
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- 2. Trigger pour alertes automatiques de stock
CREATE TRIGGER check_stock_alert_trigger
    AFTER INSERT OR UPDATE OF quantite ON public.stocks
    FOR EACH ROW
    EXECUTE FUNCTION public.check_stock_alert();

-- 3. Trigger pour décrémenter le stock après une vente
CREATE TRIGGER update_stock_after_sale_trigger
    AFTER INSERT ON public.vente_details
    FOR EACH ROW
    EXECUTE FUNCTION public.update_stock_after_sale();

-- 4. Ajouter quelques données de test pour vérifier le fonctionnement
-- Test avec un stock bas qui devrait déclencher une alerte
UPDATE public.stocks 
SET quantite = 2 
WHERE id = (SELECT id FROM public.stocks LIMIT 1);

-- Test avec un stock épuisé
UPDATE public.stocks 
SET quantite = 0 
WHERE id = (SELECT id FROM public.stocks OFFSET 1 LIMIT 1);