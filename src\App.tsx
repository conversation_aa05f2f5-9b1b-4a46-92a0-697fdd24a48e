import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from '@/components/auth/AuthProvider';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { LoginPage } from '@/components/auth/LoginPage';
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";

// Configuration optimisée React Query pour performance
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Cache pendant 5 minutes pour les données relativement statiques
      staleTime: 5 * 60 * 1000,
      // Garde en cache pendant 10 minutes
      gcTime: 10 * 60 * 1000,
      // Retry automatique pour la robustesse
      retry: (failureCount, error: any) => {
        if (error?.code === 'PGRST116') return false; // Pas de retry pour les erreurs de données
        return failureCount < 2;
      },
      // Refetch en arrière-plan pour la fraîcheur des données
      refetchOnWindowFocus: true,
      refetchOnMount: 'always',
    },
    mutations: {
      retry: 1,
    },
  },
});

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/auth" element={<LoginPage />} />
            <Route path="/" element={
              <ProtectedRoute>
                <Index />
              </ProtectedRoute>
            } />
            <Route path="/stocks" element={
              <ProtectedRoute requiredPermission={{ resource: 'stocks', action: 'read' }}>
                <Index />
              </ProtectedRoute>
            } />
            <Route path="/ventes" element={
              <ProtectedRoute requiredPermission={{ resource: 'ventes', action: 'read' }}>
                <Index />
              </ProtectedRoute>
            } />
            <Route path="/transferts" element={
              <ProtectedRoute requiredPermission={{ resource: 'transferts', action: 'read' }}>
                <Index />
              </ProtectedRoute>
            } />
            <Route path="/transferts/:id" element={
              <ProtectedRoute requiredPermission={{ resource: 'transferts', action: 'read' }}>
                <Index />
              </ProtectedRoute>
            } />
            <Route path="/alertes" element={
              <ProtectedRoute>
                <Index />
              </ProtectedRoute>
            } />
            <Route path="/boutiques" element={
              <ProtectedRoute requiredPermission={{ resource: 'boutiques', action: 'read' }}>
                <Index />
              </ProtectedRoute>
            } />
            <Route path="/produits" element={
              <ProtectedRoute requiredPermission={{ resource: 'produits', action: 'read' }}>
                <Index />
              </ProtectedRoute>
            } />
            <Route path="/employes" element={
              <ProtectedRoute requiredPermission={{ resource: 'employes', action: 'read' }}>
                <Index />
              </ProtectedRoute>
            } />
            <Route path="/inventaire" element={
              <ProtectedRoute requiredPermission={{ resource: 'stocks', action: 'read' }}>
                <Index />
              </ProtectedRoute>
            } />
            <Route path="/admin" element={
              <ProtectedRoute requiredRole={['admin_system']}>
                <Index />
              </ProtectedRoute>
            } />
            <Route path="/rapports" element={
              <ProtectedRoute>
                <Index />
              </ProtectedRoute>
            } />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
