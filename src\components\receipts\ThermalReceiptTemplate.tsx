/**
 * Templates de reçus thermiques pour différents formats d'imprimantes
 */

import React from 'react';
import { formatCurrency, formatDateTime } from '@/lib/formatters';

export interface ThermalReceiptProps {
  width: 57 | 58 | 80;
  boutique: {
    nom: string;
    adresse: string;
    telephone?: string;
    email?: string;
  };
  vente: {
    numero_facture: string;
    date_vente: string;
    employe_nom?: string;
    client_nom?: string;
    client_telephone?: string;
  };
  produits: Array<{
    nom: string;
    quantite: number;
    prix_unitaire: number;
    sous_total: number;
  }>;
  totaux: {
    sous_total: number;
    montant_tva: number;
    montant_total: number;
    mode_paiement: string;
  };
}

export const ThermalReceiptTemplate: React.FC<ThermalReceiptProps> = ({
  width,
  boutique,
  vente,
  produits,
  totaux
}) => {
  const maxChars = width === 80 ? 48 : 32;
  const isNarrow = width === 57 || width === 58;

  const styles = {
    container: {
      fontFamily: 'monospace',
      fontSize: isNarrow ? '10px' : '12px',
      lineHeight: '1.2',
      width: `${width}mm`,
      padding: '2mm',
      backgroundColor: 'white',
      color: 'black',
      margin: '0 auto'
    },
    center: {
      textAlign: 'center' as const
    },
    left: {
      textAlign: 'left' as const
    },
    right: {
      textAlign: 'right' as const
    },
    bold: {
      fontWeight: 'bold'
    },
    large: {
      fontSize: isNarrow ? '14px' : '16px',
      fontWeight: 'bold'
    },
    separator: {
      borderTop: '1px solid black',
      margin: '2px 0'
    },
    row: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'flex-start'
    },
    truncate: {
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      whiteSpace: 'nowrap' as const
    }
  };

  const truncateText = (text: string, maxLength: number): string => {
    return text.length > maxLength ? text.substring(0, maxLength - 3) + '...' : text;
  };

  return (
    <div style={styles.container}>
      {/* En-tête boutique */}
      <div style={{ ...styles.center, ...styles.large }}>
        {boutique.nom}
      </div>
      <div style={styles.center}>
        {boutique.adresse}
      </div>
      {boutique.telephone && (
        <div style={styles.center}>
          Tél: {boutique.telephone}
        </div>
      )}
      {boutique.email && (
        <div style={styles.center}>
          {boutique.email}
        </div>
      )}

      {/* Ligne de séparation */}
      <div style={styles.separator}></div>

      {/* Informations facture */}
      <div style={styles.bold}>
        Facture: {vente.numero_facture}
      </div>
      <div>
        Date: {formatDateTime(vente.date_vente)}
      </div>
      {vente.employe_nom && (
        <div>
          Vendeur: {truncateText(vente.employe_nom, maxChars - 9)}
        </div>
      )}
      {vente.client_nom && (
        <div>
          Client: {truncateText(vente.client_nom, maxChars - 8)}
        </div>
      )}
      {vente.client_telephone && (
        <div>
          Tél: {vente.client_telephone}
        </div>
      )}

      <div style={styles.separator}></div>

      {/* Produits */}
      {produits.map((produit, index) => (
        <div key={index} style={{ marginBottom: '4px' }}>
          <div style={styles.truncate}>
            {truncateText(produit.nom, maxChars)}
          </div>
          <div style={styles.row}>
            <span>
              {produit.quantite} x {formatCurrency(produit.prix_unitaire)}
            </span>
            <span style={styles.bold}>
              {formatCurrency(produit.sous_total)}
            </span>
          </div>
        </div>
      ))}

      {/* Totaux */}
      <div style={styles.separator}></div>
      
      <div style={styles.row}>
        <span style={styles.bold}>Sous-total:</span>
        <span>{formatCurrency(totaux.sous_total)}</span>
      </div>
      
      <div style={styles.row}>
        <span style={styles.bold}>TVA:</span>
        <span>{formatCurrency(totaux.montant_tva)}</span>
      </div>
      
      <div style={{ ...styles.row, ...styles.large, marginTop: '4px' }}>
        <span>TOTAL:</span>
        <span>{formatCurrency(totaux.montant_total)}</span>
      </div>

      <div style={{ marginTop: '8px' }}>
        <strong>Paiement: {totaux.mode_paiement}</strong>
      </div>

      {/* Pied de page */}
      <div style={styles.separator}></div>
      <div style={{ ...styles.center, marginTop: '8px' }}>
        <div style={styles.bold}>Merci de votre visite!</div>
        <div>À bientôt chez</div>
        <div>Maison des Téléphones</div>
      </div>
    </div>
  );
};

/**
 * Template de test pour vérifier l'impression
 */
export const ThermalTestTemplate: React.FC<{ width: 57 | 58 | 80 }> = ({ width }) => {
  const testData: ThermalReceiptProps = {
    width,
    boutique: {
      nom: 'MAISON DES TELEPHONES',
      adresse: 'Test - Abidjan, Côte d\'Ivoire',
      telephone: '+225 XX XX XX XX',
      email: '<EMAIL>'
    },
    vente: {
      numero_facture: 'TEST-001',
      date_vente: new Date().toISOString(),
      employe_nom: 'Test Vendeur',
      client_nom: 'Client Test',
      client_telephone: '+225 XX XX XX XX'
    },
    produits: [
      {
        nom: 'iPhone 15 Pro Max 256GB Space Black',
        quantite: 1,
        prix_unitaire: 850000,
        sous_total: 850000
      },
      {
        nom: 'Coque de protection',
        quantite: 2,
        prix_unitaire: 5000,
        sous_total: 10000
      }
    ],
    totaux: {
      sous_total: 860000,
      montant_tva: 0,
      montant_total: 860000,
      mode_paiement: 'Espèces'
    }
  };

  return <ThermalReceiptTemplate {...testData} />;
};