import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: {
    resource: string;
    action: string;
  };
  requiredRole?: string[];
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission,
  requiredRole,
}) => {
  const { user, profile, loading } = useAuth();
  const location = useLocation();

  console.log('ProtectedRoute - user:', user, 'profile:', profile, 'loading:', loading, 'location:', location.pathname);

  // Afficher un loader pendant le chargement
  if (loading) {
    console.log('ProtectedRoute - Affichage du loader');
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground">Vérification des permissions...</p>
        </div>
      </div>
    );
  }

  // Rediriger vers la page de connexion si non authentifié
  if (!user) {
    console.log('ProtectedRoute - Utilisateur non connecté, redirection vers /auth');
    return <Navigate to="/auth" state={{ from: location }} replace />;
  }

  // Vérifier les permissions requises si spécifiées
  if (requiredPermission && profile) {
    const { hasPermission } = useAuth();
    const hasRequiredPermission = hasPermission(requiredPermission.resource, requiredPermission.action);
    if (!hasRequiredPermission) {
      console.log('ProtectedRoute - Permission insuffisante');
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-2">Accès refusé</h2>
            <p className="text-muted-foreground">Vous n'avez pas les permissions nécessaires pour accéder à cette page.</p>
          </div>
        </div>
      );
    }
  }

  // Vérifier les rôles requis si spécifiés
  if (requiredRole && profile) {
    const hasRequiredRole = requiredRole.includes(profile.role);
    if (!hasRequiredRole) {
      console.log('ProtectedRoute - Rôle insuffisant');
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-2">Accès refusé</h2>
            <p className="text-muted-foreground">Votre rôle ne vous permet pas d'accéder à cette page.</p>
          </div>
        </div>
      );
    }
  }

  console.log('ProtectedRoute - Utilisateur connecté et autorisé, affichage du contenu protégé');
  return <>{children}</>;
};