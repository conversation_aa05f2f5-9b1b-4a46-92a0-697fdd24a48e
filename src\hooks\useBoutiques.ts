import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useSupabaseRetry, getOptimizedQueryConfig } from './useRetryMechanism';
import { boutiqueSchema, type BoutiqueFormData } from '@/lib/validations';

export interface Boutique {
  id: string;
  nom: string;
  adresse: string;
  telephone?: string;
  email?: string;
  type: 'centrale' | 'boutique';
  statut: 'active' | 'inactive' | 'maintenance';
  manager_id?: string;
  created_at: string;
  updated_at: string;
}

export const useBoutiques = () => {
  const { executeSupabaseQuery } = useSupabaseRetry();
  
  return useQuery({
    queryKey: ['boutiques'],
    queryFn: () => executeSupabaseQuery(async () => {
      return await supabase
        .from('boutiques')
        .select('*')
        .order('type', { ascending: false })
        .order('nom');
    }),
    ...getOptimizedQueryConfig(),
    staleTime: 15 * 60 * 1000, // 15 minutes - données relativement statiques
    gcTime: 30 * 60 * 1000,     // 30 minutes
  });
};

export const useCreateBoutique = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { executeSupabaseQuery } = useSupabaseRetry();

  return useMutation({
    mutationFn: async (boutique: Omit<Boutique, 'id' | 'created_at' | 'updated_at'>) => {
      // Validation côté client
      const validatedData = boutiqueSchema.parse(boutique);
      
      return executeSupabaseQuery(async () => {
        return await supabase
          .from('boutiques')
          .insert(validatedData as any)
          .select()
          .single();
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['boutiques'] });
      toast({
        title: "Boutique créée",
        description: "La nouvelle boutique a été créée avec succès."
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "Erreur de création",
        description: error.message.includes('validation') 
          ? "Données invalides. Vérifiez les champs requis."
          : "Impossible de créer la boutique. Réessayez."
      });
    },
    ...getOptimizedQueryConfig(),
  });
};

export const useUpdateBoutique = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { executeSupabaseQuery } = useSupabaseRetry();
  
  return useMutation({
    mutationFn: async ({ id, ...boutique }: { id: string } & Partial<Omit<Boutique, 'id' | 'created_at' | 'updated_at'>>) => {
      // Validation partielle côté client
      const validatedData = boutiqueSchema.partial().parse(boutique);
      
      return executeSupabaseQuery(async () => {
        return await supabase
          .from('boutiques')
          .update(validatedData)
          .eq('id', id)
          .select()
          .single();
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['boutiques'] });
      toast({
        title: "Boutique mise à jour",
        description: "La boutique a été mise à jour avec succès."
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive", 
        title: "Erreur de mise à jour",
        description: error.message.includes('validation') 
          ? "Données invalides. Vérifiez les champs modifiés."
          : "Impossible de mettre à jour la boutique. Réessayez."
      });
    },
    ...getOptimizedQueryConfig(),
  });
};

export const useDeleteBoutique = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { executeSupabaseQuery } = useSupabaseRetry();
  
  return useMutation({
    mutationFn: async (id: string) => {
      if (!id || typeof id !== 'string') {
        throw new Error('ID boutique invalide');
      }
      
      return executeSupabaseQuery(async () => {
        const result = await supabase
          .from('boutiques')
          .delete()
          .eq('id', id);
        
        if (result.error) throw result.error;
        return { data: { success: true }, error: null };
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['boutiques'] });
      queryClient.invalidateQueries({ queryKey: ['employes'] }); // Invalider les employés liés
      toast({
        title: "Boutique supprimée",
        description: "La boutique a été supprimée avec succès."
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "Erreur de suppression",
        description: error.message.includes('foreign key') 
          ? "Impossible de supprimer: des employés ou données sont liés à cette boutique."
          : "Impossible de supprimer la boutique. Réessayez."
      });
    },
    ...getOptimizedQueryConfig(),
  });
};