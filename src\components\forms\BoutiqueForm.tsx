import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { ErrorFallback } from '@/components/ui/error-fallback';
import { boutiqueSchema, type BoutiqueFormData } from '@/lib/validations';
import { useCreateBoutique, useUpdateBoutique, type Boutique } from '@/hooks/useBoutiques';

interface BoutiqueFormProps {
  boutique?: any; // Données existantes pour l'édition
  onSuccess?: () => void;
  onCancel?: () => void;
}

export const BoutiqueForm: React.FC<BoutiqueFormProps> = ({
  boutique,
  onSuccess,
  onCancel
}) => {
  const isEditing = !!boutique;
  const createMutation = useCreateBoutique();
  const updateMutation = useUpdateBoutique();
  
  const form = useForm<BoutiqueFormData>({
    resolver: zodResolver(boutiqueSchema),
    defaultValues: {
      nom: boutique?.nom || '',
      type: boutique?.type || 'boutique',
      adresse: boutique?.adresse || '',
      telephone: boutique?.telephone || '',
      email: boutique?.email || '',
      statut: boutique?.statut || 'active'
    }
  });

  const onSubmit = async (data: BoutiqueFormData) => {
    try {
      if (isEditing) {
        await updateMutation.mutateAsync({ id: boutique.id, ...data });
      } else {
        await createMutation.mutateAsync(data as Omit<Boutique, 'id' | 'created_at' | 'updated_at'>);
      }
      onSuccess?.();
    } catch (error) {
      // L'erreur est gérée par les hooks
    }
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;
  const error = createMutation.error || updateMutation.error;

  if (error && !isLoading) {
    return (
      <ErrorFallback
        error={error}
        resetError={() => {
          createMutation.reset();
          updateMutation.reset();
        }}
        title={`Erreur lors de ${isEditing ? 'la modification' : 'la création'}`}
      />
    );
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>
          {isEditing ? 'Modifier la boutique' : 'Nouvelle boutique'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="nom"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nom de la boutique *</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Ex: Boutique Centre-Ville" 
                        {...field} 
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Type *</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                      disabled={isLoading}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner un type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="centrale">Centrale</SelectItem>
                        <SelectItem value="boutique">Boutique</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="adresse"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Adresse *</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Adresse complète de la boutique"
                      className="resize-none"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="telephone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Téléphone</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Ex: +33 1 23 45 67 89"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input 
                        type="email"
                        placeholder="<EMAIL>"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="statut"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Statut</FormLabel>
                  <Select 
                    onValueChange={field.onChange} 
                    defaultValue={field.value}
                    disabled={isLoading}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                      <SelectItem value="maintenance">Maintenance</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Une boutique inactive ne peut pas effectuer d'opérations
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex gap-3 pt-4">
              <Button 
                type="submit" 
                disabled={isLoading}
                className="flex-1"
              >
                {isLoading && <LoadingSpinner size="sm" className="mr-2" />}
                {isEditing ? 'Mettre à jour' : 'Créer la boutique'}
              </Button>
              
              {onCancel && (
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={onCancel}
                  disabled={isLoading}
                >
                  Annuler
                </Button>
              )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};