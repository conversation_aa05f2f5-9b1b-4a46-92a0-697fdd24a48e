-- Phase 1: Critical Security Fixes

-- 1. Update database functions to include proper search_path protection
CREATE OR REPLACE FUNCTION public.calculate_vente_total()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = 'public'
AS $function$
BEGIN
  UPDATE ventes 
  SET montant_total = (
    SELECT COALESCE(SUM(sous_total), 0)
    FROM vente_details 
    WHERE vente_id = COALESCE(NEW.vente_id, OLD.vente_id)
  ),
  updated_at = now()
  WHERE id = COALESCE(NEW.vente_id, OLD.vente_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$function$;

CREATE OR REPLACE FUNCTION public.check_stock_alert()
RETURNS trigger
LANGUAGE plpgsql
SET search_path = 'public'
AS $function$
BEGIN
    -- Vérifier si le stock est en dessous du seuil
    IF NEW.quantite <= NEW.seuil_alerte AND (OLD.quantite IS NULL OR OLD.quantite > NEW.seuil_alerte) THEN
        INSERT INTO alertes (type, titre, message, boutique_id, produit_id, niveau)
        SELECT 
            CASE WHEN NEW.quantite = 0 THEN 'stock_epuise' ELSE 'stock_bas' END,
            CASE WHEN NEW.quantite = 0 THEN 'Stock épuisé' ELSE 'Stock bas' END,
            CASE WHEN NEW.quantite = 0 
                THEN 'Le produit ' || p.nom || ' est épuisé dans la boutique ' || b.nom
                ELSE 'Le produit ' || p.nom || ' a un stock bas (' || NEW.quantite || ' restant) dans la boutique ' || b.nom
            END,
            NEW.boutique_id,
            NEW.produit_id,
            CASE WHEN NEW.quantite = 0 THEN 'error' ELSE 'warning' END
        FROM produits p, boutiques b
        WHERE p.id = NEW.produit_id AND b.id = NEW.boutique_id;
    END IF;
    
    RETURN NEW;
END;
$function$;

-- 2. Create secure PIN authentication function
CREATE OR REPLACE FUNCTION public.authenticate_with_pin_secure(_pin text, _user_agent text DEFAULT NULL, _user_ip inet DEFAULT NULL)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = 'public'
AS $function$
DECLARE
  user_profile RECORD;
  attempt_count INTEGER;
  lockout_until TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Check for too many failed attempts
  SELECT COUNT(*), MAX(created_at) + interval '15 minutes'
  INTO attempt_count, lockout_until
  FROM audit_logs 
  WHERE table_name = 'auth_attempts' 
    AND old_data->>'user_ip' = _user_ip::text
    AND old_data->>'success' = 'false'
    AND created_at > now() - interval '15 minutes';

  IF attempt_count >= 5 AND lockout_until > now() THEN
    -- Log failed attempt due to lockout
    INSERT INTO audit_logs (table_name, operation, old_data, user_ip, user_agent)
    VALUES ('auth_attempts', 'LOCKOUT', jsonb_build_object(
      'pin_attempt', 'REDACTED',
      'user_ip', _user_ip,
      'success', false,
      'reason', 'too_many_attempts'
    ), _user_ip, _user_agent);
    
    RETURN json_build_object('error', 'Account temporarily locked due to too many failed attempts');
  END IF;

  -- Find user profile with matching PIN using crypt for verification
  SELECT up.*, e.nom, e.prenom, e.email, b.nom as boutique_nom
  INTO user_profile
  FROM user_profiles up
  LEFT JOIN employes e ON e.id = up.employe_id
  LEFT JOIN boutiques b ON b.id = e.boutique_id
  WHERE up.pin_code IS NOT NULL 
    AND up.pin_code = crypt(_pin, up.pin_code)
    AND up.is_active = true;

  -- Log authentication attempt
  INSERT INTO audit_logs (table_name, operation, old_data, user_id, user_ip, user_agent)
  VALUES ('auth_attempts', 'PIN_AUTH', jsonb_build_object(
    'pin_attempt', 'REDACTED',
    'user_ip', _user_ip,
    'success', CASE WHEN user_profile.id IS NOT NULL THEN true ELSE false END,
    'user_id', user_profile.user_id
  ), user_profile.user_id, _user_ip, _user_agent);

  -- Return null if no match found
  IF user_profile.id IS NULL THEN
    RETURN json_build_object('error', 'Invalid PIN');
  END IF;

  -- Update last login
  UPDATE user_profiles 
  SET last_login = now() 
  WHERE id = user_profile.id;

  -- Return user profile data (without sensitive information)
  RETURN json_build_object(
    'id', user_profile.id,
    'user_id', user_profile.user_id,
    'role', user_profile.role,
    'employe_id', user_profile.employe_id,
    'employe_nom', user_profile.nom,
    'employe_prenom', user_profile.prenom,
    'boutique_nom', user_profile.boutique_nom,
    'is_active', user_profile.is_active
  );
END;
$function$;

-- 3. Create secure PIN generation with proper hashing
CREATE OR REPLACE FUNCTION public.generate_user_pin_secure(target_user_id uuid)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = 'public'
AS $function$
DECLARE
  new_pin TEXT;
  pin_hash TEXT;
BEGIN
  -- Vérifier les permissions
  IF NOT has_permission(auth.uid(), 'users', 'admin') THEN
    RAISE EXCEPTION 'Permission insuffisante pour générer un PIN';
  END IF;

  -- Générer un PIN de 4 chiffres sécurisé
  new_pin := LPAD((FLOOR(RANDOM() * 9000) + 1000)::TEXT, 4, '0');
  
  -- Hacher le PIN pour le stockage sécurisé
  pin_hash := crypt(new_pin, gen_salt('bf', 10));
  
  -- Mettre à jour le profil utilisateur
  UPDATE user_profiles 
  SET pin_code = pin_hash, updated_at = now()
  WHERE user_id = target_user_id;

  -- Log PIN generation (without the actual PIN)
  INSERT INTO audit_logs (table_name, operation, new_data, user_id)
  VALUES ('user_profiles', 'PIN_GENERATED', jsonb_build_object(
    'target_user_id', target_user_id,
    'generated_by', auth.uid()
  ), auth.uid());
  
  -- Retourner le PIN en clair (uniquement pour l'affichage immédiat)
  RETURN new_pin;
END;
$function$;

-- 4. Improve RLS policies - Replace overly permissive policies

-- Drop existing overly permissive policies
DROP POLICY IF EXISTS "Allow full access for authenticated users" ON alertes;
DROP POLICY IF EXISTS "Allow full access for authenticated users" ON boutiques;
DROP POLICY IF EXISTS "Allow full access for authenticated users" ON categories;

-- Create restrictive RLS policies for alertes
CREATE POLICY "Users can view alertes in their boutique"
ON alertes FOR SELECT
USING (
  boutique_id IN (
    SELECT e.boutique_id 
    FROM user_profiles up
    JOIN employes e ON e.id = up.employe_id
    WHERE up.user_id = auth.uid() AND up.is_active = true
  ) OR
  has_permission(auth.uid(), 'alertes', 'admin')
);

CREATE POLICY "Admins can manage all alertes"
ON alertes FOR ALL
USING (has_permission(auth.uid(), 'alertes', 'admin'))
WITH CHECK (has_permission(auth.uid(), 'alertes', 'admin'));

-- Create restrictive RLS policies for boutiques
CREATE POLICY "Users can view their assigned boutique"
ON boutiques FOR SELECT
USING (
  id IN (
    SELECT e.boutique_id 
    FROM user_profiles up
    JOIN employes e ON e.id = up.employe_id
    WHERE up.user_id = auth.uid() AND up.is_active = true
  ) OR
  has_permission(auth.uid(), 'boutiques', 'read')
);

CREATE POLICY "Authorized users can manage boutiques"
ON boutiques FOR ALL
USING (has_permission(auth.uid(), 'boutiques', 'write'))
WITH CHECK (has_permission(auth.uid(), 'boutiques', 'write'));

-- Create restrictive RLS policies for categories
CREATE POLICY "Authenticated users can view categories"
ON categories FOR SELECT
USING (auth.uid() IS NOT NULL);

CREATE POLICY "Authorized users can manage categories"
ON categories FOR INSERT, UPDATE, DELETE
USING (has_permission(auth.uid(), 'produits', 'write'))
WITH CHECK (has_permission(auth.uid(), 'produits', 'write'));