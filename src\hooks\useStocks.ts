import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface Stock {
  id: string;
  produit_id: string;
  boutique_id: string;
  quantite: number;
  seuil_alerte: number;
  emplacement?: string;
  produits?: {
    nom: string;
    marque: string;
    modele: string;
    code_produit: string;
    prix_vente: number;
  };
  boutiques?: {
    nom: string;
  };
}

export const useStocks = (boutiqueId?: string) => {
  return useQuery({
    queryKey: ['stocks', boutiqueId],
    queryFn: async () => {
      let query = supabase
        .from('stocks')
        .select(`
          *,
          produits:produit_id(nom, marque, modele, code_produit, prix_vente),
          boutiques:boutique_id(nom)
        `);
      
      if (boutiqueId) {
        query = query.eq('boutique_id', boutiqueId);
      }
      
      const { data, error } = await query.order('quantite', { ascending: true });
      
      if (error) throw error;
      return data as Stock[];
    },
    // Stock change fréquemment, cache plus court
    staleTime: 2 * 60 * 1000,  // 2 minutes
    gcTime: 5 * 60 * 1000,     // 5 minutes
    refetchInterval: 5 * 60 * 1000, // Actualisation auto toutes les 5 minutes
  });
};

export const useUpdateStock = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, quantite }: { id: string; quantite: number }) => {
      const { data, error } = await supabase
        .from('stocks')
        .update({ quantite })
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['stocks'] });
      toast({
        title: "Stock mis à jour",
        description: "La quantité en stock a été mise à jour."
      });
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de mettre à jour le stock."
      });
    }
  });
};