import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Calendar, MapPin, User, CreditCard, TrendingUp, ShoppingCart, DollarSign, Plus, Eye } from 'lucide-react';
import { useVentes } from '@/hooks/useVentes';
import { useBoutiques } from '@/hooks/useBoutiques';
import { formatCurrency, formatDate } from '@/lib/formatters';
import { VenteForm } from './VenteForm';
import { VenteDetail } from './VenteDetail';

export const VenteManager = () => {
  const [selectedBoutique, setSelectedBoutique] = useState<string>('all');
  const [dateDebut, setDateDebut] = useState<string>('');
  const [dateFin, setDateFin] = useState<string>('');
  const [showNewVenteDialog, setShowNewVenteDialog] = useState(false);
  const [selectedVenteId, setSelectedVenteId] = useState<string | null>(null);

  const { data: boutiques } = useBoutiques();
  const { data: ventes, isLoading } = useVentes(selectedBoutique === 'all' ? undefined : selectedBoutique, dateDebut, dateFin);

  const getModeIcon = (mode: string) => {
    switch (mode) {
      case 'especes': return '💵';
      case 'carte': return '💳';
      case 'cheque': return '📝';
      case 'virement': return '🏦';
      case 'mobile_money': return '📱';
      case 'crypto': return '₿';
      default: return '💰';
    }
  };

  const getStatutVariant = (statut: string) => {
    switch (statut) {
      case 'validee': return 'default';
      case 'en_attente': return 'secondary';
      case 'annulee': return 'destructive';
      case 'remboursee': return 'outline';
      default: return 'secondary';
    }
  };

  // Calculs statistiques
  const totalVentes = ventes?.reduce((sum, vente) => sum + vente.montant_total, 0) || 0;
  const nombreVentes = ventes?.length || 0;
  const panierMoyen = nombreVentes > 0 ? totalVentes / nombreVentes : 0;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Gestion des Ventes</h1>
        <Button onClick={() => setShowNewVenteDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Nouvelle Vente
        </Button>
      </div>

      {/* Filtres */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Date début</label>
              <Input
                type="date"
                value={dateDebut}
                onChange={(e) => setDateDebut(e.target.value)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Date fin</label>
              <Input
                type="date"
                value={dateFin}
                onChange={(e) => setDateFin(e.target.value)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Boutique</label>
              <Select value={selectedBoutique} onValueChange={setSelectedBoutique}>
                <SelectTrigger>
                  <SelectValue placeholder="Toutes les boutiques" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Toutes les boutiques</SelectItem>
                  {boutiques?.map((boutique) => (
                    <SelectItem key={boutique.id} value={boutique.id}>
                      {boutique.nom}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total des Ventes</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalVentes)}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Nombre de Ventes</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{nombreVentes}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Panier Moyen</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(panierMoyen)}</div>
          </CardContent>
        </Card>
      </div>

      {/* Liste des ventes */}
      <div className="grid gap-4">
        <h2 className="text-xl font-semibold">Liste des ventes</h2>
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : ventes && ventes.length > 0 ? (
          <div className="grid gap-4">
            {ventes.map((vente) => (
              <Card key={vente.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-2">
                      <div className="flex items-center gap-4">
                        <div className="font-semibold text-lg">{vente.numero_facture}</div>
                        <Badge variant={getStatutVariant(vente.statut)}>
                          {vente.statut.toUpperCase()}
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>{formatDate(vente.date_vente)}</span>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <span>{vente.boutiques?.nom}</span>
                        </div>
                        
                        {(vente.clients || vente.client_nom) && (
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-muted-foreground" />
                            <span>
                              {vente.clients ? 
                                `${vente.clients.nom} ${vente.clients.prenom || ''}`.trim() : 
                                vente.client_nom
                              }
                            </span>
                          </div>
                        )}
                        
                        <div className="flex items-center gap-2">
                          <CreditCard className="h-4 w-4 text-muted-foreground" />
                          <span className="flex items-center gap-1">
                            {getModeIcon(vente.mode_paiement)}
                            {vente.mode_paiement.charAt(0).toUpperCase() + vente.mode_paiement.slice(1).replace('_', ' ')}
                          </span>
                        </div>
                      </div>
                      
                      <div className="text-sm text-muted-foreground">
                        Vendu par: {vente.employes?.nom} {vente.employes?.prenom}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <div className="text-2xl font-bold text-primary">
                          {formatCurrency(vente.montant_total)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          TVA: {formatCurrency(vente.montant_tva)}
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedVenteId(vente.id)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        Voir
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="flex flex-col items-center justify-center h-32">
              <ShoppingCart className="h-12 w-12 text-muted-foreground mb-4" />
              <p className="text-muted-foreground">Aucune vente trouvée</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Dialogs */}
      <Dialog open={showNewVenteDialog} onOpenChange={setShowNewVenteDialog}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Nouvelle Vente</DialogTitle>
          </DialogHeader>
          <VenteForm
            onSuccess={() => setShowNewVenteDialog(false)}
            onCancel={() => setShowNewVenteDialog(false)}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={!!selectedVenteId} onOpenChange={() => setSelectedVenteId(null)}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Détails de la vente</DialogTitle>
          </DialogHeader>
          {selectedVenteId && (
            <VenteDetail
              venteId={selectedVenteId}
              onClose={() => setSelectedVenteId(null)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};