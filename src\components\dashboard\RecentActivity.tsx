import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { LoadingSpinner, LoadingSkeleton } from "@/components/ui/loading-spinner";
import { 
  ArrowLeftRight, 
  ShoppingCart, 
  Package, 
  AlertTriangle,
  Clock,
  CheckCircle,
  XCircle
} from "lucide-react";
import { useVentes } from "@/hooks/useVentes";
import { useTransferts } from "@/hooks/useTransferts";
import { useAlertes } from "@/hooks/useAlertes";
import { formatDistanceToNow } from "date-fns";
import { fr } from "date-fns/locale";
import { formatCurrency, formatDateTime } from "@/lib/formatters";
import React from "react";

const getStatusIcon = (status: string) => {
  switch (status) {
    case "completed":
    case "validee":
    case "recu":
      return <CheckCircle className="h-3 w-3 text-success" />;
    case "warning":
    case "error":
      return <AlertTriangle className="h-3 w-3 text-warning" />;
    case "pending":
    case "en_attente":
    case "expedie":
      return <Clock className="h-3 w-3 text-muted-foreground" />;
    case "processing":
    case "en_transit":
      return <Clock className="h-3 w-3 text-info" />;
    default:
      return <Clock className="h-3 w-3 text-muted-foreground" />;
  }
};

const getStatusBadge = (status: string) => {
  switch (status) {
    case "completed":
    case "validee":
    case "recu":
      return <Badge variant="default" className="text-xs bg-success/10 text-success">Terminé</Badge>;
    case "warning":
    case "error":
      return <Badge variant="outline" className="text-xs border-warning text-warning">Urgent</Badge>;
    case "pending":
    case "en_attente":
      return <Badge variant="secondary" className="text-xs">En attente</Badge>;
    case "processing":
    case "en_transit":
    case "expedie":
      return <Badge variant="outline" className="text-xs border-info text-info">En cours</Badge>;
    default:
      return <Badge variant="secondary" className="text-xs">Inconnu</Badge>;
  }
};

export const RecentActivity = () => {
  const { data: ventes, isLoading: ventesLoading } = useVentes();
  const { data: transferts, isLoading: transfertsLoading } = useTransferts();
  const { data: alertes, isLoading: alertesLoading } = useAlertes();

  const isLoading = ventesLoading || transfertsLoading || alertesLoading;

  // Combiner et trier les activités récentes par timestamp réel
  const activities = React.useMemo(() => {
    const allActivities: any[] = [];

    // Ajouter les ventes récentes
    if (ventes) {
      ventes.slice(0, 5).forEach(vente => {
        allActivities.push({
          id: `vente-${vente.id}`,
          type: "vente",
          title: "Nouvelle vente",
          description: `Facture ${vente.numero_facture} - ${formatCurrency(vente.montant_total)}`,
          timestamp: new Date(vente.date_vente).getTime(),
          time: formatDistanceToNow(new Date(vente.date_vente), { addSuffix: true, locale: fr }),
          status: vente.statut,
          icon: ShoppingCart,
          color: "text-success"
        });
      });
    }

    // Ajouter les transferts récents
    if (transferts) {
      transferts.slice(0, 3).forEach(transfert => {
        allActivities.push({
          id: `transfert-${transfert.id}`,
          type: "transfert",
          title: "Transfert de stock",
          description: `${transfert.boutique_source?.nom || 'Boutique'} → ${transfert.boutique_destination?.nom || 'Boutique'}`,
          timestamp: new Date(transfert.created_at).getTime(),
          time: formatDistanceToNow(new Date(transfert.created_at), { addSuffix: true, locale: fr }),
          status: transfert.statut,
          icon: ArrowLeftRight,
          color: "text-primary"
        });
      });
    }

    // Ajouter les alertes récentes
    if (alertes) {
      alertes.slice(0, 3).forEach(alerte => {
        allActivities.push({
          id: `alerte-${alerte.id}`,
          type: "alerte",
          title: alerte.titre,
          description: alerte.message,
          timestamp: new Date(alerte.created_at).getTime(),
          time: formatDistanceToNow(new Date(alerte.created_at), { addSuffix: true, locale: fr }),
          status: alerte.niveau,
          icon: AlertTriangle,
          color: alerte.niveau === 'error' ? "text-destructive" : "text-warning"
        });
      });
    }

    // Trier par timestamp décroissant et prendre les 6 plus récents
    return allActivities
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, 6);
  }, [ventes, transferts, alertes]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5 text-primary" />
          Activité Récente
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            <LoadingSpinner text="Chargement des activités récentes..." />
            <LoadingSkeleton rows={4} />
          </div>
        ) : (
          <div className="space-y-4">
            {activities.length > 0 ? activities.map((activity) => {
              const Icon = activity.icon;
              return (
                <div
                  key={activity.id}
                  className="flex items-start gap-4 p-3 border border-border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className={`h-8 w-8 rounded-lg bg-muted flex items-center justify-center flex-shrink-0`}>
                    <Icon className={`h-4 w-4 ${activity.color}`} />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium text-sm truncate">{activity.title}</h4>
                      {getStatusIcon(activity.status)}
                    </div>
                    <p className="text-sm text-muted-foreground truncate">
                      {activity.description}
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-xs text-muted-foreground">{activity.time}</span>
                      {getStatusBadge(activity.status)}
                    </div>
                  </div>
                </div>
              );
            }) : (
              <div className="text-center py-8 text-muted-foreground">
                <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Aucune activité récente</p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};