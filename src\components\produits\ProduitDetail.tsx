import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Edit, Package, TrendingUp, History, BarChart3 } from 'lucide-react';
import { useProduit } from '@/hooks/useProduits';
import { formatCurrency, formatDate } from '@/lib/formatters';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

interface ProduitDetailProps {
  produitId: string;
  onEdit?: () => void;
}

export const ProduitDetail = ({ produitId, onEdit }: ProduitDetailProps) => {
  const { data: produit, isLoading } = useProduit(produitId);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!produit) {
    return <div>Produit non trouvé</div>;
  }

  const totalStock = produit.stocks?.reduce((acc, stock) => acc + stock.quantite, 0) || 0;
  const marge = produit.prix_achat > 0 ? ((produit.prix_vente - produit.prix_achat) / produit.prix_achat * 100) : 0;
  const valeurStock = totalStock * produit.prix_achat;

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-start">
        <div>
          <div className="flex items-center space-x-3">
            <h2 className="text-2xl font-bold">{produit.nom}</h2>
            <Badge 
              variant={produit.etat === 'neuf' ? 'default' : produit.etat === 'occasion' ? 'secondary' : 'destructive'}
            >
              {produit.etat}
            </Badge>
          </div>
          <p className="text-muted-foreground">
            {produit.marque} {produit.modele} • Code: {produit.code_produit}
          </p>
          {produit.categorie && (
            <Badge variant="outline" className="mt-2">
              {produit.categorie.nom}
            </Badge>
          )}
        </div>
        {onEdit && (
          <Button onClick={onEdit}>
            <Edit className="h-4 w-4 mr-2" />
            Modifier
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Informations principales */}
        <div className="lg:col-span-2 space-y-6">
          {/* Caractéristiques */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Package className="h-5 w-5" />
                <span>Caractéristiques</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Marque</label>
                  <p>{produit.marque}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Modèle</label>
                  <p>{produit.modele}</p>
                </div>
                {produit.couleur && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Couleur</label>
                    <p>{produit.couleur}</p>
                  </div>
                )}
                {produit.stockage && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Stockage</label>
                    <p>{produit.stockage}</p>
                  </div>
                )}
                {produit.imei && (
                  <div className="col-span-2">
                    <label className="text-sm font-medium text-muted-foreground">IMEI/Série</label>
                    <p className="font-mono">{produit.imei}</p>
                  </div>
                )}
              </div>
              
              {produit.description && (
                <>
                  <Separator />
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Description</label>
                    <p className="mt-1">{produit.description}</p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Stock par boutique */}
          <Card>
            <CardHeader>
              <CardTitle>Stock par boutique</CardTitle>
            </CardHeader>
            <CardContent>
              {produit.stocks && produit.stocks.length > 0 ? (
                <div className="space-y-3">
                  {produit.stocks.map((stock) => (
                    <div key={stock.id} className="flex justify-between items-center p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">{stock.boutique?.nom}</div>
                        {stock.emplacement && (
                          <div className="text-sm text-muted-foreground">
                            Emplacement: {stock.emplacement}
                          </div>
                        )}
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-lg">{stock.quantite}</div>
                        <div className="text-sm text-muted-foreground">
                          Seuil: {stock.seuil_alerte}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-center py-8">
                  Aucun stock enregistré pour ce produit
                </p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Panneau latéral */}
        <div className="space-y-6">
          {/* Tarification */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>Tarification</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Prix d'achat</label>
                <p className="text-xl font-bold">{formatCurrency(produit.prix_achat)}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Prix de vente</label>
                <p className="text-xl font-bold text-green-600">{formatCurrency(produit.prix_vente)}</p>
              </div>
              <Separator />
              <div>
                <label className="text-sm font-medium text-muted-foreground">Marge bénéficiaire</label>
                <p className={`text-lg font-bold ${marge > 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {marge.toFixed(2)}%
                </p>
                <p className="text-sm text-muted-foreground">
                  {formatCurrency(produit.prix_vente - produit.prix_achat)} par unité
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Statistiques de stock */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5" />
                <span>Statistiques</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Stock total</label>
                <p className="text-2xl font-bold">{totalStock}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Boutiques</label>
                <p className="text-lg font-semibold">{produit.stocks?.length || 0}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Valeur stock</label>
                <p className="text-lg font-semibold">{formatCurrency(valeurStock)}</p>
              </div>
            </CardContent>
          </Card>

          {/* Informations système */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <History className="h-5 w-5" />
                <span>Informations</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Créé le</label>
                <p>{formatDate(produit.created_at)}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Modifié le</label>
                <p>{formatDate(produit.updated_at)}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};