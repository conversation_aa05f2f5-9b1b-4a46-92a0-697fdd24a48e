import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useBoutiques } from '@/hooks/useBoutiques';
import { useEmployes } from '@/hooks/useEmployes';
import { useCategories } from '@/hooks/useCategories';
import { Calendar, RotateCcw } from 'lucide-react';

interface ReportsFiltersProps {
  filters: {
    dateDebut: string;
    dateFin: string;
    boutiqueId: string;
    employeId: string;
    categorieId: string;
  };
  onFiltersChange: (filters: any) => void;
}

export const ReportsFilters = ({ filters, onFiltersChange }: ReportsFiltersProps) => {
  const { data: boutiques } = useBoutiques();
  const { data: employes } = useEmployes();
  const { data: categories } = useCategories();

  const handleFilterChange = (key: string, value: string) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  const resetFilters = () => {
    onFiltersChange({
      dateDebut: '',
      dateFin: '',
      boutiqueId: '',
      employeId: '',
      categorieId: ''
    });
  };

  const presetDates = [
    { label: 'Aujourd\'hui', days: 0 },
    { label: '7 derniers jours', days: 7 },
    { label: '30 derniers jours', days: 30 },
    { label: '90 derniers jours', days: 90 }
  ];

  const setPresetDate = (days: number) => {
    const end = new Date();
    const start = new Date();
    start.setDate(end.getDate() - days);
    
    onFiltersChange({
      ...filters,
      dateDebut: start.toISOString().split('T')[0],
      dateFin: end.toISOString().split('T')[0]
    });
  };

  return (
    <div className="space-y-6">
      {/* Période prédéfinie */}
      <div className="space-y-2">
        <Label>Période Prédéfinie</Label>
        <div className="flex flex-wrap gap-2">
          {presetDates.map((preset) => (
            <Button
              key={preset.days}
              variant="outline"
              size="sm"
              onClick={() => setPresetDate(preset.days)}
              className="text-xs"
            >
              {preset.label}
            </Button>
          ))}
        </div>
      </div>

      {/* Dates personnalisées */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="dateDebut">Date de début</Label>
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              id="dateDebut"
              type="date"
              value={filters.dateDebut}
              onChange={(e) => handleFilterChange('dateDebut', e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="dateFin">Date de fin</Label>
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              id="dateFin"
              type="date"
              value={filters.dateFin}
              onChange={(e) => handleFilterChange('dateFin', e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      </div>

      {/* Filtres par entité */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label>Boutique</Label>
          <Select value={filters.boutiqueId} onValueChange={(value) => handleFilterChange('boutiqueId', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Toutes les boutiques" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Toutes les boutiques</SelectItem>
              {boutiques?.map((boutique) => (
                <SelectItem key={boutique.id} value={boutique.id}>
                  {boutique.nom}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Employé</Label>
          <Select value={filters.employeId} onValueChange={(value) => handleFilterChange('employeId', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Tous les employés" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Tous les employés</SelectItem>
              {employes?.map((employe) => (
                <SelectItem key={employe.id} value={employe.id}>
                  {employe.nom} {employe.prenom}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Catégorie</Label>
          <Select value={filters.categorieId} onValueChange={(value) => handleFilterChange('categorieId', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Toutes les catégories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Toutes les catégories</SelectItem>
              {categories?.map((categorie) => (
                <SelectItem key={categorie.id} value={categorie.id}>
                  {categorie.nom}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={resetFilters} className="flex items-center gap-2">
          <RotateCcw className="h-4 w-4" />
          Réinitialiser
        </Button>
      </div>
    </div>
  );
};