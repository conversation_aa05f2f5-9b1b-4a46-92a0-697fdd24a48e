import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useMemo } from 'react';

interface PerformanceReportsFilters {
  dateDebut: string;
  dateFin: string;
  boutiqueId: string;
  employeId: string;
  categorieId: string;
}

export const usePerformanceReports = (filters: PerformanceReportsFilters) => {
  const { data: ventesData, isLoading: ventesLoading } = useQuery({
    queryKey: ['performance-ventes', filters],
    queryFn: async () => {
      let query = supabase
        .from('ventes')
        .select(`
          *,
          boutiques:boutique_id(nom, type),
          employes:employe_id(nom, prenom, poste)
        `)
        .order('date_vente', { ascending: false });

      if (filters.dateDebut) {
        query = query.gte('date_vente', filters.dateDebut);
      }
      if (filters.dateFin) {
        query = query.lte('date_vente', filters.dateFin);
      }
      if (filters.boutiqueId) {
        query = query.eq('boutique_id', filters.boutiqueId);
      }
      if (filters.employeId) {
        query = query.eq('employe_id', filters.employeId);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data;
    },
    staleTime: 5 * 60 * 1000,
  });

  const { data: boutiquesData, isLoading: boutiquesLoading } = useQuery({
    queryKey: ['boutiques-performance'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('boutiques')
        .select('*')
        .eq('statut', 'active');
      
      if (error) throw error;
      return data;
    },
    staleTime: 15 * 60 * 1000,
  });

  const isLoading = ventesLoading || boutiquesLoading;

  // Statistiques des boutiques
  const statsBoutiques = useMemo(() => {
    if (!ventesData || !boutiquesData) return null;

    const caTotalReseau = ventesData.reduce((sum, vente) => sum + (vente.montant_total || 0), 0);
    const nbBoutiquesActives = boutiquesData.length;

    // Performance par boutique
    const performanceParBoutique = boutiquesData.map(boutique => {
      const ventesParBoutique = ventesData.filter(v => v.boutique_id === boutique.id);
      const caBoutique = ventesParBoutique.reduce((sum, v) => sum + (v.montant_total || 0), 0);
      
      // Simulation d'objectifs
      const objectifBoutique = 5000000; // 5M FCFA par mois
      const performance = objectifBoutique > 0 ? Math.round((caBoutique / objectifBoutique) * 100) : 0;

      return {
        boutique: boutique.nom,
        ca: caBoutique,
        performance: Math.min(performance, 150) // Plafonner à 150%
      };
    });

    const performanceMoyenne = performanceParBoutique.reduce((sum, b) => sum + b.performance, 0) / nbBoutiquesActives;
    const croissance = 15.7; // +15.7% simulation

    return {
      caTotalReseau,
      nbBoutiquesActives,
      performanceMoyenne: Math.round(performanceMoyenne),
      croissance,
      performanceParBoutique: performanceParBoutique.sort((a, b) => b.performance - a.performance)
    };
  }, [ventesData, boutiquesData]);

  // Statistiques des employés
  const statsEmployes = useMemo(() => {
    if (!ventesData) return null;

    const employesUniques = [...new Map(
      ventesData
        .filter(v => v.employes)
        .map(v => [v.employe_id, v.employes])
    ).values()];

    const vendeursActifs = employesUniques.length;
    const caTotalEmployes = ventesData.reduce((sum, v) => sum + (v.montant_total || 0), 0);
    const caMoyenParVendeur = vendeursActifs > 0 ? caTotalEmployes / vendeursActifs : 0;

    return {
      vendeursActifs,
      caMoyenParVendeur,
      caTotalEmployes
    };
  }, [ventesData]);

  // Top vendeurs
  const topVendeurs = useMemo(() => {
    if (!ventesData) return [];

    const ventesParVendeur = ventesData.reduce((acc, vente) => {
      if (!vente.employe_id || !vente.employes) return acc;

      const employeId = vente.employe_id;
      if (!acc[employeId]) {
        acc[employeId] = {
          nom: vente.employes.nom,
          prenom: vente.employes.prenom,
          boutique: vente.boutiques?.nom || 'Non définie',
          ca: 0,
          nbVentes: 0
        };
      }
      acc[employeId].ca += vente.montant_total || 0;
      acc[employeId].nbVentes += 1;
      return acc;
    }, {} as Record<string, any>);

    return Object.values(ventesParVendeur)
      .sort((a: any, b: any) => b.ca - a.ca)
      .slice(0, 10);
  }, [ventesData]);

  // Objectifs vs réalisé (simulation)
  const objectifsVsRealise = useMemo(() => {
    const mois = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'];
    return mois.map((mois, index) => ({
      mois,
      objectif: 15000000 + (index * 1000000), // Objectifs croissants
      realise: 12000000 + (index * 1200000) + Math.random() * 2000000 // Simulation
    }));
  }, []);

  // Évolution des performances
  const evolutionPerformance = useMemo(() => {
    if (!ventesData) return [];

    // Grouper par semaine pour l'évolution
    const ventesParSemaine = ventesData.reduce((acc, vente) => {
      const date = new Date(vente.date_vente || '');
      const semaine = `S${Math.ceil(date.getDate() / 7)}-${date.getMonth() + 1}`;
      
      if (!acc[semaine]) {
        acc[semaine] = 0;
      }
      acc[semaine] += vente.montant_total || 0;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(ventesParSemaine)
      .map(([semaine, ca]) => ({ semaine, ca }))
      .slice(-8); // 8 dernières semaines
  }, [ventesData]);

  return {
    data: ventesData,
    isLoading,
    statsBoutiques,
    statsEmployes,
    evolutionPerformance,
    topVendeurs,
    objectifsVsRealise
  };
};