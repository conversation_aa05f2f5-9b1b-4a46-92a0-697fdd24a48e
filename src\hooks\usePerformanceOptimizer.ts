import { useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

/**
 * Hook d'optimisation pour précharger les données critiques
 * et maintenir la fraîcheur du cache
 */
export const usePerformanceOptimizer = () => {
  const queryClient = useQueryClient();

  useEffect(() => {
    // Préchargement des données critiques au démarrage
    const prefetchCriticalData = async () => {
      try {
        // Précharger les boutiques (données statiques)
        await queryClient.prefetchQuery({
          queryKey: ['boutiques'],
          queryFn: async () => {
            const { data, error } = await supabase
              .from('boutiques')
              .select('*')
              .order('type', { ascending: false })
              .order('nom');
            if (error) throw error;
            return data;
          },
          staleTime: 15 * 60 * 1000, // 15 minutes
        });

        // Précharger les employés (données semi-statiques)
        await queryClient.prefetchQuery({
          queryKey: ['employes'],
          queryFn: async () => {
            const { data, error } = await supabase
              .from('employes')
              .select(`
                *,
                boutiques:boutique_id(nom)
              `)
              .order('nom');
            if (error) throw error;
            return data;
          },
          staleTime: 10 * 60 * 1000, // 10 minutes
        });
      } catch (error) {
        console.error('Erreur lors du préchargement des données:', error);
        // Ne pas bloquer l'application si le préchargement échoue
      }
    };

    // Précharger après un court délai pour ne pas bloquer le rendu initial
    const timer = setTimeout(prefetchCriticalData, 100);

    return () => clearTimeout(timer);
  }, [queryClient]);

  // Fonction pour invalider intelligemment les caches liés
  const invalidateRelatedQueries = (entityType: string, boutiqueId?: string) => {
    switch (entityType) {
      case 'stock':
        // Invalider stocks, alertes et dashboard
        queryClient.invalidateQueries({ queryKey: ['stocks'] });
        queryClient.invalidateQueries({ queryKey: ['alertes'] });
        break;
      
      case 'vente':
        // Invalider ventes, stocks (quantité) et dashboard
        queryClient.invalidateQueries({ queryKey: ['ventes'] });
        queryClient.invalidateQueries({ queryKey: ['stocks'] });
        break;
      
      case 'transfert':
        // Invalider transferts, stocks et alertes
        queryClient.invalidateQueries({ queryKey: ['transferts'] });
        queryClient.invalidateQueries({ queryKey: ['stocks'] });
        queryClient.invalidateQueries({ queryKey: ['alertes'] });
        break;
      
      case 'employe':
        // Invalider employés et boutiques
        queryClient.invalidateQueries({ queryKey: ['employes'] });
        if (boutiqueId) {
          queryClient.invalidateQueries({ queryKey: ['employes', boutiqueId] });
        }
        break;
    }
  };

  // Fonction pour actualiser les données temps réel
  const refreshRealtimeData = () => {
    queryClient.invalidateQueries({ queryKey: ['alertes'] });
    queryClient.invalidateQueries({ queryKey: ['stocks'] });
    queryClient.invalidateQueries({ queryKey: ['ventes'] });
  };

  return {
    invalidateRelatedQueries,
    refreshRealtimeData,
    queryClient
  };
};

/**
 * Hook pour la synchronisation en temps réel des données critiques
 */
export const useRealtimeSync = (queryClient: any) => {
  useEffect(() => {
    const refreshRealtimeData = () => {
      queryClient.invalidateQueries({ queryKey: ['alertes'] });
      queryClient.invalidateQueries({ queryKey: ['stocks'] });
      queryClient.invalidateQueries({ queryKey: ['ventes'] });
    };

    // Configuration de la synchronisation temps réel via Supabase
    const channel = supabase
      .channel('realtime-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'stocks'
        },
        () => {
          // Actualiser les données liées aux stocks
          refreshRealtimeData();
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public', 
          table: 'alertes'
        },
        () => {
          // Actualiser les alertes
          refreshRealtimeData();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [queryClient]);
};