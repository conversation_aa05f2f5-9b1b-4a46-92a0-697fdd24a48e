import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CampagneInventaireForm } from './CampagneInventaireForm';
import { InventaireDetail } from './InventaireDetail';
import { MouvementsStock } from './MouvementsStock';
import { useCampagnesInventaire } from '@/hooks/useInventaire';
import { useBoutiques } from '@/hooks/useBoutiques';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  ClipboardList, 
  Plus, 
  History, 
  Eye,
  Calendar,
  MapPin,
  AlertTriangle,
  Lock
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';

export const InventaireManager = () => {
  const [selectedBoutique, setSelectedBoutique] = useState<string>('');
  const [showForm, setShowForm] = useState(false);
  const [selectedCampagne, setSelectedCampagne] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('campagnes');

  const { data: boutiques } = useBoutiques();
  const { data: campagnesResult, isLoading } = useCampagnesInventaire(selectedBoutique === 'all' ? undefined : selectedBoutique);
  const campagnes = campagnesResult?.data || [];

  const getStatutVariant = (statut: string) => {
    switch (statut) {
      case 'en_cours': return 'secondary';
      case 'terminee': return 'default';
      case 'validee': return 'default';
      case 'annulee': return 'destructive';
      default: return 'secondary';
    }
  };

  const getStatutLabel = (statut: string) => {
    switch (statut) {
      case 'en_cours': return 'En cours';
      case 'terminee': return 'Terminée';
      case 'validee': return 'Validée';
      case 'annulee': return 'Annulée';
      default: return statut;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-9 w-80" />
          <div className="flex items-center gap-4">
            <Skeleton className="h-10 w-64" />
            <Skeleton className="h-10 w-48" />
          </div>
        </div>
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="space-y-2 flex-1">
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-10 w-10 rounded-lg" />
                      <div className="space-y-2">
                        <Skeleton className="h-5 w-48" />
                        <Skeleton className="h-4 w-64" />
                      </div>
                    </div>
                    <Skeleton className="h-4 w-40" />
                  </div>
                  <div className="flex items-center gap-3">
                    <Skeleton className="h-6 w-20" />
                    <Skeleton className="h-8 w-28" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (selectedCampagne) {
    return (
      <InventaireDetail 
        campagneId={selectedCampagne}
        onBack={() => setSelectedCampagne(null)}
      />
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Gestion des Inventaires</h1>
        <div className="flex items-center gap-4">
          <Select value={selectedBoutique} onValueChange={setSelectedBoutique}>
            <SelectTrigger className="w-64">
              <SelectValue placeholder="Toutes les boutiques" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Toutes les boutiques</SelectItem>
              {boutiques?.map((boutique) => (
                <SelectItem key={boutique.id} value={boutique.id}>
                  {boutique.nom}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button onClick={() => setShowForm(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Nouvelle campagne
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="campagnes">
            <ClipboardList className="h-4 w-4 mr-2" />
            Campagnes
          </TabsTrigger>
          <TabsTrigger value="mouvements">
            <History className="h-4 w-4 mr-2" />
            Mouvements de stock
          </TabsTrigger>
        </TabsList>

        <TabsContent value="campagnes" className="space-y-4">
          {/* Alertes pour campagnes actives */}
          {campagnes.some(c => c.statut === 'en_cours') && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Vous avez {campagnes.filter(c => c.statut === 'en_cours').length} campagne(s) d'inventaire en cours. 
                Assurez-vous de les finaliser avant d'en créer de nouvelles.
              </AlertDescription>
            </Alert>
          )}

          <div className="grid gap-4">
            {campagnes.map((campagne) => {
              const isLocked = campagne.statut === 'terminee' || campagne.statut === 'validee';
              
              return (
                <Card key={campagne.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-primary/10">
                            {isLocked ? (
                              <Lock className="h-5 w-5 text-muted-foreground" />
                            ) : (
                              <ClipboardList className="h-5 w-5 text-primary" />
                            )}
                          </div>
                          <div>
                            <h3 className="font-semibold text-lg flex items-center gap-2">
                              {campagne.nom}
                              {isLocked && (
                                <Lock className="h-4 w-4 text-muted-foreground" />
                              )}
                            </h3>
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                {campagne.boutiques?.nom}
                              </div>
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {formatDistanceToNow(new Date(campagne.date_debut), { 
                                  addSuffix: true, 
                                  locale: fr 
                                })}
                              </div>
                              {campagne.date_fin && (
                                <div className="flex items-center gap-1">
                                  <Calendar className="h-3 w-3" />
                                  Terminée {formatDistanceToNow(new Date(campagne.date_fin), { 
                                    addSuffix: true, 
                                    locale: fr 
                                  })}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        
                        {campagne.employes && (
                          <p className="text-sm text-muted-foreground">
                            Responsable: {campagne.employes.prenom} {campagne.employes.nom}
                          </p>
                        )}
                        
                        {campagne.commentaires && (
                          <p className="text-sm">{campagne.commentaires}</p>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-3">
                        <Badge variant={getStatutVariant(campagne.statut)}>
                          {getStatutLabel(campagne.statut)}
                        </Badge>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedCampagne(campagne.id)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          {isLocked ? 'Consulter' : 'Voir détails'}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}

            {campagnes.length === 0 && (
              <Card>
                <CardContent className="p-12 text-center">
                  <ClipboardList className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="font-semibold mb-2">Aucune campagne d'inventaire</h3>
                  <p className="text-muted-foreground mb-4">
                    Créez votre première campagne d'inventaire pour commencer.
                  </p>
                  <Button onClick={() => setShowForm(true)}>
                    Créer une campagne
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="mouvements" className="space-y-4">
          <MouvementsStock 
            boutiqueId={selectedBoutique === 'all' ? undefined : selectedBoutique}
          />
        </TabsContent>
      </Tabs>

      {showForm && (
        <CampagneInventaireForm
          onClose={() => setShowForm(false)}
          boutiqueId={selectedBoutique === 'all' ? undefined : selectedBoutique}
        />
      )}
    </div>
  );
};