-- Création de la table des mouvements de stock pour la traçabilité
CREATE TABLE public.mouvements_stock (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  produit_id UUID REFERENCES public.produits(id) ON DELETE CASCADE,
  boutique_id UUID REFERENCES public.boutiques(id) ON DELETE CASCADE,
  type_mouvement TEXT NOT NULL CHECK (type_mouvement IN ('entree', 'sortie', 'ajustement', 'transfert_sortie', 'transfert_entree', 'vente', 'inventaire')),
  quantite_avant INTEGER NOT NULL,
  quantite_apres INTEGER NOT NULL,
  quantite_mouvement INTEGER NOT NULL,
  motif TEXT,
  reference_id UUID, -- ID de la vente, transfert, etc. qui a causé le mouvement
  reference_type TEXT, -- 'vente', 'transfert', 'ajustement_manuel', etc.
  employe_id UUID REFERENCES public.employes(id),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  created_by UUID REFERENCES auth.users(id)
);

-- Création de la table des campagnes d'inventaire
CREATE TABLE public.campagnes_inventaire (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  nom TEXT NOT NULL,
  boutique_id UUID REFERENCES public.boutiques(id) ON DELETE CASCADE,
  date_debut TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  date_fin TIMESTAMP WITH TIME ZONE,
  statut TEXT NOT NULL DEFAULT 'en_cours' CHECK (statut IN ('en_cours', 'terminee', 'validee', 'annulee')),
  commentaires TEXT,
  responsable_id UUID REFERENCES public.employes(id),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  created_by UUID REFERENCES auth.users(id)
);

-- Création de la table des détails d'inventaire
CREATE TABLE public.inventaire_details (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  campagne_id UUID REFERENCES public.campagnes_inventaire(id) ON DELETE CASCADE,
  produit_id UUID REFERENCES public.produits(id) ON DELETE CASCADE,
  stock_id UUID REFERENCES public.stocks(id) ON DELETE CASCADE,
  quantite_theorique INTEGER NOT NULL DEFAULT 0,
  quantite_physique INTEGER,
  ecart INTEGER GENERATED ALWAYS AS (quantite_physique - quantite_theorique) STORED,
  valide BOOLEAN DEFAULT false,
  commentaire TEXT,
  compte_par UUID REFERENCES public.employes(id),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Création de la table des fournisseurs
CREATE TABLE public.fournisseurs (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  nom TEXT NOT NULL,
  contact_nom TEXT,
  email TEXT,
  telephone TEXT,
  adresse TEXT,
  conditions_paiement TEXT,
  delai_livraison INTEGER, -- en jours
  statut TEXT NOT NULL DEFAULT 'actif' CHECK (statut IN ('actif', 'inactif', 'suspendu')),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Création de la table des commandes fournisseurs
CREATE TABLE public.commandes_fournisseur (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  numero_commande TEXT NOT NULL UNIQUE,
  fournisseur_id UUID REFERENCES public.fournisseurs(id) ON DELETE CASCADE,
  boutique_id UUID REFERENCES public.boutiques(id) ON DELETE CASCADE,
  date_commande TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  date_livraison_prevue TIMESTAMP WITH TIME ZONE,
  date_livraison_reelle TIMESTAMP WITH TIME ZONE,
  statut TEXT NOT NULL DEFAULT 'en_attente' CHECK (statut IN ('en_attente', 'confirmee', 'expediee', 'livree', 'annulee')),
  montant_total NUMERIC(10,2) NOT NULL DEFAULT 0,
  montant_tva NUMERIC(10,2) NOT NULL DEFAULT 0,
  commentaires TEXT,
  responsable_id UUID REFERENCES public.employes(id),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Création de la table des détails de commandes fournisseurs
CREATE TABLE public.commande_details (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  commande_id UUID REFERENCES public.commandes_fournisseur(id) ON DELETE CASCADE,
  produit_id UUID REFERENCES public.produits(id) ON DELETE CASCADE,
  quantite_commandee INTEGER NOT NULL,
  quantite_livree INTEGER DEFAULT 0,
  prix_unitaire NUMERIC(10,2) NOT NULL,
  prix_total NUMERIC(10,2) GENERATED ALWAYS AS (quantite_commandee * prix_unitaire) STORED,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Activation de RLS sur toutes les nouvelles tables
ALTER TABLE public.mouvements_stock ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.campagnes_inventaire ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.inventaire_details ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fournisseurs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.commandes_fournisseur ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.commande_details ENABLE ROW LEVEL SECURITY;

-- Création des politiques RLS pour les mouvements de stock
CREATE POLICY "Authenticated users can view stock movements" ON public.mouvements_stock FOR SELECT USING (true);
CREATE POLICY "Authenticated users can insert stock movements" ON public.mouvements_stock FOR INSERT WITH CHECK (true);

-- Création des politiques RLS pour les campagnes d'inventaire
CREATE POLICY "Authenticated users can manage inventory campaigns" ON public.campagnes_inventaire FOR ALL USING (true);
CREATE POLICY "Authenticated users can manage inventory details" ON public.inventaire_details FOR ALL USING (true);

-- Création des politiques RLS pour les fournisseurs et commandes
CREATE POLICY "Authenticated users can manage suppliers" ON public.fournisseurs FOR ALL USING (true);
CREATE POLICY "Authenticated users can manage supplier orders" ON public.commandes_fournisseur FOR ALL USING (true);
CREATE POLICY "Authenticated users can manage order details" ON public.commande_details FOR ALL USING (true);

-- Création d'un trigger pour enregistrer automatiquement les mouvements de stock
CREATE OR REPLACE FUNCTION public.log_stock_movement()
RETURNS TRIGGER AS $$
DECLARE
  movement_type TEXT;
  motif_text TEXT;
BEGIN
  -- Déterminer le type de mouvement
  IF TG_OP = 'UPDATE' THEN
    IF NEW.quantite > OLD.quantite THEN
      movement_type := 'entree';
      motif_text := 'Augmentation de stock';
    ELSE
      movement_type := 'sortie';
      motif_text := 'Diminution de stock';
    END IF;
    
    -- Enregistrer le mouvement seulement si la quantité a changé
    IF NEW.quantite != OLD.quantite THEN
      INSERT INTO public.mouvements_stock (
        produit_id,
        boutique_id,
        type_mouvement,
        quantite_avant,
        quantite_apres,
        quantite_mouvement,
        motif,
        created_by
      ) VALUES (
        NEW.produit_id,
        NEW.boutique_id,
        movement_type,
        OLD.quantite,
        NEW.quantite,
        NEW.quantite - OLD.quantite,
        motif_text,
        auth.uid()
      );
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Appliquer le trigger aux stocks
CREATE TRIGGER stock_movement_trigger
  AFTER UPDATE ON public.stocks
  FOR EACH ROW
  EXECUTE FUNCTION public.log_stock_movement();

-- Création d'un trigger pour mettre à jour les timestamps
CREATE TRIGGER update_campagnes_inventaire_updated_at
  BEFORE UPDATE ON public.campagnes_inventaire
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_inventaire_details_updated_at
  BEFORE UPDATE ON public.inventaire_details
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_fournisseurs_updated_at
  BEFORE UPDATE ON public.fournisseurs
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_commandes_fournisseur_updated_at
  BEFORE UPDATE ON public.commandes_fournisseur
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Création d'index pour optimiser les performances
CREATE INDEX idx_mouvements_stock_produit_boutique ON public.mouvements_stock(produit_id, boutique_id);
CREATE INDEX idx_mouvements_stock_date ON public.mouvements_stock(created_at);
CREATE INDEX idx_inventaire_details_campagne ON public.inventaire_details(campagne_id);
CREATE INDEX idx_commande_details_commande ON public.commande_details(commande_id);