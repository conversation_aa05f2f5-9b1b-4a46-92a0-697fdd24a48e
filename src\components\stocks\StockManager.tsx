import { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useStocks, useUpdateStock } from '@/hooks/useStocks';
import { useBoutiques } from '@/hooks/useBoutiques';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertTriangle, Package, Edit } from 'lucide-react';

export const StockManager = () => {
  const [selectedBoutique, setSelectedBoutique] = useState<string>('');
  const [editingStock, setEditingStock] = useState<string | null>(null);
  const [newQuantity, setNewQuantity] = useState<number>(0);

  const { data: boutiques } = useBoutiques();
  const { data: stocks, isLoading } = useStocks(selectedBoutique === 'all' ? undefined : selectedBoutique);
  const updateStock = useUpdateStock();

  const handleUpdateStock = async (stockId: string) => {
    await updateStock.mutateAsync({ id: stockId, quantite: newQuantity });
    setEditingStock(null);
  };

  const getStockStatus = (quantite: number, seuil: number) => {
    if (quantite === 0) return { label: 'Épuisé', variant: 'destructive' as const };
    if (quantite <= seuil) return { label: 'Stock bas', variant: 'secondary' as const };
    return { label: 'En stock', variant: 'default' as const };
  };

  if (isLoading) return <div>Chargement...</div>;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Gestion des Stocks</h1>
        <Select value={selectedBoutique} onValueChange={setSelectedBoutique}>
          <SelectTrigger className="w-64">
            <SelectValue placeholder="Toutes les boutiques" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Toutes les boutiques</SelectItem>
            {boutiques?.map((boutique) => (
              <SelectItem key={boutique.id} value={boutique.id}>
                {boutique.nom}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="grid gap-4">
        {stocks?.map((stock) => {
          const status = getStockStatus(stock.quantite, stock.seuil_alerte);
          
          return (
            <Card key={stock.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-primary/10">
                      <Package className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold">
                        {stock.produits?.nom} - {stock.produits?.marque}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {stock.produits?.code_produit} • {stock.boutiques?.nom}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      {editingStock === stock.id ? (
                        <div className="flex items-center space-x-2">
                          <Input
                            type="number"
                            value={newQuantity}
                            onChange={(e) => setNewQuantity(Number(e.target.value))}
                            className="w-20"
                            min="0"
                          />
                          <Button 
                            size="sm" 
                            onClick={() => handleUpdateStock(stock.id)}
                            disabled={updateStock.isPending}
                          >
                            Sauver
                          </Button>
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => setEditingStock(null)}
                          >
                            Annuler
                          </Button>
                        </div>
                      ) : (
                        <>
                          <div className="flex items-center space-x-2">
                            <span className="text-2xl font-bold">{stock.quantite}</span>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => {
                                setEditingStock(stock.id);
                                setNewQuantity(stock.quantite);
                              }}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            Seuil d'alerte: {stock.seuil_alerte}
                          </p>
                        </>
                      )}
                    </div>
                    
                    <div className="flex flex-col items-end space-y-2">
                      <Badge variant={status.variant}>
                        {status.label}
                      </Badge>
                      {stock.quantite <= stock.seuil_alerte && (
                        <div className="flex items-center text-warning">
                          <AlertTriangle className="h-4 w-4 mr-1" />
                          <span className="text-xs">Réapprovisionner</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};