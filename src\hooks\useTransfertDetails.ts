import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface TransfertDetail {
  id: string;
  transfert_id: string;
  produit_id: string;
  quantite: number;
  created_at: string;
  produits?: {
    nom: string;
    marque: string;
    modele: string;
    code_produit: string;
    prix_vente: number;
  };
}

export const useTransfertDetails = (transfertId: string) => {
  return useQuery({
    queryKey: ['transfert-details', transfertId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('transfert_details')
        .select(`
          *,
          produits:produit_id(nom, marque, modele, code_produit, prix_vente)
        `)
        .eq('transfert_id', transfertId)
        .order('created_at', { ascending: true });
      
      if (error) throw error;
      return data as TransfertDetail[];
    },
    enabled: !!transfertId,
    staleTime: 5 * 60 * 1000,  // 5 minutes
    gcTime: 10 * 60 * 1000,    // 10 minutes
  });
};

export const useCreateTransfertDetail = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (detail: Omit<TransfertDetail, 'id' | 'created_at' | 'produits'>) => {
      const { data, error } = await supabase
        .from('transfert_details')
        .insert(detail)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['transfert-details', variables.transfert_id] });
      queryClient.invalidateQueries({ queryKey: ['transferts'] });
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible d'ajouter le produit au transfert."
      });
    }
  });
};

export const useUpdateTransfertDetail = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, quantite }: { id: string; quantite: number }) => {
      const { data, error } = await supabase
        .from('transfert_details')
        .update({ quantite })
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['transfert-details', data.transfert_id] });
      toast({
        title: "Produit mis à jour",
        description: "La quantité du produit a été mise à jour."
      });
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de mettre à jour le produit."
      });
    }
  });
};

export const useDeleteTransfertDetail = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('transfert_details')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
      return id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['transfert-details'] });
      toast({
        title: "Produit supprimé",
        description: "Le produit a été retiré du transfert."
      });
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de supprimer le produit du transfert."
      });
    }
  });
};