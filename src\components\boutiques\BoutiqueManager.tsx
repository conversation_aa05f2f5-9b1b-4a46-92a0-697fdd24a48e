import { useState } from "react";
import { Plus, Search, Filter, MoreHorizontal, MapPin, Phone, Mail, Users, Eye, Edit, Trash2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { ErrorFallback } from "@/components/ui/error-fallback";
import { BoutiqueForm } from "@/components/forms/BoutiqueForm";
import { BoutiqueDetail } from "./BoutiqueDetail";
import { useBoutiques, useDeleteBoutique, type Boutique } from "@/hooks/useBoutiques";
import { useEmployes } from "@/hooks/useEmployes";
import { useToast } from "@/hooks/use-toast";

export const BoutiqueManager = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedType, setSelectedType] = useState<"all" | "centrale" | "boutique">("all");
  const [selectedBoutique, setSelectedBoutique] = useState<Boutique | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);
  const [boutiqueToEdit, setBoutiqueToEdit] = useState<Boutique | null>(null);

  const { data: boutiques, isLoading, error, refetch } = useBoutiques();
  const { data: employes } = useEmployes();
  const { mutate: deleteBoutique, isPending: isDeleting } = useDeleteBoutique();
  const { toast } = useToast();

  // Filtrage des boutiques
  const filteredBoutiques = boutiques?.filter(boutique => {
    const matchesSearch = boutique.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         boutique.adresse.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === "all" || boutique.type === selectedType;
    return matchesSearch && matchesType;
  }) || [];

  // Groupement par type
  const centrales = filteredBoutiques.filter(b => b.type === "centrale");
  const boutiquesLocal = filteredBoutiques.filter(b => b.type === "boutique");

  const handleDelete = (boutique: Boutique) => {
    deleteBoutique(boutique.id, {
      onSuccess: () => {
        toast({
          title: "Boutique supprimée",
          description: `${boutique.nom} a été supprimée avec succès.`
        });
      }
    });
  };

  const handleEdit = (boutique: Boutique) => {
    setBoutiqueToEdit(boutique);
    setIsEditDialogOpen(true);
  };

  const handleView = (boutique: Boutique) => {
    setSelectedBoutique(boutique);
    setIsDetailDialogOpen(true);
  };

  const getEmployeeCount = (boutiqueId: string) => {
    return employes?.filter(emp => emp.boutique_id === boutiqueId && emp.statut === 'actif').length || 0;
  };

  const getStatusBadge = (statut: string) => {
    const variants = {
      active: "default",
      inactive: "secondary", 
      maintenance: "destructive"
    } as const;
    
    const labels = {
      active: "Active",
      inactive: "Inactive",
      maintenance: "Maintenance"
    };

    return (
      <Badge variant={variants[statut as keyof typeof variants] || "secondary"}>
        {labels[statut as keyof typeof labels] || statut}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return <ErrorFallback error={error} resetError={() => refetch()} />;
  }

  const BoutiqueCard = ({ boutique }: { boutique: any }) => (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <CardTitle className="text-lg flex items-center gap-2">
              {boutique.nom}
              {boutique.type === "centrale" && <Badge variant="outline">CENTRALE</Badge>}
            </CardTitle>
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              {getStatusBadge(boutique.statut)}
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                {getEmployeeCount(boutique.id)} employés
              </div>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleView(boutique)}>
                <Eye className="h-4 w-4 mr-2" />
                Voir détails
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleEdit(boutique)}>
                <Edit className="h-4 w-4 mr-2" />
                Modifier
              </DropdownMenuItem>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Supprimer
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Confirmer la suppression</AlertDialogTitle>
                    <AlertDialogDescription>
                      Êtes-vous sûr de vouloir supprimer "{boutique.nom}" ? Cette action est irréversible.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Annuler</AlertDialogCancel>
                    <AlertDialogAction 
                      onClick={() => handleDelete(boutique)}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      Supprimer
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-start gap-2 text-sm">
          <MapPin className="h-4 w-4 mt-0.5 text-muted-foreground flex-shrink-0" />
          <span className="text-muted-foreground">{boutique.adresse}</span>
        </div>
        {boutique.telephone && (
          <div className="flex items-center gap-2 text-sm">
            <Phone className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">{boutique.telephone}</span>
          </div>
        )}
        {boutique.email && (
          <div className="flex items-center gap-2 text-sm">
            <Mail className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">{boutique.email}</span>
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Gestion des Boutiques</h1>
          <p className="text-muted-foreground">
            Gérez votre réseau de boutiques - {filteredBoutiques.length} boutique(s)
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Nouvelle Boutique
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Créer une nouvelle boutique</DialogTitle>
            </DialogHeader>
            <BoutiqueForm 
              onSuccess={() => setIsCreateDialogOpen(false)}
              onCancel={() => setIsCreateDialogOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Barre de recherche et filtres */}
      <div className="flex gap-4 items-center">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Rechercher une boutique..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              {selectedType === "all" ? "Tous les types" : 
               selectedType === "centrale" ? "Centrales" : "Boutiques"}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => setSelectedType("all")}>
              Tous les types
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setSelectedType("centrale")}>
              Centrales uniquement
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setSelectedType("boutique")}>
              Boutiques uniquement
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        <Button variant="outline" onClick={() => refetch()}>
          Actualiser
        </Button>
      </div>

      {/* Liste des centrales */}
      {centrales.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            Boutiques Centrales ({centrales.length})
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {centrales.map(boutique => (
              <BoutiqueCard key={boutique.id} boutique={boutique} />
            ))}
          </div>
        </div>
      )}

      {/* Liste des boutiques */}
      {boutiquesLocal.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            Boutiques Locales ({boutiquesLocal.length})
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {boutiquesLocal.map(boutique => (
              <BoutiqueCard key={boutique.id} boutique={boutique} />
            ))}
          </div>
        </div>
      )}

      {/* Message si aucune boutique */}
      {filteredBoutiques.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <div className="space-y-4">
              <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                <Filter className="h-6 w-6 text-muted-foreground" />
              </div>
              <div>
                <h3 className="font-semibold">Aucune boutique trouvée</h3>
                <p className="text-muted-foreground">
                  {searchTerm ? "Essayez avec d'autres termes de recherche" : "Commencez par créer votre première boutique"}
                </p>
              </div>
              {!searchTerm && (
                <Button onClick={() => setIsCreateDialogOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Créer une boutique
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Dialog de modification */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Modifier la boutique</DialogTitle>
          </DialogHeader>
          {boutiqueToEdit && (
            <BoutiqueForm 
              boutique={boutiqueToEdit}
              onSuccess={() => {
                setIsEditDialogOpen(false);
                setBoutiqueToEdit(null);
              }}
              onCancel={() => {
                setIsEditDialogOpen(false);
                setBoutiqueToEdit(null);
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Dialog de détail */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Détails de la boutique</DialogTitle>
          </DialogHeader>
          {selectedBoutique && (
            <BoutiqueDetail 
              boutique={selectedBoutique}
              onClose={() => {
                setIsDetailDialogOpen(false);
                setSelectedBoutique(null);
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};