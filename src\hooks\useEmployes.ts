import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface Employe {
  id: string;
  nom: string;
  prenom: string;
  email: string;
  telephone?: string;
  poste: string;
  boutique_id?: string;
  salaire?: number;
  statut: 'actif' | 'inactif' | 'suspendu';
  date_embauche: string;
  created_at: string;
  updated_at: string;
  boutiques?: {
    nom: string;
  };
}

export const useEmployes = (boutiqueId?: string) => {
  return useQuery({
    queryKey: ['employes', boutiqueId],
    queryFn: async () => {
      console.log('🔍 [useEmployes] Fetching employees with boutiqueId:', boutiqueId);
      
      try {
        let query = supabase
          .from('employes')
          .select(`
            *,
            boutiques:boutique_id(nom)
          `);
        
        if (boutiqueId) {
          query = query.eq('boutique_id', boutiqueId);
        }
        
        const { data, error } = await query.order('nom');
        
        console.log('📊 [useEmployes] Raw response:', { data, error });
        
        if (error) {
          console.error('❌ [useEmployes] Database error:', error);
          throw error;
        }
        
        // Ensure data is properly formatted and has all required fields
        const formattedData = (data || []).map(employe => ({
          ...employe,
          // Ensure all required fields exist
          nom: employe.nom || '',
          prenom: employe.prenom || '',
          email: employe.email || '',
          poste: employe.poste || '',
          statut: employe.statut || 'inactif',
          date_embauche: employe.date_embauche || new Date().toISOString(),
          created_at: employe.created_at || new Date().toISOString(),
          updated_at: employe.updated_at || new Date().toISOString(),
        }));
        
        console.log('✅ [useEmployes] Success:', formattedData.length, 'employees found');
        return formattedData as Employe[];
      } catch (err) {
        console.error('💥 [useEmployes] Unexpected error:', err);
        throw err;
      }
    },
    // Employés changent peu, cache plus long
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 20 * 60 * 1000,    // 20 minutes
  });
};

export const useCreateEmploye = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (employe: Omit<Employe, 'id' | 'created_at' | 'updated_at'>) => {
      console.log('🔄 [useCreateEmploye] Creating employee:', employe);
      
      const { data, error } = await supabase
        .from('employes')
        .insert(employe)
        .select()
        .single();
      
      if (error) {
        console.error('❌ [useCreateEmploye] Database error:', error);
        throw error;
      }
      
      console.log('✅ [useCreateEmploye] Employee created:', data);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employes'] });
      toast({
        title: "Employé créé",
        description: "Le nouvel employé a été créé avec succès."
      });
    },
    onError: (error: any) => {
      console.error('💥 [useCreateEmploye] Creation failed:', error);
      let errorMessage = "Impossible de créer l'employé.";
      
      if (error?.code === '23505') {
        errorMessage = "Un employé avec cette adresse email existe déjà.";
      } else if (error?.code === '23503') {
        errorMessage = "La boutique sélectionnée n'existe pas.";
      } else if (error?.message) {
        errorMessage = `Erreur: ${error.message}`;
      }
      
      toast({
        variant: "destructive",
        title: "Erreur",
        description: errorMessage
      });
    }
  });
};

export const useUpdateEmploye = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, ...employe }: Partial<Employe> & { id: string }) => {
      const { data, error } = await supabase
        .from('employes')
        .update(employe)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employes'] });
      toast({
        title: "Employé modifié",
        description: "Les informations de l'employé ont été mises à jour."
      });
    },
    onError: (error) => {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de modifier l'employé."
      });
    }
  });
};

export const useDeleteEmploye = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('employes')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employes'] });
      toast({
        title: "Employé supprimé",
        description: "L'employé a été supprimé avec succès."
      });
    },
    onError: (error) => {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de supprimer l'employé."
      });
    }
  });
};

export const useEmployeStats = () => {
  return useQuery({
    queryKey: ['employe-stats'],
    queryFn: async () => {
      const { data: employes, error } = await supabase
        .from('employes')
        .select('statut, salaire, date_embauche, boutique_id');
      
      if (error) throw error;
      
      const total = employes.length;
      const actifs = employes.filter(e => e.statut === 'actif').length;
      const inactifs = employes.filter(e => e.statut === 'inactif').length;
      const suspendus = employes.filter(e => e.statut === 'suspendu').length;
      
      const salaireMoyen = employes
        .filter(e => e.salaire)
        .reduce((acc, e) => acc + (e.salaire || 0), 0) / employes.filter(e => e.salaire).length || 0;
      
      return {
        total,
        actifs,
        inactifs,
        suspendus,
        salaireMoyen: Math.round(salaireMoyen)
      };
    },
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000,    // 30 minutes
  });
};