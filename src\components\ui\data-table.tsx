import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { LoadingTable } from '@/components/ui/loading-spinner';
import { ErrorFallback, EmptyStateFallback } from '@/components/ui/error-fallback';
import { Search, RefreshCw, Filter } from 'lucide-react';

interface Column<T> {
  key: keyof T | string;
  label: string;
  render?: (value: any, row: T) => React.ReactNode;
  sortable?: boolean;
  width?: string;
}

interface DataTableProps<T> {
  data: T[] | undefined;
  columns: Column<T>[];
  isLoading?: boolean;
  error?: Error | null;
  onRefresh?: () => void;
  title?: string;
  subtitle?: string;
  searchable?: boolean;
  searchPlaceholder?: string;
  onSearch?: (query: string) => void;
  searchValue?: string;
  emptyMessage?: string;
  emptyDescription?: string;
  actions?: React.ReactNode;
  className?: string;
}

export function DataTable<T extends Record<string, any>>({
  data,
  columns,
  isLoading = false,
  error = null,
  onRefresh,
  title,
  subtitle,
  searchable = false,
  searchPlaceholder = "Rechercher...",
  onSearch,
  searchValue = "",
  emptyMessage = "Aucune donnée disponible",
  emptyDescription = "Il n'y a actuellement aucune donnée à afficher",
  actions,
  className = ""
}: DataTableProps<T>) {

  if (isLoading) {
    return (
      <Card className={className}>
        {(title || subtitle || actions) && (
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                {title && <CardTitle>{title}</CardTitle>}
                {subtitle && <p className="text-sm text-muted-foreground">{subtitle}</p>}
              </div>
              {actions}
            </div>
          </CardHeader>
        )}
        <CardContent>
          <LoadingTable />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        {(title || subtitle || actions) && (
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                {title && <CardTitle>{title}</CardTitle>}
                {subtitle && <p className="text-sm text-muted-foreground">{subtitle}</p>}
              </div>
              {actions}
            </div>
          </CardHeader>
        )}
        <CardContent>
          <ErrorFallback
            error={error}
            resetError={onRefresh}
            title="Erreur de chargement des données"
            description="Impossible de charger les données du tableau"
          />
        </CardContent>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card className={className}>
        {(title || subtitle || actions) && (
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                {title && <CardTitle>{title}</CardTitle>}
                {subtitle && <p className="text-sm text-muted-foreground">{subtitle}</p>}
              </div>
              {actions}
            </div>
          </CardHeader>
        )}
        <CardContent>
          <EmptyStateFallback
            title={emptyMessage}
            description={emptyDescription}
          />
        </CardContent>
      </Card>
    );
  }

  const getValue = (row: T, key: string): any => {
    if (key.includes('.')) {
      return key.split('.').reduce((obj, k) => obj?.[k], row);
    }
    return row[key];
  };

  return (
    <Card className={className}>
      {(title || subtitle || searchable || actions) && (
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              {title && <CardTitle>{title}</CardTitle>}
              {subtitle && <p className="text-sm text-muted-foreground">{subtitle}</p>}
            </div>
            <div className="flex items-center gap-2">
              {searchable && onSearch && (
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder={searchPlaceholder}
                    value={searchValue}
                    onChange={(e) => onSearch(e.target.value)}
                    className="pl-8 w-64"
                  />
                </div>
              )}
              {onRefresh && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onRefresh}
                  className="h-9"
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              )}
              {actions}
            </div>
          </div>
        </CardHeader>
      )}
      
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                {columns.map((column, index) => (
                  <TableHead 
                    key={index} 
                    style={{ width: column.width }}
                    className={column.sortable ? "cursor-pointer hover:bg-muted/50" : ""}
                  >
                    <div className="flex items-center gap-2">
                      {column.label}
                      {column.sortable && (
                        <Filter className="h-3 w-3 text-muted-foreground" />
                      )}
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.map((row, rowIndex) => (
                <TableRow key={rowIndex} className="hover:bg-muted/50">
                  {columns.map((column, colIndex) => {
                    const value = getValue(row, column.key as string);
                    return (
                      <TableCell key={colIndex}>
                        {column.render ? column.render(value, row) : value}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        
        {data.length > 0 && (
          <div className="flex items-center justify-between pt-4">
            <p className="text-sm text-muted-foreground">
              {data.length} élément{data.length > 1 ? 's' : ''} affiché{data.length > 1 ? 's' : ''}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}