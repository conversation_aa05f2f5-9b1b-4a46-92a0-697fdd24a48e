import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useCreateProduit, useUpdateProduit, type ProduitWithCategory } from '@/hooks/useProduits';
import { useCategories } from '@/hooks/useCategories';
import { produitSchema, type ProduitFormData } from '@/lib/validations';
import { useEffect } from 'react';

interface ProduitFormProps {
  produit?: ProduitWithCategory | null;
  onClose: () => void;
}

export const ProduitForm = ({ produit, onClose }: ProduitFormProps) => {
  const { data: categories = [] } = useCategories();
  const createProduit = useCreateProduit();
  const updateProduit = useUpdateProduit();

  const form = useForm<ProduitFormData>({
    resolver: zodResolver(produitSchema),
    defaultValues: {
      nom: '',
      marque: '',
      modele: '',
      code_produit: '',
      prix_achat: 0,
      prix_vente: 0,
      couleur: '',
      stockage: '',
      description: '',
      etat: 'neuf',
      categorie_id: '',
      imei: '',
    },
  });

  // Charger les données du produit à modifier
  useEffect(() => {
    if (produit) {
      form.reset({
        nom: produit.nom,
        marque: produit.marque,
        modele: produit.modele,
        code_produit: produit.code_produit,
        prix_achat: produit.prix_achat,
        prix_vente: produit.prix_vente,
        couleur: produit.couleur || '',
        stockage: produit.stockage || '',
        description: produit.description || '',
        etat: produit.etat as 'neuf' | 'occasion' | 'defectueux',
        categorie_id: produit.categorie?.id || '',
        imei: produit.imei || '',
      });
    }
  }, [produit, form]);

  const onSubmit = async (data: ProduitFormData) => {
    try {
      if (produit) {
        await updateProduit.mutateAsync({ id: produit.id, data });
      } else {
        await createProduit.mutateAsync(data);
      }
      onClose();
    } catch (error) {
      // L'erreur est déjà gérée dans les hooks
    }
  };

  // Générer automatiquement le code produit
  const generateCode = () => {
    const marque = form.getValues('marque');
    const modele = form.getValues('modele');
    if (marque && modele) {
      const code = `${marque.slice(0, 3).toUpperCase()}-${modele.slice(0, 3).toUpperCase()}-${Date.now().toString().slice(-4)}`;
      form.setValue('code_produit', code);
    }
  };

  // Calculer la marge automatiquement
  const prixAchat = form.watch('prix_achat');
  const prixVente = form.watch('prix_vente');
  const marge = prixAchat > 0 ? ((prixVente - prixAchat) / prixAchat * 100) : 0;

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Informations de base */}
        <Card>
          <CardHeader>
            <CardTitle>Informations générales</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="nom"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nom du produit *</FormLabel>
                  <FormControl>
                    <Input placeholder="iPhone 15 Pro Max" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="code_produit"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Code produit *</FormLabel>
                  <div className="flex space-x-2">
                    <FormControl>
                      <Input placeholder="APP-IPH-1234" {...field} />
                    </FormControl>
                    <Button 
                      type="button" 
                      variant="outline" 
                      size="sm"
                      onClick={generateCode}
                    >
                      Générer
                    </Button>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="marque"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Marque *</FormLabel>
                  <FormControl>
                    <Input placeholder="Apple" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="modele"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Modèle *</FormLabel>
                  <FormControl>
                    <Input placeholder="iPhone 15 Pro Max" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="categorie_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Catégorie</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionner une catégorie" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.nom}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="etat"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>État *</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="neuf">Neuf</SelectItem>
                      <SelectItem value="occasion">Occasion</SelectItem>
                      <SelectItem value="defectueux">Défectueux</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Caractéristiques */}
        <Card>
          <CardHeader>
            <CardTitle>Caractéristiques</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FormField
              control={form.control}
              name="couleur"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Couleur</FormLabel>
                  <FormControl>
                    <Input placeholder="Noir" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="stockage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Stockage</FormLabel>
                  <FormControl>
                    <Input placeholder="256 GB" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="imei"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>IMEI/Numéro série</FormLabel>
                  <FormControl>
                    <Input placeholder="123456789012345" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Prix */}
        <Card>
          <CardHeader>
            <CardTitle>Tarification</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="prix_achat"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Prix d'achat * (XAF)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="0"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="prix_vente"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Prix de vente * (XAF)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="0"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Affichage de la marge */}
            {prixAchat > 0 && prixVente > 0 && (
              <div className="p-4 bg-muted rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Marge bénéficiaire:</span>
                  <span className={`font-medium ${marge > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {marge.toFixed(2)}% ({prixVente - prixAchat} XAF)
                  </span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Description */}
        <Card>
          <CardHeader>
            <CardTitle>Description</CardTitle>
          </CardHeader>
          <CardContent>
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description du produit</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Description détaillée du produit..."
                      rows={4}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={onClose}>
            Annuler
          </Button>
          <Button 
            type="submit" 
            disabled={createProduit.isPending || updateProduit.isPending}
          >
            {produit ? 'Mettre à jour' : 'Créer le produit'}
          </Button>
        </div>
      </form>
    </Form>
  );
};