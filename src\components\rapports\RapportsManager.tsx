import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger, DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { 
  BarChart3, 
  Package, 
  TrendingUp, 
  DollarSign, 
  ArrowLeft,
  Download,
  Calendar,
  Filter,
  MoreVertical
} from 'lucide-react';
import { VentesReports } from './VentesReports';
import { StockReports } from './StockReports';
import { PerformanceReports } from './PerformanceReports';
import { FinancialReports } from './FinancialReports';
import { ReportsFilters } from './ReportsFilters';
import { useIsMobile } from '@/hooks/use-mobile';

export const RapportsManager = () => {
  const [activeTab, setActiveTab] = useState('ventes');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    dateDebut: '',
    dateFin: '',
    boutiqueId: '',
    employeId: '',
    categorieId: ''
  });
  const isMobile = useIsMobile();

  const tabs = [
    {
      id: 'ventes',
      label: 'Ventes',
      icon: TrendingUp,
      description: 'Chiffre d\'affaires et tendances'
    },
    {
      id: 'stock',
      label: 'Stock',
      icon: Package,
      description: 'Rotation et valorisation'
    },
    {
      id: 'performance',
      label: 'Performance',
      icon: BarChart3,
      description: 'Boutiques et employés'
    },
    {
      id: 'financial',
      label: 'Financier',
      icon: DollarSign,
      description: 'Marges et rentabilité'
    }
  ];

  const FiltersComponent = () => (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Filtres Avancés</CardTitle>
      </CardHeader>
      <CardContent>
        <ReportsFilters filters={filters} onFiltersChange={setFilters} />
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Header avec navigation et actions */}
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button 
              variant="ghost" 
              size={isMobile ? "sm" : "default"}
              onClick={() => window.history.back()} 
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              {!isMobile && "Retour"}
            </Button>
            <div>
              <h1 className={`font-bold text-foreground flex items-center gap-2 ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                <BarChart3 className={`text-primary ${isMobile ? 'h-5 w-5' : 'h-6 w-6'}`} />
                {isMobile ? "Rapports" : "Rapports & Analyses"}
              </h1>
              {!isMobile && (
                <p className="text-muted-foreground text-sm">
                  Analyses avancées et rapports détaillés pour optimiser vos performances
                </p>
              )}
            </div>
          </div>

          {isMobile ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setShowFilters(!showFilters)}>
                  <Filter className="h-4 w-4 mr-2" />
                  Filtres
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Calendar className="h-4 w-4 mr-2" />
                  Période
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Download className="h-4 w-4 mr-2" />
                  Exporter
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                Filtres
              </Button>
              <Button variant="outline" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Période
              </Button>
              <Button className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                Exporter
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Filtres repliables */}
      {isMobile ? (
        <Sheet open={showFilters} onOpenChange={setShowFilters}>
          <SheetContent side="bottom" className="h-[80vh]">
            <div className="py-6">
              <h3 className="text-lg font-semibold mb-4">Filtres Avancés</h3>
              <ReportsFilters filters={filters} onFiltersChange={setFilters} />
            </div>
          </SheetContent>
        </Sheet>
      ) : (
        showFilters && <FiltersComponent />
      )}

      {/* Onglets principaux */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className={`grid w-full grid-cols-4 ${isMobile ? 'h-auto' : ''}`}>
          {tabs.map((tab) => (
            <TabsTrigger 
              key={tab.id} 
              value={tab.id} 
              className={`flex flex-col items-center gap-1 ${isMobile ? 'p-2' : 'p-3'}`}
            >
              <tab.icon className="h-4 w-4" />
              <span className={`font-medium ${isMobile ? 'text-xs' : 'text-sm'}`}>{tab.label}</span>
              {!isMobile && (
                <span className="text-xs text-muted-foreground hidden sm:block">
                  {tab.description}
                </span>
              )}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value="ventes" className="space-y-6">
          <VentesReports filters={filters} />
        </TabsContent>

        <TabsContent value="stock" className="space-y-6">
          <StockReports filters={filters} />
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <PerformanceReports filters={filters} />
        </TabsContent>

        <TabsContent value="financial" className="space-y-6">
          <FinancialReports filters={filters} />
        </TabsContent>
      </Tabs>
    </div>
  );
};