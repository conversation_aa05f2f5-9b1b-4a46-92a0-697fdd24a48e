import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { LoadingCard } from "@/components/ui/loading-spinner";
import { ErrorFallback, EmptyStateFallback } from '@/components/ui/error-fallback';
import { 
  Store, 
  Package, 
  AlertTriangle, 
  TrendingUp, 
  Banknote,
  Users,
  ArrowLeftRight,
  ShoppingCart
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useBoutiques } from "@/hooks/useBoutiques";
import { useStocks } from "@/hooks/useStocks";
import { useVentes } from "@/hooks/useVentes";
import { useAlertes } from "@/hooks/useAlertes";
import { useTransferts } from "@/hooks/useTransferts";
import { useEmployes } from "@/hooks/useEmployes";
import { formatCurrency, formatNumber, getCurrentIvorianTime } from "@/lib/formatters";


export const StatsCards = () => {
  const { data: boutiques, isLoading: boutiquesLoading, error: boutiquesError, refetch: refetchBoutiques } = useBoutiques();
  const { data: stocks, isLoading: stocksLoading, error: stocksError } = useStocks();
  const { data: ventes, isLoading: ventesLoading, error: ventesError } = useVentes(undefined, getCurrentIvorianTime().toISOString().split('T')[0], getCurrentIvorianTime().toISOString().split('T')[0]);
  const { data: alertes, isLoading: alertesLoading, error: alertesError } = useAlertes(undefined, true);
  const { data: transferts, isLoading: transfertsLoading, error: transfertsError } = useTransferts();
  const { data: employes } = useEmployes();

  // Calcul du CA mensuel depuis la base de données
  const currentDate = getCurrentIvorianTime();
  const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1).toISOString().split('T')[0];
  const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).toISOString().split('T')[0];
  const { data: ventesMonth } = useVentes(undefined, startOfMonth, endOfMonth);

  const isLoading = boutiquesLoading || stocksLoading || ventesLoading || alertesLoading || transfertsLoading;
  const hasError = boutiquesError || stocksError || ventesError || alertesError || transfertsError;

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <LoadingCard key={index} />
        ))}
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="grid grid-cols-1 gap-4">
        <ErrorFallback 
          error={boutiquesError || stocksError || ventesError || alertesError || transfertsError} 
          resetError={() => refetchBoutiques()} 
          title="Erreur de chargement des statistiques"
          description="Impossible de charger les données du tableau de bord"
        />
      </div>
    );
  }

  if (!boutiques || boutiques.length === 0) {
    return (
      <EmptyStateFallback
        title="Aucune boutique configurée"
        description="Créez votre première boutique pour voir les statistiques du tableau de bord"
        action={{
          label: "Créer une boutique",
          onClick: () => window.location.href = '/boutiques'
        }}
      />
    );
  }

  // Calculs dynamiques avec des valeurs par défaut sécurisées
  const boutiquesActives = boutiques?.filter(b => b.statut === 'active').length ?? 0;
  const totalCentrale = boutiques?.filter(b => b.type === 'centrale').length ?? 0;
  const totalBoutiques = boutiques?.filter(b => b.type === 'boutique').length ?? 0;
  const totalProduits = stocks?.reduce((sum, stock) => sum + stock.quantite, 0) ?? 0;
  const stocksBas = stocks?.filter(stock => stock.quantite <= stock.seuil_alerte).length ?? 0;
  const ventesToday = ventes?.reduce((sum, vente) => sum + vente.montant_total, 0) ?? 0;
  const alertesNonLues = alertes?.length ?? 0;
  const transactionsToday = ventes?.length ?? 0;
  const transfertsEnCours = transferts?.filter(t => ['en_attente', 'expedie', 'en_transit'].includes(t.statut)).length ?? 0;
  const caMonthly = ventesMonth?.reduce((sum, vente) => sum + vente.montant_total, 0) ?? 0;
  const employesActifs = employes?.filter(e => e.statut === 'actif').length ?? 0;

  const statsData = [
    {
      title: "Boutiques Actives",
      value: boutiquesActives.toString(),
      description: `${totalCentrale} centrale + ${totalBoutiques} boutiques`,
      icon: Store,
      color: "text-primary",
      bgColor: "bg-primary/10"
    },
    {
      title: "Produits en Stock",
      value: formatNumber(totalProduits),
      description: "+12% ce mois",
      icon: Package,
      color: "text-success",
      bgColor: "bg-success/10"
    },
    {
      title: "Alertes Stock Bas",
      value: stocksBas.toString(),
      description: "Nécessite réapprovisionnement",
      icon: AlertTriangle,
      color: "text-warning",
      bgColor: "bg-warning/10"
    },
    {
      title: "Ventes Aujourd'hui",
      value: formatCurrency(ventesToday),
      description: "+18% vs hier",
      icon: Banknote,
      color: "text-info",
      bgColor: "bg-info/10"
    },
    {
      title: "Employés Actifs",
      value: employesActifs.toString(),
      description: "Vendeurs & caissiers",
      icon: Users,
      color: "text-muted-foreground",
      bgColor: "bg-muted"
    },
    {
      title: "Transferts en Cours",
      value: transfertsEnCours.toString(),
      description: "Entre boutiques",
      icon: ArrowLeftRight,
      color: "text-primary",
      bgColor: "bg-primary/10"
    },
    {
      title: "Transactions Jour",
      value: transactionsToday.toString(),
      description: "Toutes boutiques",
      icon: ShoppingCart,
      color: "text-success",
      bgColor: "bg-success/10"
    },
    {
      title: "CA Mensuel",
      value: formatCurrency(caMonthly),
      description: `${ventesMonth?.length ?? 0} transactions ce mois`,
      icon: TrendingUp,
      color: "text-info",
      bgColor: "bg-info/10"
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {statsData.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {stat.title}
              </CardTitle>
              <div className={`h-8 w-8 rounded-lg ${stat.bgColor} flex items-center justify-center`}>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground mt-1">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};