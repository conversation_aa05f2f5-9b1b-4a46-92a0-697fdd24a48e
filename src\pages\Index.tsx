import { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { usePerformanceOptimizer, useRealtimeSync } from "@/hooks/usePerformanceOptimizer";
import { Header } from "@/components/layout/Header";
import { Sidebar } from "@/components/layout/Sidebar";
import { PlaceholderPage } from "@/components/pages/PlaceholderPage";
import { Dashboard } from "@/components/dashboard/Dashboard";
import { BoutiqueManager } from "@/components/boutiques/BoutiqueManager";
import { StockManager } from "@/components/stocks/StockManager";
import { InventaireManager } from "@/components/inventaire/InventaireManager";
import { VenteManager } from "@/components/ventes/VenteManager";
import { TransfertManager } from "@/components/transferts/TransfertManager";
import { ProduitManager } from "@/components/produits/ProduitManager";
import { RapportsManager } from "@/components/rapports/RapportsManager";
import { AlerteManager } from "@/components/alertes/AlerteManager";
import { UserManagement } from '@/components/admin/UserManagement';
import { AdminGuide } from '@/components/admin/AdminGuide';
import { EmployeManager } from '@/components/employes/EmployeManager';
import { 
  Store, 
  Package, 
  ArrowLeftRight, 
  ClipboardList, 
  ShoppingCart, 
  Users, 
  BarChart3,
  Settings,
  Smartphone,
  AlertTriangle
} from "lucide-react";

const pageIcons = {
  boutiques: Store,
  stock: Package,
  transferts: ArrowLeftRight,
  ventes: ShoppingCart,
  produits: Smartphone,
  employes: Users,
  inventaire: ClipboardList,
  rapports: BarChart3,
  alertes: AlertTriangle,
  parametres: Settings
};

const Index = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(true);

  // Optimisation des performances et synchronisation temps réel
  const { queryClient } = usePerformanceOptimizer();
  useRealtimeSync(queryClient);

  const AdminPage = () => (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Administration Système</h1>
        <p className="text-muted-foreground">Gestion des utilisateurs et configuration avancée</p>
      </div>
      <AdminGuide />
      <UserManagement />
    </div>
  );

  const renderContent = () => {
    switch (location.pathname) {
      case "/stocks":
        return <StockManager />;
      case "/ventes":
        return <VenteManager />;
      case "/transferts":
        return <TransfertManager />;
      case "/alertes":
        return <AlerteManager />;
      case "/boutiques":
        return <BoutiqueManager />;
      case "/produits":
        return <ProduitManager />;
      case "/inventaire":
        return <InventaireManager />;
      case "/employes":
        return <EmployeManager />;
      case "/admin":
        return (
          <div className="space-y-6">
            <AdminGuide />
            <UserManagement />
          </div>
        );
      case "/parametres":
        return <PlaceholderPage 
          title="Paramètres" 
          description="Module de paramètres en cours de développement." 
          icon={Settings}
          onBack={() => window.history.back()}
        />;
      case "/rapports":
        return <RapportsManager />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header onMenuToggle={() => setSidebarOpen(!sidebarOpen)} />
      <div className="flex">
        <Sidebar 
          isOpen={sidebarOpen}
          currentPage={location.pathname === "/" ? "dashboard" : location.pathname.substring(1)}
          onPageChange={(page) => {
            if (page === "dashboard") {
              navigate("/");
            } else {
              navigate(`/${page}`);
            }
          }}
        />
        <main className="flex-1 p-6">
          {renderContent()}
        </main>
      </div>
    </div>
  );
};

export default Index;