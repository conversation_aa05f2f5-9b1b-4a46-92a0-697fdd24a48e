import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAlertes, useMarquerAlerteLue } from '@/hooks/useAlertes';
import { useBoutiques } from '@/hooks/useBoutiques';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertTriangle, Info, CheckCircle, XCircle, Calendar, Check } from 'lucide-react';
import { format } from 'date-fns';
import { formatDateTime } from '@/lib/formatters';
import { fr } from 'date-fns/locale';

export const AlerteManager = () => {
  const [selectedBoutique, setSelectedBoutique] = useState<string>('');
  const [showOnlyUnread, setShowOnlyUnread] = useState<boolean>(true);

  const { data: boutiques } = useBoutiques();
  const { data: alertes, isLoading } = useAlertes(selectedBoutique === 'all' ? undefined : selectedBoutique, showOnlyUnread);
  const marquerLue = useMarquerAlerteLue();

  const getNiveauIcon = (niveau: string) => {
    switch (niveau) {
      case 'error': return <XCircle className="h-4 w-4" />;
      case 'warning': return <AlertTriangle className="h-4 w-4" />;
      case 'success': return <CheckCircle className="h-4 w-4" />;
      case 'info': return <Info className="h-4 w-4" />;
      default: return <Info className="h-4 w-4" />;
    }
  };

  const getNiveauVariant = (niveau: string) => {
    switch (niveau) {
      case 'error': return 'destructive';
      case 'warning': return 'secondary';
      case 'success': return 'default';
      case 'info': return 'outline';
      default: return 'secondary';
    }
  };

  const getTypeLabel = (type: string) => {
    const labels = {
      'stock_bas': 'Stock Bas',
      'stock_epuise': 'Stock Épuisé',
      'transfert_retard': 'Transfert en Retard',
      'vente_importante': 'Vente Importante',
      'systeme': 'Système'
    };
    return labels[type as keyof typeof labels] || type;
  };

  const handleMarquerLue = async (alerteId: string) => {
    await marquerLue.mutateAsync(alerteId);
  };

  if (isLoading) return <div>Chargement...</div>;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Centre d'Alertes</h1>
        <div className="flex space-x-4">
          <Button
            variant={showOnlyUnread ? "default" : "outline"}
            onClick={() => setShowOnlyUnread(!showOnlyUnread)}
          >
            {showOnlyUnread ? "Toutes les alertes" : "Non lues seulement"}
          </Button>
          <Select value={selectedBoutique} onValueChange={setSelectedBoutique}>
            <SelectTrigger className="w-64">
              <SelectValue placeholder="Toutes les boutiques" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Toutes les boutiques</SelectItem>
              {boutiques?.map((boutique) => (
                <SelectItem key={boutique.id} value={boutique.id}>
                  {boutique.nom}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Statistiques des alertes */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {['error', 'warning', 'info', 'success'].map((niveau) => {
          const count = alertes?.filter(a => a.niveau === niveau && !a.lu).length || 0;
          return (
            <Card key={niveau}>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  {getNiveauIcon(niveau)}
                  <div>
                    <div className="text-2xl font-bold">{count}</div>
                    <div className="text-sm text-muted-foreground capitalize">
                      {niveau === 'error' ? 'Erreurs' : 
                       niveau === 'warning' ? 'Avertissements' :
                       niveau === 'info' ? 'Informations' : 'Succès'}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Liste des alertes */}
      <div className="grid gap-4">
        {alertes?.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <CheckCircle className="h-12 w-12 text-success mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Aucune alerte</h3>
              <p className="text-muted-foreground">
                {showOnlyUnread ? "Toutes les alertes ont été lues !" : "Aucune alerte pour le moment."}
              </p>
            </CardContent>
          </Card>
        ) : (
          alertes?.map((alerte) => (
            <Card 
              key={alerte.id} 
              className={`hover:shadow-md transition-shadow ${!alerte.lu ? 'border-l-4 border-l-primary' : ''}`}
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className={`flex items-center justify-center w-10 h-10 rounded-lg ${
                      alerte.niveau === 'error' ? 'bg-destructive/10' :
                      alerte.niveau === 'warning' ? 'bg-warning/10' :
                      alerte.niveau === 'success' ? 'bg-success/10' : 'bg-muted'
                    }`}>
                      {getNiveauIcon(alerte.niveau)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="font-semibold">{alerte.titre}</h3>
                        <Badge variant={getNiveauVariant(alerte.niveau)}>
                          {getTypeLabel(alerte.type)}
                        </Badge>
                        {!alerte.lu && (
                          <Badge variant="outline" className="text-xs">Nouveau</Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        {alerte.message}
                      </p>
                      <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3" />
                          <span>
                            {formatDateTime(alerte.created_at)}
                          </span>
                        </div>
                        {alerte.boutiques && (
                          <span>• {alerte.boutiques.nom}</span>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {!alerte.lu && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleMarquerLue(alerte.id)}
                        disabled={marquerLue.isPending}
                      >
                        <Check className="h-4 w-4 mr-1" />
                        Marquer lu
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};