import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import type { ProduitFormData, SearchFilters } from '@/lib/validations';

// Types pour les produits avec relations
export interface ProduitWithCategory {
  id: string;
  nom: string;
  marque: string;
  modele: string;
  code_produit: string;
  prix_achat: number;
  prix_vente: number;
  couleur?: string;
  stockage?: string;
  description?: string;
  etat: string;
  imei?: string;
  created_at: string;
  updated_at: string;
  categorie?: {
    id: string;
    nom: string;
  };
  stocks?: Array<{
    id: string;
    quantite: number;
    seuil_alerte: number;
    emplacement?: string;
    boutique?: {
      id: string;
      nom: string;
    };
  }>;
  _count?: {
    stocks: number;
    total_quantity: number;
  } | null;
}

export const useProduits = (filters?: SearchFilters) => {
  return useQuery({
    queryKey: ['produits', filters],
    queryFn: async () => {
      console.log('🔍 [useProduits] Fetching products with filters:', filters);
      
      try {
        let query = supabase
          .from('produits')
          .select(`
            *,
            categories:categorie_id (
              id,
              nom
            )
          `)
          .order('created_at', { ascending: false });

        // Appliquer les filtres
        if (filters?.search) {
          query = query.or(`nom.ilike.%${filters.search}%,marque.ilike.%${filters.search}%,modele.ilike.%${filters.search}%,code_produit.ilike.%${filters.search}%`);
        }

        if (filters?.categorie_id) {
          query = query.eq('categorie_id', filters.categorie_id);
        }

        if (filters?.marque) {
          query = query.eq('marque', filters.marque);
        }

        if (filters?.etat) {
          query = query.eq('etat', filters.etat);
        }

        if (filters?.prix_min !== undefined) {
          query = query.gte('prix_vente', filters.prix_min);
        }

        if (filters?.prix_max !== undefined) {
          query = query.lte('prix_vente', filters.prix_max);
        }

        // Pagination
        if (filters?.offset !== undefined && filters?.limit !== undefined) {
          query = query.range(filters.offset, filters.offset + filters.limit - 1);
        }

        const { data, error } = await query;
        
        console.log('📊 [useProduits] Raw response:', { data, error });
        
        if (error) {
          console.error('❌ [useProduits] Database error:', error);
          throw error;
        }

        // Récupérer les stocks pour chaque produit
        console.log('🔄 [useProduits] Fetching stocks for', data?.length, 'products');
        const produitsWithStocks = await Promise.all(
          (data || []).map(async (produit: any) => {
            try {
              const { data: stocks, error: stockError } = await supabase
                .from('stocks')
                .select('quantite')
                .eq('produit_id', produit.id);

              if (stockError) {
                console.warn('⚠️ [useProduits] Stock query error for product', produit.id, stockError);
              }

              const totalQuantity = (stocks || []).reduce((acc: number, stock: any) => acc + (stock?.quantite || 0), 0);
              
              return {
                ...produit,
                // Ensure categorie exists
                categorie: produit.categories || null,
                // Always provide _count with fallback values
                _count: {
                  stocks: (stocks || []).length,
                  total_quantity: totalQuantity
                }
              };
            } catch (stockError) {
              console.warn('⚠️ [useProduits] Stock fetch error for product', produit.id, stockError);
              return {
                ...produit,
                categorie: produit.categories || null,
                _count: {
                  stocks: 0,
                  total_quantity: 0
                }
              };
            }
          })
        );

        console.log('✅ [useProduits] Success:', produitsWithStocks.length, 'products with stocks');
        return produitsWithStocks as any;
      } catch (err) {
        console.error('💥 [useProduits] Unexpected error:', err);
        throw err;
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 3,
    retryDelay: 1000,
  });
};

export const useProduit = (id: string) => {
  return useQuery({
    queryKey: ['produit', id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('produits')
        .select(`
          *,
          categories:categorie_id (
            id,
            nom
          )
        `)
        .eq('id', id)
        .maybeSingle();

      if (error) {
        console.error('Error fetching produit:', error);
        throw error;
      }
      
      if (!data) {
        throw new Error('Produit non trouvé');
      }

      // Récupérer les stocks
      const { data: stocks } = await supabase
        .from('stocks')
        .select(`
          id,
          quantite,
          seuil_alerte,
          emplacement,
          boutique_id
        `)
        .eq('produit_id', id);

      // Récupérer les boutiques pour les stocks
      const stocksWithBoutiques = await Promise.all(
        (stocks || []).map(async (stock: any) => {
          const { data: boutique } = await supabase
            .from('boutiques')
            .select('id, nom')
            .eq('id', stock.boutique_id)
            .single();
          
          return {
            ...stock,
            boutique: boutique
          };
        })
      );

      return {
        ...data,
        categorie: data.categories,
        stocks: stocksWithBoutiques || []
      } as ProduitWithCategory;
    },
    enabled: !!id,
  });
};

export const useCreateProduit = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: ProduitFormData) => {
      const { data: result, error } = await supabase
        .from('produits')
        .insert(data as any)
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['produits'] });
      toast.success('Produit créé avec succès');
    },
    onError: (error: any) => {
      toast.error('Erreur lors de la création du produit: ' + error.message);
    },
  });
};

export const useUpdateProduit = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Record<string, any> }) => {
      const { data: result, error } = await supabase
        .from('produits')
        .update(data)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['produits'] });
      queryClient.invalidateQueries({ queryKey: ['produit', id] });
      toast.success('Produit mis à jour avec succès');
    },
    onError: (error: any) => {
      toast.error('Erreur lors de la mise à jour: ' + error.message);
    },
  });
};

export const useDeleteProduit = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: string) => {
      // Vérifier s'il y a des stocks associés
      const { data: stocks } = await supabase
        .from('stocks')
        .select('quantite')
        .eq('produit_id', id);

      const hasStock = stocks?.some(stock => stock.quantite > 0);
      
      if (hasStock) {
        throw new Error('Impossible de supprimer un produit avec du stock');
      }

      // Vérifier s'il y a des ventes associées
      const { data: ventes } = await supabase
        .from('vente_details')
        .select('id')
        .eq('produit_id', id)
        .limit(1);

      if (ventes && ventes.length > 0) {
        throw new Error('Impossible de supprimer un produit qui a été vendu');
      }

      const { error } = await supabase
        .from('produits')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['produits'] });
      toast.success('Produit supprimé avec succès');
    },
    onError: (error: any) => {
      toast.error('Erreur lors de la suppression: ' + error.message);
    },
  });
};

export const useMarques = () => {
  return useQuery({
    queryKey: ['marques'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('produits')
        .select('marque')
        .not('marque', 'is', null);

      if (error) throw error;

      // Obtenir les marques uniques
      const marques = [...new Set(data.map(p => p.marque))].sort();
      return marques;
    },
    staleTime: 1000 * 60 * 10, // 10 minutes
  });
};

export const useProduitStats = () => {
  return useQuery({
    queryKey: ['produit-stats'],
    queryFn: async () => {
      // Statistiques générales
      const { data: totalProduits, error: errorTotal } = await supabase
        .from('produits')
        .select('id', { count: 'exact', head: true });

      if (errorTotal) throw errorTotal;

      // Produits par catégorie
      const { data: parCategorie, error: errorCategorie } = await supabase
        .from('produits')
        .select(`
          categorie_id,
          categories!categorie_id(nom)
        `);

      if (errorCategorie) throw errorCategorie;

      // Valeur totale des stocks
      const { data: stocksValue, error: errorStocks } = await supabase
        .from('produits')
        .select(`
          id,
          prix_achat
        `);

      if (errorStocks) throw errorStocks;

      // Récupérer les stocks séparément
      const { data: allStocks } = await supabase
        .from('stocks')
        .select('produit_id, quantite');

      if (errorStocks) throw errorStocks;

      // Calculer la valeur du stock
      const valeurStock = stocksValue?.reduce((acc, produit) => {
        const stocksProduit = allStocks?.filter(s => s.produit_id === produit.id) || [];
        const totalQuantite = stocksProduit.reduce((sum, stock) => sum + (stock.quantite || 0), 0);
        return acc + (produit.prix_achat * totalQuantite);
      }, 0) || 0;

      // Grouper par catégorie
      const categoriesMap = new Map();
      parCategorie?.forEach(produit => {
        const categorieName = produit.categories?.nom || 'Sans catégorie';
        categoriesMap.set(categorieName, (categoriesMap.get(categorieName) || 0) + 1);
      });

      return {
        total: totalProduits || 0,
        parCategorie: Array.from(categoriesMap.entries()).map(([nom, count]) => ({ nom, count })),
        valeurStock: valeurStock,
      };
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};