import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useIsMobile } from '@/hooks/use-mobile';
import { 
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { 
  Package, 
  AlertTriangle, 
  TrendingDown, 
  DollarSign,
  Download,
  RefreshCw
} from 'lucide-react';
import { useStockReports } from '@/hooks/useStockReports';
import { formatCurrency, formatNumber } from '@/lib/formatters';

interface StockReportsProps {
  filters: {
    dateDebut: string;
    dateFin: string;
    boutiqueId: string;
    employeId: string;
    categorieId: string;
  };
}

export const StockReports = ({ filters }: StockReportsProps) => {
  const { 
    data: stockData, 
    isLoading,
    stats,
    rotationStock,
    produitsDormants,
    valorisationStock,
    alertesStock
  } = useStockReports(filters);
  const isMobile = useIsMobile();

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-8 w-full mb-2" />
                <Skeleton className="h-4 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Skeleton className="h-80" />
          <Skeleton className="h-80" />
        </div>
      </div>
    );
  }

  const COLORS = ['hsl(var(--primary))', 'hsl(var(--secondary))', 'hsl(var(--accent))', 'hsl(var(--destructive))'];

  return (
    <div className="space-y-6">
      {/* KPIs stocks */}
      <div className={`grid gap-4 ${isMobile ? 'grid-cols-2' : 'grid-cols-1 md:grid-cols-4'}`}>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Valeur du Stock</p>
                <p className="text-2xl font-bold">{formatCurrency(stats?.valeurTotale || 0)}</p>
                <p className="text-xs text-green-600">
                  {formatNumber(stats?.nombreProduits || 0)} produits différents
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Rotation Moyenne</p>
                <p className="text-2xl font-bold">{stats?.rotationMoyenne || 0} jours</p>
                <p className="text-xs text-blue-600">
                  Délai moyen d'écoulement
                </p>
              </div>
              <RefreshCw className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Produits en Alerte</p>
                <p className="text-2xl font-bold text-destructive">{stats?.produitsEnAlerte || 0}</p>
                <p className="text-xs text-red-600">
                  Stock bas ou rupture
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-destructive" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Produits Dormants</p>
                <p className="text-2xl font-bold text-warning">{stats?.produitsDormants || 0}</p>
                <p className="text-xs text-orange-600">
                  &gt; 90 jours sans mouvement
                </p>
              </div>
              <TrendingDown className="h-8 w-8 text-warning" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Graphiques */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Valorisation par boutique */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Valorisation du Stock par Boutique</CardTitle>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={valorisationStock}>
                <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                <XAxis 
                  dataKey="boutique" 
                  stroke="hsl(var(--muted-foreground))"
                  fontSize={12}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis 
                  stroke="hsl(var(--muted-foreground))"
                  fontSize={12}
                  tickFormatter={(value) => formatCurrency(value)}
                />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'hsl(var(--background))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px'
                  }}
                  formatter={(value) => [formatCurrency(value as number), 'Valeur']}
                />
                <Bar dataKey="valeur" fill="hsl(var(--primary))" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Rotation par catégorie */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Rotation par Catégorie</CardTitle>
            <Button variant="outline" size="sm">
              <Package className="h-4 w-4 mr-2" />
              Détails
            </Button>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={rotationStock}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${name}: ${value}j`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="rotation"
                >
                  {rotationStock?.map((_, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'hsl(var(--background))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px'
                  }}
                  formatter={(value) => [`${value} jours`, 'Rotation moyenne']}
                />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Alertes et produits dormants */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Alertes stock */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              Alertes Stock Critique
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-80 overflow-y-auto">
              {alertesStock?.map((alerte, index) => (
                <div key={index} className="flex items-center justify-between p-3 border border-destructive/20 rounded-lg bg-destructive/5">
                  <div className="flex-1">
                    <p className="font-medium text-sm">{alerte.produit}</p>
                    <p className="text-xs text-muted-foreground">{alerte.boutique}</p>
                  </div>
                  <div className="text-right">
                    <Badge variant="destructive" className="text-xs">
                      {alerte.quantite} restant(s)
                    </Badge>
                    <p className="text-xs text-muted-foreground mt-1">
                      Seuil: {alerte.seuil}
                    </p>
                  </div>
                </div>
              )) || (
                <p className="text-muted-foreground text-center py-4">
                  Aucune alerte stock
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Produits dormants */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingDown className="h-5 w-5 text-warning" />
              Produits Dormants
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-80 overflow-y-auto">
              {produitsDormants?.map((produit, index) => (
                <div key={index} className="flex items-center justify-between p-3 border border-warning/20 rounded-lg bg-warning/5">
                  <div className="flex-1">
                    <p className="font-medium text-sm">{produit.nom}</p>
                    <p className="text-xs text-muted-foreground">{produit.boutique}</p>
                  </div>
                  <div className="text-right">
                    <Badge variant="outline" className="text-xs">
                      {produit.joursInactif} jours
                    </Badge>
                    <p className="text-xs text-muted-foreground mt-1">
                      {formatCurrency(produit.valeur)}
                    </p>
                  </div>
                </div>
              )) || (
                <p className="text-muted-foreground text-center py-4">
                  Aucun produit dormant
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};