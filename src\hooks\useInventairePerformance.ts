import { useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const useInventairePerformance = () => {
  const queryClient = useQueryClient();

  // Précharger les données critiques pour l'inventaire
  const preloadInventoryData = useCallback(async (boutiqueId?: string) => {
    console.log('🚀 [useInventairePerformance] Preloading inventory data');
    
    try {
      // Précharger les campagnes
      await queryClient.prefetchQuery({
        queryKey: ['campagnes_inventaire', boutiqueId],
        queryFn: async () => {
          let query = supabase
            .from('campagnes_inventaire')
            .select(`
              *,
              boutiques:boutique_id(nom),
              employes:responsable_id(nom, prenom)
            `);
          
          if (boutiqueId) {
            query = query.eq('boutique_id', boutiqueId);
          }
          
          const { data, error } = await query
            .order('created_at', { ascending: false })
            .limit(10); // Limiter pour les performances initiales
          
          if (error) throw error;
          return data;
        },
        staleTime: 30 * 1000,
      });

      // Précharger les stocks si boutiqueId est fourni
      if (boutiqueId) {
        await queryClient.prefetchQuery({
          queryKey: ['stocks', boutiqueId],
          queryFn: async () => {
            const { data, error } = await supabase
              .from('stocks')
              .select(`
                *,
                produits:produit_id(nom, marque, modele, code_produit)
              `)
              .eq('boutique_id', boutiqueId);
            
            if (error) throw error;
            return data;
          },
        });
      }

      console.log('✅ [useInventairePerformance] Data preloaded successfully');
    } catch (error) {
      console.error('❌ [useInventairePerformance] Preloading error:', error);
    }
  }, [queryClient]);

  // Invalider intelligemment les caches liés à l'inventaire
  const invalidateInventoryQueries = useCallback((type: 'campaign' | 'details' | 'stocks' | 'all') => {
    console.log('🔄 [useInventairePerformance] Invalidating queries:', type);
    
    switch (type) {
      case 'campaign':
        queryClient.invalidateQueries({ queryKey: ['campagnes_inventaire'] });
        break;
      case 'details':
        queryClient.invalidateQueries({ queryKey: ['inventaire_details'] });
        break;
      case 'stocks':
        queryClient.invalidateQueries({ queryKey: ['stocks'] });
        queryClient.invalidateQueries({ queryKey: ['mouvements_stock'] });
        break;
      case 'all':
        queryClient.invalidateQueries({ queryKey: ['campagnes_inventaire'] });
        queryClient.invalidateQueries({ queryKey: ['inventaire_details'] });
        queryClient.invalidateQueries({ queryKey: ['stocks'] });
        queryClient.invalidateQueries({ queryKey: ['mouvements_stock'] });
        break;
    }
  }, [queryClient]);

  // Optimiser les requêtes en arrière-plan
  const optimizeQueries = useCallback(() => {
    console.log('⚡ [useInventairePerformance] Optimizing queries');
    
    // Nettoyer les anciennes requêtes
    queryClient.getQueryCache().findAll().forEach(query => {
      if (query.state.dataUpdatedAt < Date.now() - 10 * 60 * 1000) { // 10 minutes
        queryClient.removeQueries({ queryKey: query.queryKey });
      }
    });

    // Garder en cache les données critiques
    queryClient.getQueryCache().findAll({ queryKey: ['campagnes_inventaire'] }).forEach(query => {
      if (query.state.status === 'success') {
        queryClient.setQueryDefaults(query.queryKey, {
          staleTime: 5 * 60 * 1000, // 5 minutes pour les campagnes
        });
      }
    });
  }, [queryClient]);

  return {
    preloadInventoryData,
    invalidateInventoryQueries,
    optimizeQueries,
  };
};