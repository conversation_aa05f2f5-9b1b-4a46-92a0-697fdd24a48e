import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { VenteWithDetails } from '@/hooks/useVenteDetails';

export interface PDFGenerationOptions {
  quality?: number;
  scale?: number;
  format?: 'a4' | 'letter';
  orientation?: 'portrait' | 'landscape';
  filename?: string;
}

export class InvoicePDFService {
  
  static async generatePDF(
    vente: VenteWithDetails, 
    options: PDFGenerationOptions = {}
  ): Promise<jsPDF> {
    const {
      quality = 1.0,
      scale = 2,
      format = 'a4',
      orientation = 'portrait'
    } = options;

    // Créer le template dans le DOM temporairement
    const templateContainer = document.createElement('div');
    templateContainer.style.position = 'absolute';
    templateContainer.style.left = '-9999px';
    templateContainer.style.top = '-9999px';
    templateContainer.style.width = '210mm';
    templateContainer.style.background = 'white';
    
    // Injecter le contenu du template
    templateContainer.innerHTML = await this.getInvoiceHTML(vente);
    document.body.appendChild(templateContainer);

    try {
      // Générer le canvas avec html2canvas
      const canvas = await html2canvas(templateContainer, {
        scale: scale,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        logging: false,
        width: 794, // A4 width in pixels at 96 DPI
        height: 1123, // A4 height in pixels at 96 DPI
      });

      // Créer le PDF
      const pdf = new jsPDF({
        orientation: orientation,
        unit: 'mm',
        format: format,
        compress: true
      });

      // Calculer les dimensions
      const imgWidth = 210; // A4 width in mm
      const pageHeight = 297; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;
      let position = 0;

      // Ajouter l'image au PDF
      const imgData = canvas.toDataURL('image/jpeg', quality);
      pdf.addImage(imgData, 'JPEG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      // Gérer les pages multiples si nécessaire
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'JPEG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      // Ajouter les métadonnées
      pdf.setProperties({
        title: `Facture ${vente.numero_facture}`,
        subject: `Facture pour ${vente.client_nom || 'Client anonyme'}`,
        author: 'Maison des Téléphones',
        keywords: 'facture, vente, téléphone',
        creator: 'Système de Gestion Maison des Téléphones'
      });

      return pdf;
    } finally {
      // Nettoyer le DOM
      document.body.removeChild(templateContainer);
    }
  }

  static async downloadPDF(vente: VenteWithDetails, options: PDFGenerationOptions = {}): Promise<void> {
    const pdf = await this.generatePDF(vente, options);
    const filename = options.filename || this.generateFilename(vente);
    pdf.save(filename);
  }

  static async printPDF(vente: VenteWithDetails, options: PDFGenerationOptions = {}): Promise<void> {
    const pdf = await this.generatePDF(vente, options);
    
    // Ouvrir dans une nouvelle fenêtre pour impression
    const pdfDataUri = pdf.output('datauristring');
    const printWindow = window.open();
    
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>Impression Facture ${vente.numero_facture}</title>
            <style>
              body { margin: 0; padding: 0; }
              iframe { width: 100%; height: 100vh; border: none; }
            </style>
          </head>
          <body>
            <iframe src="${pdfDataUri}"></iframe>
            <script>
              window.onload = function() {
                setTimeout(() => {
                  window.print();
                }, 500);
              }
            </script>
          </body>
        </html>
      `);
      printWindow.document.close();
    }
  }

  static async previewPDF(vente: VenteWithDetails, options: PDFGenerationOptions = {}): Promise<string> {
    const pdf = await this.generatePDF(vente, options);
    return pdf.output('datauristring');
  }

  private static generateFilename(vente: VenteWithDetails): string {
    const date = new Date(vente.date_vente).toISOString().split('T')[0];
    const clientName = (vente.client_nom || 'Client-Anonyme')
      .replace(/[^a-zA-Z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
    
    return `Facture_${vente.numero_facture}_${clientName}_${date}.pdf`;
  }

  private static async getInvoiceHTML(vente: VenteWithDetails): Promise<string> {
    // Template HTML statique pour éviter les dépendances React lors de la génération
    const formatCurrency = (amount: number) => `${amount.toLocaleString('fr-CI')} FCFA`;
    const formatDate = (date: string) => new Date(date).toLocaleDateString('fr-CI');

    return `
      <div style="background: white; padding: 32px; max-width: 794px; min-height: 1123px; font-family: Arial, sans-serif; font-size: 14px; color: #374151;">
        <!-- En-tête -->
        <div style="display: flex; justify-content: space-between; margin-bottom: 32px; border-bottom: 1px solid #e5e7eb; padding-bottom: 24px;">
          <div>
            <h1 style="font-size: 24px; font-weight: bold; color: #2563eb; margin-bottom: 8px;">Maison des Téléphones</h1>
            <div style="color: #6b7280; line-height: 1.5;">
              <p>Zone Industrielle, Abidjan, Côte d'Ivoire</p>
              <p>Tél: +225 27 20 XX XX XX</p>
              <p>Email: <EMAIL></p>
            </div>
          </div>
          <div style="text-align: right;">
            <h2 style="font-size: 32px; font-weight: bold; margin-bottom: 16px;">FACTURE</h2>
            <div style="background: #dbeafe; padding: 16px; border-radius: 8px;">
              <p style="font-weight: 600; color: #1e40af; margin: 0;">N° ${vente.numero_facture}</p>
              <p style="color: #6b7280; margin: 4px 0 0 0;">Date: ${formatDate(vente.date_vente)}</p>
            </div>
          </div>
        </div>

        <!-- Infos client -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 32px; margin-bottom: 32px;">
          <div>
            <h3 style="font-weight: bold; margin-bottom: 12px; border-bottom: 1px solid #e5e7eb; padding-bottom: 4px;">FACTURÉ À</h3>
            <div style="line-height: 1.5;">
              <p style="font-weight: 600; margin: 0;">${vente.client_nom || 'Client anonyme'}</p>
              ${vente.client_telephone ? `<p style="margin: 4px 0;">Tél: ${vente.client_telephone}</p>` : ''}
              ${vente.client_email ? `<p style="margin: 4px 0;">Email: ${vente.client_email}</p>` : ''}
            </div>
          </div>
          <div>
            <h3 style="font-weight: bold; margin-bottom: 12px; border-bottom: 1px solid #e5e7eb; padding-bottom: 4px;">BOUTIQUE</h3>
            <div style="line-height: 1.5;">
              <p style="font-weight: 600; margin: 0;">${vente.boutiques?.nom || ''}</p>
              <p style="margin: 4px 0;">Vendeur: ${vente.employes?.nom || ''}</p>
            </div>
          </div>
        </div>

        <!-- Tableau produits -->
        <table style="width: 100%; border-collapse: collapse; margin-bottom: 32px;">
          <thead>
            <tr style="background: #dbeafe;">
              <th style="border: 1px solid #d1d5db; padding: 12px; text-align: left; font-weight: 600;">Produit</th>
              <th style="border: 1px solid #d1d5db; padding: 12px; text-align: center; font-weight: 600;">Qté</th>
              <th style="border: 1px solid #d1d5db; padding: 12px; text-align: right; font-weight: 600;">Prix Unit.</th>
              <th style="border: 1px solid #d1d5db; padding: 12px; text-align: center; font-weight: 600;">Remise</th>
              <th style="border: 1px solid #d1d5db; padding: 12px; text-align: right; font-weight: 600;">Total</th>
            </tr>
          </thead>
          <tbody>
            ${vente.vente_details?.map(detail => `
              <tr>
                <td style="border: 1px solid #d1d5db; padding: 12px;">
                  <div>
                    <p style="font-weight: 500; margin: 0;">${detail.produits?.nom || ''}</p>
                    ${detail.produits?.code_produit ? `<p style="font-size: 12px; color: #6b7280; margin: 4px 0 0 0;">Code: ${detail.produits.code_produit}</p>` : ''}
                  </div>
                </td>
                <td style="border: 1px solid #d1d5db; padding: 12px; text-align: center;">${detail.quantite}</td>
                <td style="border: 1px solid #d1d5db; padding: 12px; text-align: right;">${formatCurrency(detail.prix_unitaire)}</td>
                <td style="border: 1px solid #d1d5db; padding: 12px; text-align: center;">${detail.remise}%</td>
                <td style="border: 1px solid #d1d5db; padding: 12px; text-align: right; font-weight: 500;">${formatCurrency(detail.sous_total)}</td>
              </tr>
            `).join('') || ''}
          </tbody>
        </table>

        <!-- Totaux -->
        <div style="display: flex; justify-content: flex-end; margin-bottom: 32px;">
          <div style="width: 300px;">
            <div style="background: #f9fafb; padding: 16px; border-radius: 8px;">
              <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                <span>Sous-total HT:</span>
                <span style="font-weight: 500;">${formatCurrency((vente.montant_total - vente.montant_tva) || 0)}</span>
              </div>
              <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                <span>TVA:</span>
                <span style="font-weight: 500;">${formatCurrency(vente.montant_tva || 0)}</span>
              </div>
              <div style="border-top: 1px solid #d1d5db; padding-top: 12px;">
                <div style="display: flex; justify-content: space-between; font-size: 18px; font-weight: bold; color: #2563eb;">
                  <span>TOTAL TTC:</span>
                  <span>${formatCurrency(vente.montant_total)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Mentions légales -->
        <div style="font-size: 11px; color: #6b7280; line-height: 1.4; border-top: 1px solid #e5e7eb; padding-top: 16px;">
          <p style="font-weight: 600; margin: 0 0 8px 0;">CONDITIONS DE VENTE:</p>
          <p style="margin: 0 0 4px 0;">• Paiement à réception de facture, sauf accord particulier.</p>
          <p style="margin: 0 0 4px 0;">• Tout retard de paiement entraîne l'application d'intérêts de retard.</p>
          <p style="margin: 0 0 4px 0;">• Garantie selon conditions du constructeur.</p>
          <p style="margin: 16px 0 0 0; text-align: center; font-weight: 500;">Merci de votre confiance - Maison des Téléphones</p>
        </div>
      </div>
    `;
  }
}