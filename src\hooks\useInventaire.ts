import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface CampagneInventaire {
  id: string;
  nom: string;
  boutique_id: string;
  date_debut: string;
  date_fin?: string;
  statut: 'en_cours' | 'terminee' | 'validee' | 'annulee';
  commentaires?: string;
  responsable_id?: string;
  created_at: string;
  updated_at: string;
  boutiques?: {
    nom: string;
  };
  employes?: {
    nom: string;
    prenom: string;
  };
}

export interface InventaireDetail {
  id: string;
  campagne_id: string;
  produit_id: string;
  stock_id: string;
  quantite_theorique: number;
  quantite_physique?: number;
  ecart?: number;
  valide: boolean;
  commentaire?: string;
  compte_par?: string;
  created_at: string;
  updated_at: string;
  produits?: {
    nom: string;
    marque: string;
    modele: string;
    code_produit: string;
  };
  stocks?: {
    quantite: number;
    emplacement?: string;
  };
  employes?: {
    nom: string;
    prenom: string;
  };
}

export interface MouvementStock {
  id: string;
  produit_id: string;
  boutique_id: string;
  type_mouvement: 'entree' | 'sortie' | 'ajustement' | 'transfert_sortie' | 'transfert_entree' | 'vente' | 'inventaire';
  quantite_avant: number;
  quantite_apres: number;
  quantite_mouvement: number;
  motif?: string;
  reference_id?: string;
  reference_type?: string;
  employe_id?: string;
  created_at: string;
  produits?: {
    nom: string;
    marque: string;
    modele: string;
    code_produit: string;
  };
  boutiques?: {
    nom: string;
  };
  employes?: {
    nom: string;
    prenom: string;
  };
}

// Hook pour les campagnes d'inventaire avec pagination
export const useCampagnesInventaire = (boutiqueId?: string, page = 0, limit = 20) => {
  return useQuery({
    queryKey: ['campagnes_inventaire', boutiqueId, page, limit],
    queryFn: async () => {
      console.log('🔍 [useCampagnesInventaire] Fetching campaigns:', { boutiqueId, page, limit });
      
      let query = supabase
        .from('campagnes_inventaire')
        .select(`
          *,
          boutiques:boutique_id(nom),
          employes:responsable_id(nom, prenom)
        `, { count: 'exact' });
      
      if (boutiqueId) {
        query = query.eq('boutique_id', boutiqueId);
      }
      
      // Pagination
      const from = page * limit;
      const to = from + limit - 1;
      
      const { data, error, count } = await query
        .order('created_at', { ascending: false })
        .range(from, to);
      
      if (error) {
        console.error('❌ [useCampagnesInventaire] Error:', error);
        throw error;
      }
      
      console.log('✅ [useCampagnesInventaire] Success:', { count, dataLength: data?.length });
      return { 
        data: data as CampagneInventaire[], 
        count: count || 0,
        hasMore: count ? (page + 1) * limit < count : false
      };
    },
    staleTime: 30 * 1000, // 30 secondes
    retry: (failureCount, error: any) => {
      if (error?.code === 'PGRST116') return false; // Pas de retry pour les erreurs de données
      return failureCount < 2;
    },
  });
};

// Hook pour créer une campagne d'inventaire
export const useCreateCampagneInventaire = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: {
      nom: string;
      boutique_id: string;
      responsable_id?: string;
      commentaires?: string;
    }) => {
      console.log('🔄 [useCreateCampagneInventaire] Creating campaign:', data);
      
      // Validation métier : vérifier qu'il n'y a pas déjà une campagne en cours pour cette boutique
      const { data: existingCampagnes, error: checkError } = await supabase
        .from('campagnes_inventaire')
        .select('id, nom')
        .eq('boutique_id', data.boutique_id)
        .eq('statut', 'en_cours');
      
      if (checkError) {
        console.error('❌ [useCreateCampagneInventaire] Check error:', checkError);
        throw checkError;
      }
      
      if (existingCampagnes && existingCampagnes.length > 0) {
        const error = new Error(`Une campagne d'inventaire "${existingCampagnes[0].nom}" est déjà en cours pour cette boutique.`);
        error.name = 'BusinessRuleViolation';
        throw error;
      }
      
      // Créer la nouvelle campagne
      const { data: result, error } = await supabase
        .from('campagnes_inventaire')
        .insert({
          ...data,
          created_by: (await supabase.auth.getUser()).data.user?.id
        })
        .select()
        .single();
      
      if (error) {
        console.error('❌ [useCreateCampagneInventaire] Insert error:', error);
        throw error;
      }
      
      console.log('✅ [useCreateCampagneInventaire] Campaign created:', result);
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['campagnes_inventaire'] });
      toast({
        title: "Campagne créée",
        description: "La campagne d'inventaire a été créée avec succès."
      });
    },
    onError: (error: any) => {
      console.error('💥 [useCreateCampagneInventaire] Failed:', error);
      
      let errorMessage = "Impossible de créer la campagne d'inventaire.";
      if (error.name === 'BusinessRuleViolation') {
        errorMessage = error.message;
      } else if (error?.code === '23505') {
        errorMessage = "Une campagne avec ce nom existe déjà.";
      } else if (error?.message) {
        errorMessage = `Erreur: ${error.message}`;
      }
      
      toast({
        variant: "destructive",
        title: "Erreur",
        description: errorMessage
      });
    }
  });
};

// Hook pour les détails d'inventaire
export const useInventaireDetails = (campagneId: string) => {
  return useQuery({
    queryKey: ['inventaire_details', campagneId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('inventaire_details')
        .select(`
          *,
          produits:produit_id(nom, marque, modele, code_produit),
          stocks:stock_id(quantite, emplacement),
          employes:compte_par(nom, prenom)
        `)
        .eq('campagne_id', campagneId)
        .order('created_at', { ascending: true });
      
      if (error) throw error;
      return data as InventaireDetail[];
    },
    enabled: !!campagneId,
  });
};

// Hook pour initialiser une campagne d'inventaire avec les stocks existants
export const useInitialiserCampagne = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ campagneId, boutiqueId }: { campagneId: string; boutiqueId: string }) => {
      // Récupérer tous les stocks de la boutique
      const { data: stocks, error: stocksError } = await supabase
        .from('stocks')
        .select('*')
        .eq('boutique_id', boutiqueId);
      
      if (stocksError) throw stocksError;

      // Créer les détails d'inventaire pour chaque stock
      const inventaireDetails = stocks.map(stock => ({
        campagne_id: campagneId,
        produit_id: stock.produit_id,
        stock_id: stock.id,
        quantite_theorique: stock.quantite,
      }));

      const { error } = await supabase
        .from('inventaire_details')
        .insert(inventaireDetails);
      
      if (error) throw error;
      return inventaireDetails;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventaire_details'] });
      toast({
        title: "Campagne initialisée",
        description: "Les produits ont été ajoutés à la campagne d'inventaire."
      });
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible d'initialiser la campagne d'inventaire."
      });
    }
  });
};

// Hook pour mettre à jour un détail d'inventaire
export const useUpdateInventaireDetail = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ 
      id, 
      quantite_physique, 
      commentaire 
    }: { 
      id: string; 
      quantite_physique: number; 
      commentaire?: string;
    }) => {
      const { data, error } = await supabase
        .from('inventaire_details')
        .update({ quantite_physique, commentaire })
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventaire_details'] });
      toast({
        title: "Quantité mise à jour",
        description: "La quantité physique a été enregistrée."
      });
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de mettre à jour la quantité."
      });
    }
  });
};

// Hook pour valider les écarts d'inventaire
export const useValiderEcarts = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (campagneId: string) => {
      console.log('🔄 [useValiderEcarts] Validating discrepancies for campaign:', campagneId);
      
      // Vérifier d'abord le statut de la campagne
      const { data: campagne, error: campagneError } = await supabase
        .from('campagnes_inventaire')
        .select('statut, nom')
        .eq('id', campagneId)
        .single();
      
      if (campagneError) {
        console.error('❌ [useValiderEcarts] Campaign check error:', campagneError);
        throw campagneError;
      }
      
      if (campagne.statut !== 'en_cours') {
        const error = new Error(`La campagne "${campagne.nom}" ne peut plus être modifiée (statut: ${campagne.statut}).`);
        error.name = 'BusinessRuleViolation';
        throw error;
      }
      
      // Récupérer tous les détails avec écarts
      const { data: details, error: detailsError } = await supabase
        .from('inventaire_details')
        .select('*, stocks!inner(id, quantite, boutique_id)')
        .eq('campagne_id', campagneId)
        .not('quantite_physique', 'is', null);
      
      if (detailsError) {
        console.error('❌ [useValiderEcarts] Details error:', detailsError);
        throw detailsError;
      }

      let adjustmentCount = 0;
      
      // Appliquer les ajustements de stock avec transaction-like behavior
      for (const detail of details) {
        const ecart = (detail.quantite_physique || 0) - detail.quantite_theorique;
        
        if (ecart !== 0) {
          console.log('📝 [useValiderEcarts] Processing adjustment:', {
            produit_id: detail.produit_id,
            ecart,
            quantite_theorique: detail.quantite_theorique,
            quantite_physique: detail.quantite_physique
          });
          
          // Mettre à jour le stock
          const { error: stockUpdateError } = await supabase
            .from('stocks')
            .update({ 
              quantite: detail.quantite_physique,
              updated_at: new Date().toISOString()
            })
            .eq('id', detail.stock_id);

          if (stockUpdateError) {
            console.error('❌ [useValiderEcarts] Stock update error:', stockUpdateError);
            throw stockUpdateError;
          }

          // Enregistrer le mouvement de stock
          const { error: movementError } = await supabase
            .from('mouvements_stock')
            .insert({
              produit_id: detail.produit_id,
              boutique_id: detail.stocks.boutique_id,
              type_mouvement: 'inventaire',
              quantite_avant: detail.quantite_theorique,
              quantite_apres: detail.quantite_physique,
              quantite_mouvement: ecart,
              motif: `Ajustement inventaire - ${detail.commentaire || 'Écart constaté lors du comptage physique'}`,
              reference_id: campagneId,
              reference_type: 'campagne_inventaire',
              created_by: (await supabase.auth.getUser()).data.user?.id
            });

          if (movementError) {
            console.error('❌ [useValiderEcarts] Movement error:', movementError);
            throw movementError;
          }
          
          adjustmentCount++;
        }
      }

      // Marquer la campagne comme terminée
      const { error: updateError } = await supabase
        .from('campagnes_inventaire')
        .update({ 
          statut: 'terminee',
          date_fin: new Date().toISOString()
        })
        .eq('id', campagneId);
      
      if (updateError) {
        console.error('❌ [useValiderEcarts] Campaign update error:', updateError);
        throw updateError;
      }
      
      console.log('✅ [useValiderEcarts] Validation completed:', { adjustmentCount });
      return { adjustmentCount };
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['campagnes_inventaire'] });
      queryClient.invalidateQueries({ queryKey: ['stocks'] });
      queryClient.invalidateQueries({ queryKey: ['mouvements_stock'] });
      queryClient.invalidateQueries({ queryKey: ['inventaire_details'] });
      
      toast({
        title: "Inventaire validé",
        description: `${result.adjustmentCount} ajustement(s) de stock ont été appliqués.`
      });
    },
    onError: (error: any) => {
      console.error('💥 [useValiderEcarts] Validation failed:', error);
      
      let errorMessage = "Impossible de valider l'inventaire.";
      if (error.name === 'BusinessRuleViolation') {
        errorMessage = error.message;
      } else if (error?.message) {
        errorMessage = `Erreur: ${error.message}`;
      }
      
      toast({
        variant: "destructive",
        title: "Erreur",
        description: errorMessage
      });
    }
  });
};

// Hook pour les mouvements de stock
export const useMouvementsStock = (filters?: {
  produitId?: string;
  boutiqueId?: string;
  typeMouvement?: string;
  dateDebut?: string;
  dateFin?: string;
}) => {
  return useQuery({
    queryKey: ['mouvements_stock', filters],
    queryFn: async () => {
      let query = supabase
        .from('mouvements_stock')
        .select(`
          *,
          produits:produit_id(nom, marque, modele, code_produit),
          boutiques:boutique_id(nom),
          employes:employe_id(nom, prenom)
        `);
      
      if (filters?.produitId) {
        query = query.eq('produit_id', filters.produitId);
      }
      if (filters?.boutiqueId) {
        query = query.eq('boutique_id', filters.boutiqueId);
      }
      if (filters?.typeMouvement) {
        query = query.eq('type_mouvement', filters.typeMouvement);
      }
      if (filters?.dateDebut) {
        query = query.gte('created_at', filters.dateDebut);
      }
      if (filters?.dateFin) {
        query = query.lte('created_at', filters.dateFin);
      }
      
      const { data, error } = await query.order('created_at', { ascending: false });
      
      if (error) throw error;
      return data as MouvementStock[];
    },
    staleTime: 60 * 1000, // 1 minute
  });
};