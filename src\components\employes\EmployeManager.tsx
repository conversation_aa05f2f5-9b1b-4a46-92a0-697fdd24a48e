import { useState, useEffect } from 'react';
import { Plus, Filter, Download, Eye, Edit, Trash2, Users, AlertTriangle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { DataTable } from '@/components/ui/data-table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useEmployes, useDeleteEmploye, useEmployeStats, type Employe } from '@/hooks/useEmployes';
import { useBoutiques } from '@/hooks/useBoutiques';
import { EmployeForm } from './EmployeForm';
import { EmployeDetail } from './EmployeDetail';
import { formatCurrency } from '@/lib/formatters';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

export const EmployeManager = () => {
  const [selectedEmploye, setSelectedEmploye] = useState<Employe | null>(null);
  const [editingEmploye, setEditingEmploye] = useState<Employe | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [showDetail, setShowDetail] = useState(false);
  const [filters, setFilters] = useState({
    search: '',
    boutique_id: '',
    statut: '',
    poste: ''
  });

  const { data: employes = [], isLoading, refetch, error } = useEmployes();
  const { data: boutiques = [] } = useBoutiques();
  const { data: stats } = useEmployeStats();
  const deleteEmploye = useDeleteEmploye();

  // Filtrage des employés
  const filteredEmployes = employes.filter(employe => {
    if (!employe) return false;
    
    const searchLower = filters.search.toLowerCase();
    const matchesSearch = !filters.search || 
      (employe.nom || '').toLowerCase().includes(searchLower) ||
      (employe.prenom || '').toLowerCase().includes(searchLower) ||
      (employe.email || '').toLowerCase().includes(searchLower) ||
      (employe.poste || '').toLowerCase().includes(searchLower);
    
    const matchesBoutique = !filters.boutique_id || employe.boutique_id === filters.boutique_id;
    const matchesStatut = !filters.statut || employe.statut === filters.statut;
    const matchesPoste = !filters.poste || (employe.poste || '').toLowerCase().includes(filters.poste.toLowerCase());
    
    return matchesSearch && matchesBoutique && matchesStatut && matchesPoste;
  });

  const handleEdit = (employe: Employe) => {
    setEditingEmploye(employe);
    setShowForm(true);
  };

  const handleDelete = async (id: string) => {
    deleteEmploye.mutate(id);
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    setEditingEmploye(null);
    refetch();
  };

  const getStatutVariant = (statut: string) => {
    switch (statut) {
      case 'actif': return 'default';
      case 'inactif': return 'secondary';
      case 'suspendu': return 'destructive';
      default: return 'outline';
    }
  };

  const columns = [
    {
      key: 'nom_complet',
      label: 'Nom complet',
      render: (employe: Employe) => (
        <div>
          <div className="font-medium">{employe?.prenom || ''} {employe?.nom || ''}</div>
          <div className="text-sm text-muted-foreground">{employe?.email || ''}</div>
        </div>
      ),
    },
    {
      key: 'poste',
      label: 'Poste',
      render: (employe: Employe) => (
        <Badge variant="outline">{employe?.poste || 'Non défini'}</Badge>
      ),
    },
    {
      key: 'boutique',
      label: 'Boutique',
      render: (employe: Employe) => (
        <span>{employe?.boutiques?.nom || 'Non assignée'}</span>
      ),
    },
    {
      key: 'statut',
      label: 'Statut',
      render: (employe: Employe) => (
        <Badge variant={getStatutVariant(employe?.statut || 'inactif')}>
          {employe?.statut || 'inactif'}
        </Badge>
      ),
    },
    {
      key: 'salaire',
      label: 'Salaire',
      render: (employe: Employe) => (
        <span className="font-mono">
          {employe?.salaire ? formatCurrency(employe.salaire) : 'Non renseigné'}
        </span>
      ),
    },
    {
      key: 'date_embauche',
      label: 'Date d\'embauche',
      render: (employe: Employe) => (
        <span className="text-sm">
          {employe?.date_embauche ? format(new Date(employe.date_embauche), 'dd/MM/yyyy', { locale: fr }) : 'Non définie'}
        </span>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (employe: Employe) => (
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setSelectedEmploye(employe);
              setShowDetail(true);
            }}
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEdit(employe)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="ghost" size="sm">
                <Trash2 className="h-4 w-4" />
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Supprimer l'employé</AlertDialogTitle>
                <AlertDialogDescription>
                  Êtes-vous sûr de vouloir supprimer {employe?.prenom || ''} {employe?.nom || 'cet employé'} ? 
                  Cette action est irréversible.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Annuler</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => handleDelete(employe?.id || '')}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  Supprimer
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      ),
    },
  ];

  if (error) {
    return (
      <Card className="m-6">
        <CardContent className="pt-6">
          <div className="flex items-center justify-center space-x-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            <span>Erreur lors du chargement des employés</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Statistiques */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Total</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Actifs</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.actifs}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Inactifs</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-500">{stats.inactifs}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Suspendus</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.suspendus}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Salaire moyen</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats.salaireMoyen)}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Interface principale */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <CardTitle>Gestion des Employés</CardTitle>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Exporter
              </Button>
              <Dialog open={showForm} onOpenChange={setShowForm}>
                <DialogTrigger asChild>
                  <Button onClick={() => setEditingEmploye(null)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Nouvel employé
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>
                      {editingEmploye ? 'Modifier l\'employé' : 'Nouvel employé'}
                    </DialogTitle>
                  </DialogHeader>
                  <EmployeForm
                    employe={editingEmploye}
                    onSuccess={handleFormSuccess}
                    onCancel={() => {
                      setShowForm(false);
                      setEditingEmploye(null);
                    }}
                  />
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filtres */}
          <div className="flex flex-wrap gap-4 mb-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4" />
              <Input
                placeholder="Rechercher..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="w-64"
              />
            </div>
            <Select
              value={filters.boutique_id || "all"}
              onValueChange={(value) => setFilters(prev => ({ ...prev, boutique_id: value === "all" ? "" : value }))}
            >
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Toutes les boutiques" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Toutes les boutiques</SelectItem>
                {boutiques.map((boutique) => (
                  <SelectItem key={boutique.id} value={boutique.id}>
                    {boutique.nom}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select
              value={filters.statut || "all"}
              onValueChange={(value) => setFilters(prev => ({ ...prev, statut: value === "all" ? "" : value }))}
            >
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Tous les statuts" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les statuts</SelectItem>
                <SelectItem value="actif">Actif</SelectItem>
                <SelectItem value="inactif">Inactif</SelectItem>
                <SelectItem value="suspendu">Suspendu</SelectItem>
              </SelectContent>
            </Select>
            <Input
              placeholder="Poste..."
              value={filters.poste}
              onChange={(e) => setFilters(prev => ({ ...prev, poste: e.target.value }))}
              className="w-40"
            />
          </div>

          {/* Tableau */}
          <DataTable
            data={filteredEmployes}
            columns={columns}
            isLoading={isLoading}
            emptyMessage="Aucun employé trouvé"
          />
        </CardContent>
      </Card>

      {/* Dialog de détail */}
      <Dialog open={showDetail} onOpenChange={setShowDetail}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Détails de l'employé</DialogTitle>
          </DialogHeader>
          {selectedEmploye && (
            <EmployeDetail
              employe={selectedEmploye}
              onEdit={() => {
                setShowDetail(false);
                handleEdit(selectedEmploye);
              }}
              onClose={() => setShowDetail(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};