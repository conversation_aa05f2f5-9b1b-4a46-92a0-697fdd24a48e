import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, Di<PERSON>Header, Di<PERSON>Title, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { VenteWithDetails } from '@/hooks/useVenteDetails';
import { InvoicePDFService } from '@/services/invoicePDFService';
import { InvoiceTemplate } from './InvoiceTemplate';
import { Printer, Download, Eye, FileText, Loader } from 'lucide-react';

interface InvoicePrintDialogProps {
  vente: VenteWithDetails;
  children: React.ReactNode;
}

export const InvoicePrintDialog: React.FC<InvoicePrintDialogProps> = ({ vente, children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const { toast } = useToast();

  const handlePreview = async () => {
    if (previewUrl) return; // Déjà généré
    
    setIsGenerating(true);
    try {
      const dataUri = await InvoicePDFService.previewPDF(vente);
      setPreviewUrl(dataUri);
    } catch (error) {
      console.error('Erreur lors de la prévisualisation:', error);
      toast({
        title: "Erreur",
        description: "Impossible de générer la prévisualisation",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownload = async () => {
    setIsGenerating(true);
    try {
      await InvoicePDFService.downloadPDF(vente, {
        quality: 0.9,
        scale: 2
      });
      toast({
        title: "Téléchargement réussi",
        description: "La facture PDF a été téléchargée"
      });
    } catch (error) {
      console.error('Erreur lors du téléchargement:', error);
      toast({
        title: "Erreur",
        description: "Impossible de télécharger le PDF",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handlePrint = async () => {
    setIsGenerating(true);
    try {
      await InvoicePDFService.printPDF(vente, {
        quality: 1.0,
        scale: 2
      });
      toast({
        title: "Impression lancée",
        description: "La fenêtre d'impression va s'ouvrir"
      });
    } catch (error) {
      console.error('Erreur lors de l\'impression:', error);
      toast({
        title: "Erreur",
        description: "Impossible d'imprimer la facture",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Facture {vente.numero_facture}
          </DialogTitle>
        </DialogHeader>
        
        <div className="flex flex-col h-[80vh]">
          {/* Actions */}
          <div className="flex gap-2 mb-4">
            <Button
              onClick={handlePreview}
              variant="outline"
              size="sm"
              disabled={isGenerating}
            >
              <Eye className="h-4 w-4 mr-2" />
              {previewUrl ? 'Prévisualisation' : 'Générer aperçu'}
            </Button>
            <Button
              onClick={handleDownload}
              variant="outline"
              size="sm"
              disabled={isGenerating}
            >
              {isGenerating ? (
                <Loader className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Download className="h-4 w-4 mr-2" />
              )}
              Télécharger PDF
            </Button>
            <Button
              onClick={handlePrint}
              size="sm"
              disabled={isGenerating}
            >
              {isGenerating ? (
                <Loader className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Printer className="h-4 w-4 mr-2" />
              )}
              Imprimer
            </Button>
          </div>

          <Separator className="mb-4" />

          {/* Prévisualisation */}
          <div className="flex-1 overflow-auto">
            {previewUrl ? (
              <iframe
                src={previewUrl}
                className="w-full h-full border rounded-lg"
                title="Prévisualisation de la facture"
              />
            ) : (
              <div className="h-full border rounded-lg bg-gray-50 flex flex-col">
                {/* Template React pour l'aperçu */}
                <div className="flex-1 overflow-auto p-4">
                  <div className="transform scale-75 origin-top">
                    <InvoiceTemplate vente={vente} />
                  </div>
                </div>
                
                {!isGenerating && (
                  <div className="p-4 bg-blue-50 border-t">
                    <p className="text-sm text-blue-800 text-center">
                      Cliquez sur "Générer aperçu" pour voir le PDF final
                    </p>
                  </div>
                )}
                
                {isGenerating && (
                  <div className="p-4 bg-yellow-50 border-t">
                    <div className="flex items-center justify-center gap-2 text-yellow-800">
                      <Loader className="h-4 w-4 animate-spin" />
                      <span className="text-sm">Génération du PDF en cours...</span>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};