-- Phase 6: Sécurité et Permissions - Audit Trail et Validation avancée

-- Table d'audit pour tracer toutes les opérations critiques
CREATE TABLE public.audit_logs (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  table_name text NOT NULL,
  operation text NOT NULL CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')),
  old_data jsonb,
  new_data jsonb,
  user_id uuid REFERENCES auth.users(id),
  user_ip inet,
  user_agent text,
  executed_at timestamp with time zone NOT NULL DEFAULT now(),
  boutique_id uuid REFERENCES public.boutiques(id)
);

-- Index pour performance des requêtes d'audit
CREATE INDEX idx_audit_logs_table_operation ON public.audit_logs(table_name, operation);
CREATE INDEX idx_audit_logs_user_id ON public.audit_logs(user_id);
CREATE INDEX idx_audit_logs_executed_at ON public.audit_logs(executed_at DESC);
CREATE INDEX idx_audit_logs_boutique_id ON public.audit_logs(boutique_id);

-- <PERSON><PERSON> pour audit_logs
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;

-- Politique pour voir ses propres logs d'audit
CREATE POLICY "Users can view their own audit logs" 
ON public.audit_logs 
FOR SELECT 
USING (user_id = auth.uid());

-- Fonction générique d'audit pour toutes les tables critiques
CREATE OR REPLACE FUNCTION public.audit_trigger_function()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_ip inet;
  user_agent text;
  boutique_id_val uuid;
BEGIN
  -- Extraire l'IP et user agent des headers de la requête
  user_ip := COALESCE(
    current_setting('request.headers', true)::json->>'x-real-ip',
    current_setting('request.headers', true)::json->>'x-forwarded-for',
    '127.0.0.1'
  )::inet;
  
  user_agent := current_setting('request.headers', true)::json->>'user-agent';
  
  -- Extraire boutique_id si disponible
  boutique_id_val := COALESCE(NEW.boutique_id, OLD.boutique_id);
  
  -- Insérer le log d'audit
  INSERT INTO public.audit_logs (
    table_name,
    operation,
    old_data,
    new_data,
    user_id,
    user_ip,
    user_agent,
    boutique_id
  ) VALUES (
    TG_TABLE_NAME,
    TG_OP,
    CASE WHEN TG_OP = 'DELETE' OR TG_OP = 'UPDATE' THEN to_jsonb(OLD) END,
    CASE WHEN TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN to_jsonb(NEW) END,
    auth.uid(),
    user_ip,
    user_agent,
    boutique_id_val
  );
  
  RETURN COALESCE(NEW, OLD);
END;
$$;

-- Triggers d'audit pour toutes les tables critiques
CREATE TRIGGER audit_ventes_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.ventes
  FOR EACH ROW EXECUTE FUNCTION public.audit_trigger_function();

CREATE TRIGGER audit_stocks_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.stocks
  FOR EACH ROW EXECUTE FUNCTION public.audit_trigger_function();

CREATE TRIGGER audit_transferts_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.transferts
  FOR EACH ROW EXECUTE FUNCTION public.audit_trigger_function();

CREATE TRIGGER audit_employes_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.employes
  FOR EACH ROW EXECUTE FUNCTION public.audit_trigger_function();

CREATE TRIGGER audit_boutiques_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.boutiques
  FOR EACH ROW EXECUTE FUNCTION public.audit_trigger_function();

-- Fonction de validation des données sensibles
CREATE OR REPLACE FUNCTION public.validate_sensitive_data()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
  -- Validation email
  IF NEW.email IS NOT NULL AND NEW.email !~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' THEN
    RAISE EXCEPTION 'Format email invalide: %', NEW.email;
  END IF;
  
  -- Validation téléphone (format international basique)
  IF NEW.telephone IS NOT NULL AND NEW.telephone !~ '^\+?[0-9\s\-\(\)]{8,20}$' THEN
    RAISE EXCEPTION 'Format téléphone invalide: %', NEW.telephone;
  END IF;
  
  -- Validation salaire (pas négatif)
  IF TG_TABLE_NAME = 'employes' AND NEW.salaire IS NOT NULL AND NEW.salaire < 0 THEN
    RAISE EXCEPTION 'Le salaire ne peut pas être négatif';
  END IF;
  
  -- Validation montants (pas négatifs)
  IF TG_TABLE_NAME = 'ventes' THEN
    IF NEW.montant_total < 0 THEN
      RAISE EXCEPTION 'Le montant total ne peut pas être négatif';
    END IF;
    IF NEW.montant_tva < 0 THEN
      RAISE EXCEPTION 'Le montant TVA ne peut pas être négatif';
    END IF;
  END IF;
  
  -- Validation prix produits
  IF TG_TABLE_NAME = 'produits' THEN
    IF NEW.prix_achat <= 0 THEN
      RAISE EXCEPTION 'Le prix d''achat doit être positif';
    END IF;
    IF NEW.prix_vente <= 0 THEN
      RAISE EXCEPTION 'Le prix de vente doit être positif';
    END IF;
    IF NEW.prix_vente <= NEW.prix_achat THEN
      RAISE EXCEPTION 'Le prix de vente doit être supérieur au prix d''achat';
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$;

-- Triggers de validation pour les tables sensibles
CREATE TRIGGER validate_employes_data
  BEFORE INSERT OR UPDATE ON public.employes
  FOR EACH ROW EXECUTE FUNCTION public.validate_sensitive_data();

CREATE TRIGGER validate_boutiques_data
  BEFORE INSERT OR UPDATE ON public.boutiques
  FOR EACH ROW EXECUTE FUNCTION public.validate_sensitive_data();

CREATE TRIGGER validate_ventes_data
  BEFORE INSERT OR UPDATE ON public.ventes
  FOR EACH ROW EXECUTE FUNCTION public.validate_sensitive_data();

CREATE TRIGGER validate_produits_data
  BEFORE INSERT OR UPDATE ON public.produits
  FOR EACH ROW EXECUTE FUNCTION public.validate_sensitive_data();

-- Fonction pour nettoyer les anciens logs d'audit (rétention 1 an)
CREATE OR REPLACE FUNCTION public.cleanup_old_audit_logs()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  DELETE FROM public.audit_logs 
  WHERE executed_at < now() - interval '1 year';
END;
$$;