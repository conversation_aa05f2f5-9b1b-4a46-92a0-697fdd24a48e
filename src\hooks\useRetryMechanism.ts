import { useState, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from '@/hooks/use-toast';

interface RetryConfig {
  maxAttempts?: number;
  baseDelay?: number;
  maxDelay?: number;
  backoffFactor?: number;
}

interface RetryState {
  isRetrying: boolean;
  attempts: number;
  lastError: Error | null;
}

/**
 * Hook pour gérer les mécanismes de retry automatique et manuel
 */
export const useRetryMechanism = (config: RetryConfig = {}) => {
  const {
    maxAttempts = 3,
    baseDelay = 1000,
    maxDelay = 30000,
    backoffFactor = 2
  } = config;

  const queryClient = useQueryClient();
  const [retryState, setRetryState] = useState<RetryState>({
    isRetrying: false,
    attempts: 0,
    lastError: null
  });

  // Calcul du délai avec backoff exponentiel
  const calculateDelay = useCallback((attempt: number): number => {
    const delay = Math.min(baseDelay * Math.pow(backoffFactor, attempt), maxDelay);
    // Ajouter un peu de jitter pour éviter les thundering herds
    return delay + Math.random() * 1000;
  }, [baseDelay, backoffFactor, maxDelay]);

  // Fonction de retry automatique
  const executeWithRetry = useCallback(async <T>(
    operation: () => Promise<T>,
    onError?: (error: Error, attempt: number) => void
  ): Promise<T> => {
    let lastError: Error;
    
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        setRetryState(prev => ({ 
          ...prev, 
          isRetrying: attempt > 0, 
          attempts: attempt + 1 
        }));

        const result = await operation();
        
        // Succès - reset le state
        setRetryState({ isRetrying: false, attempts: 0, lastError: null });
        return result;
        
      } catch (error) {
        lastError = error as Error;
        
        setRetryState(prev => ({ 
          ...prev, 
          lastError: lastError,
          attempts: attempt + 1
        }));

        if (onError) {
          onError(lastError, attempt + 1);
        }

        // Si c'est la dernière tentative, on throw l'erreur
        if (attempt === maxAttempts - 1) {
          setRetryState(prev => ({ ...prev, isRetrying: false }));
          throw lastError;
        }

        // Attendre avant la prochaine tentative
        const delay = calculateDelay(attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }, [maxAttempts, calculateDelay]);

  // Retry manuel pour les requêtes React Query
  const retryQuery = useCallback(async (queryKey: string[]) => {
    try {
      setRetryState(prev => ({ ...prev, isRetrying: true }));
      
      await queryClient.refetchQueries({ 
        queryKey,
        type: 'active' 
      });

      setRetryState({ isRetrying: false, attempts: 0, lastError: null });
      
      toast({
        title: "Succès",
        description: "Les données ont été actualisées avec succès",
      });
      
    } catch (error) {
      const err = error as Error;
      setRetryState(prev => ({ 
        ...prev, 
        isRetrying: false, 
        lastError: err 
      }));
      
      toast({
        title: "Erreur",
        description: "Impossible d'actualiser les données",
        variant: "destructive"
      });
    }
  }, [queryClient]);

  // Retry global pour toutes les requêtes failed
  const retryAllFailedQueries = useCallback(async () => {
    try {
      setRetryState(prev => ({ ...prev, isRetrying: true }));
      
      await queryClient.refetchQueries({ 
        type: 'active',
        stale: true 
      });

      setRetryState({ isRetrying: false, attempts: 0, lastError: null });
      
      toast({
        title: "Succès",
        description: "Toutes les données ont été actualisées",
      });
      
    } catch (error) {
      const err = error as Error;
      setRetryState(prev => ({ 
        ...prev, 
        isRetrying: false, 
        lastError: err 
      }));
      
      toast({
        title: "Erreur",
        description: "Impossible d'actualiser toutes les données",
        variant: "destructive"
      });
    }
  }, [queryClient]);

  // Reset du state de retry
  const resetRetryState = useCallback(() => {
    setRetryState({ isRetrying: false, attempts: 0, lastError: null });
  }, []);

  return {
    // State
    ...retryState,
    
    // Actions
    executeWithRetry,
    retryQuery,
    retryAllFailedQueries,
    resetRetryState,
    
    // Utils
    hasExceededMaxAttempts: retryState.attempts >= maxAttempts,
    shouldRetry: retryState.attempts < maxAttempts
  };
};

/**
 * Hook spécialisé pour les requêtes Supabase avec retry intelligent
 */
export const useSupabaseRetry = () => {
  const retryMechanism = useRetryMechanism({
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 10000
  });

  const executeSupabaseQuery = useCallback(async <T>(
    query: () => Promise<{ data: T; error: any }>
  ): Promise<T> => {
    return retryMechanism.executeWithRetry(async () => {
      const { data, error } = await query();
      
      if (error) {
        // Analyser le type d'erreur pour décider si on doit retry
        if (isRetryableError(error)) {
          throw new Error(`Supabase Error: ${error.message}`);
        } else {
          // Erreur non retryable (ex: permissions, validation)
          throw new Error(`Non-retryable Error: ${error.message}`);
        }
      }
      
      return data;
    });
  }, [retryMechanism]);

  return {
    ...retryMechanism,
    executeSupabaseQuery
  };
};

// Fonction pour déterminer si une erreur Supabase est retryable
const isRetryableError = (error: any): boolean => {
  // Erreurs réseau ou temporaires
  if (error.code === 'NETWORK_ERROR' || 
      error.code === 'TIMEOUT' ||
      error.status >= 500) {
    return true;
  }
  
  // Erreurs de rate limiting
  if (error.status === 429) {
    return true;
  }
  
  // Erreurs de connexion
  if (error.message?.includes('Failed to fetch') ||
      error.message?.includes('Network Error') ||
      error.message?.includes('Connection')) {
    return true;
  }
  
  return false;
};

/**
 * Configuration React Query optimisée avec retry intelligent
 */
export const getOptimizedQueryConfig = () => ({
  retry: (failureCount: number, error: any) => {
    // Ne pas retry les erreurs 4xx (sauf 429)
    if (error?.status >= 400 && error?.status < 500 && error?.status !== 429) {
      return false;
    }
    
    // Retry maximum 3 fois
    return failureCount < 3;
  },
  retryDelay: (attemptIndex: number) => {
    // Backoff exponentiel avec jitter
    const delay = Math.min(1000 * Math.pow(2, attemptIndex), 30000);
    return delay + Math.random() * 1000;
  },
  staleTime: 5 * 60 * 1000, // 5 minutes
  gcTime: 10 * 60 * 1000,   // 10 minutes
  refetchOnWindowFocus: false,
  refetchOnReconnect: true,
  refetchOnMount: true
});