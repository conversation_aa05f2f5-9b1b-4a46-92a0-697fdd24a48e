import { useEffect, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';

interface PerformanceMetrics {
  renderTime: number;
  queryTime: number;
  memoryUsage: number;
  slowQueries: string[];
}

/**
 * Hook pour surveiller les performances de l'application
 * et optimiser automatiquement certains aspects
 */
export const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    queryTime: 0,
    memoryUsage: 0,
    slowQueries: []
  });
  
  const queryClient = useQueryClient();

  useEffect(() => {
    // Surveiller les performances de rendu
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const renderEntries = entries.filter(entry => entry.entryType === 'measure');
      
      if (renderEntries.length > 0) {
        const avgRenderTime = renderEntries.reduce((sum, entry) => sum + entry.duration, 0) / renderEntries.length;
        setMetrics(prev => ({ ...prev, renderTime: avgRenderTime }));
      }
    });

    observer.observe({ entryTypes: ['measure', 'navigation'] });

    // Surveiller l'utilisation mémoire (si disponible)
    if ('memory' in performance) {
      const updateMemoryUsage = () => {
        const memory = (performance as any).memory;
        setMetrics(prev => ({ 
          ...prev, 
          memoryUsage: memory.usedJSHeapSize / memory.jsHeapSizeLimit 
        }));
      };

      const memoryInterval = setInterval(updateMemoryUsage, 5000);
      return () => {
        clearInterval(memoryInterval);
        observer.disconnect();
      };
    }

    return () => observer.disconnect();
  }, []);

  // Optimiser automatiquement le cache des queries
  const optimizeQueries = () => {
    const queryCache = queryClient.getQueryCache();
    const queries = queryCache.getAll();
    
    // Identifier les queries lentes
    const slowQueries = queries
      .filter(query => {
        const lastFetch = query.state.dataUpdatedAt;
        const fetchDuration = query.state.fetchStatus === 'fetching' ? Date.now() - lastFetch : 0;
        return fetchDuration > 1000; // Plus de 1 seconde
      })
      .map(query => query.queryHash);

    setMetrics(prev => ({ ...prev, slowQueries }));

    // Nettoyer les queries obsolètes
    queries.forEach(query => {
      const lastAccess = query.state.dataUpdatedAt;
      const isOld = Date.now() - lastAccess > 10 * 60 * 1000; // 10 minutes
      
      if (isOld && !query.getObserversCount()) {
        queryClient.removeQueries({ queryKey: query.queryKey });
      }
    });
  };

  // Précharger les données critiques
  const preloadCriticalData = async () => {
    // Précharger les boutiques si pas en cache
    if (!queryClient.getQueryData(['boutiques'])) {
      queryClient.prefetchQuery({
        queryKey: ['boutiques'],
        staleTime: 5 * 60 * 1000, // 5 minutes
      });
    }

    // Précharger les employés si pas en cache
    if (!queryClient.getQueryData(['employes'])) {
      queryClient.prefetchQuery({
        queryKey: ['employes'],
        staleTime: 5 * 60 * 1000,
      });
    }
  };

  return {
    metrics,
    optimizeQueries,
    preloadCriticalData,
    isPerformanceGood: metrics.renderTime < 100 && metrics.memoryUsage < 0.8,
    recommendations: {
      needsOptimization: metrics.renderTime > 200 || metrics.memoryUsage > 0.9,
      slowQueriesCount: metrics.slowQueries.length,
      memoryPressure: metrics.memoryUsage > 0.8
    }
  };
};

/**
 * Hook pour surveiller spécifiquement les performances des ventes
 */
export const useVentesPerformanceOptimizer = () => {
  const queryClient = useQueryClient();

  const optimizeVentesQueries = () => {
    // Mettre en place un cache intelligent pour les ventes
    const ventesQueries = queryClient.getQueryCache()
      .findAll({ queryKey: ['ventes'] });

    // Garder seulement les 3 dernières variations de filtres en cache
    if (ventesQueries.length > 3) {
      const oldestQueries = ventesQueries
        .sort((a, b) => a.state.dataUpdatedAt - b.state.dataUpdatedAt)
        .slice(0, ventesQueries.length - 3);

      oldestQueries.forEach(query => {
        queryClient.removeQueries({ queryKey: query.queryKey });
      });
    }
  };

  const prefetchRelatedData = async (venteId: string) => {
    // Précharger les détails de vente
    queryClient.prefetchQuery({
      queryKey: ['vente-details', venteId],
      staleTime: 2 * 60 * 1000, // 2 minutes
    });
  };

  return {
    optimizeVentesQueries,
    prefetchRelatedData
  };
};