/**
 * Service d'impression de reçus thermiques pour Maison des Téléphones
 * Support des formats 57mm, 58mm, 80mm avec connexions USB/Bluetooth/WiFi
 */

import EscPosEncoder from 'esc-pos-encoder';

export interface ThermalPrinter {
  id: string;
  name: string;
  type: 'usb' | 'bluetooth' | 'wifi';
  width: 57 | 58 | 80;
  connected: boolean;
  instance?: any;
}

export interface ReceiptData {
  boutique: {
    nom: string;
    adresse: string;
    telephone?: string;
    email?: string;
  };
  vente: {
    numero_facture: string;
    date_vente: string;
    employe_nom?: string;
    client_nom?: string;
    client_telephone?: string;
  };
  produits: Array<{
    nom: string;
    quantite: number;
    prix_unitaire: number;
    sous_total: number;
  }>;
  totaux: {
    sous_total: number;
    montant_tva: number;
    montant_total: number;
    mode_paiement: string;
  };
}

class ThermalReceiptService {
  private printers: ThermalPrinter[] = [];
  private defaultPrinter: ThermalPrinter | null = null;

  /**
   * Détecte les imprimantes disponibles
   */
  async detectPrinters(): Promise<ThermalPrinter[]> {
    const detectedPrinters: ThermalPrinter[] = [];

    try {
      // Détection USB Serial
      if ('serial' in navigator && (navigator as any).serial) {
        try {
          const ports = await (navigator as any).serial.getPorts();
          ports.forEach((port: any, index: number) => {
            detectedPrinters.push({
              id: `usb-serial-${index}`,
              name: `Imprimante USB ${index + 1}`,
              type: 'usb',
              width: 80,
              connected: false,
              instance: port
            });
          });
        } catch (err) {
          console.warn('Serial API non disponible:', err);
        }
      }

      // Détection USB WebUSB
      if ('usb' in navigator && (navigator as any).usb) {
        detectedPrinters.push({
          id: 'usb-webusb',
          name: 'Imprimante USB (WebUSB)',
          type: 'usb',
          width: 80,
          connected: false,
          instance: null
        });
      }

      // Détection Bluetooth
      if ('bluetooth' in navigator && (navigator as any).bluetooth) {
        detectedPrinters.push({
          id: 'bluetooth',
          name: 'Imprimante Bluetooth',
          type: 'bluetooth',
          width: 58,
          connected: false,
          instance: null
        });
      }

    } catch (error) {
      console.error('Erreur détection imprimantes:', error);
    }

    this.printers = detectedPrinters;
    return detectedPrinters;
  }

  /**
   * Connecte une imprimante
   */
  async connectPrinter(printerId: string): Promise<boolean> {
    const printer = this.printers.find(p => p.id === printerId);
    if (!printer) return false;

    try {
      if (printer.type === 'usb' && printer.id.includes('serial')) {
        // Connexion USB Serial
        if (printer.instance && 'open' in printer.instance) {
          await printer.instance.open({ baudRate: 9600 });
        }
      } else if (printer.type === 'usb' && printer.id.includes('webusb')) {
        // Connexion USB WebUSB
        const device = await (navigator as any).usb.requestDevice({
          filters: [{ vendorId: 0x04b8 }] // Epson par exemple
        });
        await device.open();
        printer.instance = device;
      } else if (printer.type === 'bluetooth') {
        // Connexion Bluetooth
        const device = await (navigator as any).bluetooth.requestDevice({
          filters: [{ services: ['000018f0-0000-1000-8000-00805f9b34fb'] }]
        });
        const server = await device.gatt.connect();
        printer.instance = server;
      }

      printer.connected = true;
      if (!this.defaultPrinter) {
        this.defaultPrinter = printer;
      }

      return true;
    } catch (error) {
      console.error(`Erreur connexion imprimante ${printerId}:`, error);
      return false;
    }
  }

  /**
   * Génère le contenu ESC/POS pour un reçu
   */
  private generateReceiptContent(data: ReceiptData, width: 57 | 58 | 80): Uint8Array {
    const encoder = new EscPosEncoder();
    const maxChars = width === 80 ? 48 : 32;

    // En-tête boutique
    encoder
      .initialize()
      .align('center')
      .bold(true)
      .size('double')
      .text(data.boutique.nom)
      .newline()
      .bold(false)
      .size('normal')
      .text(data.boutique.adresse)
      .newline();

    if (data.boutique.telephone) {
      encoder.text(`Tél: ${data.boutique.telephone}`).newline();
    }
    if (data.boutique.email) {
      encoder.text(`Email: ${data.boutique.email}`).newline();
    }

    // Ligne de séparation
    encoder
      .align('center')
      .text('='.repeat(maxChars))
      .newline()
      .newline();

    // Informations facture
    encoder
      .align('left')
      .bold(true)
      .text(`Facture: ${data.vente.numero_facture}`)
      .newline()
      .bold(false)
      .text(`Date: ${new Date(data.vente.date_vente).toLocaleString('fr-CI')}`)
      .newline();

    if (data.vente.employe_nom) {
      encoder.text(`Vendeur: ${data.vente.employe_nom}`).newline();
    }
    if (data.vente.client_nom) {
      encoder.text(`Client: ${data.vente.client_nom}`).newline();
    }
    if (data.vente.client_telephone) {
      encoder.text(`Tél: ${data.vente.client_telephone}`).newline();
    }

    encoder.newline().text('-'.repeat(maxChars)).newline();

    // Produits
    data.produits.forEach(produit => {
      const nomTronque = produit.nom.length > maxChars - 10 
        ? produit.nom.substring(0, maxChars - 13) + '...'
        : produit.nom;
      
      encoder
        .text(nomTronque)
        .newline()
        .text(`${produit.quantite} x ${produit.prix_unitaire.toLocaleString('fr-CI')} FCFA`)
        .align('right')
        .text(`${produit.sous_total.toLocaleString('fr-CI')} FCFA`)
        .align('left')
        .newline();
    });

    // Totaux
    encoder
      .newline()
      .text('-'.repeat(maxChars))
      .newline()
      .bold(true)
      .text('Sous-total:')
      .align('right')
      .text(`${data.totaux.sous_total.toLocaleString('fr-CI')} FCFA`)
      .align('left')
      .newline()
      .text('TVA:')
      .align('right')
      .text(`${data.totaux.montant_tva.toLocaleString('fr-CI')} FCFA`)
      .align('left')
      .newline()
      .size('double')
      .text('TOTAL:')
      .align('right')
      .text(`${data.totaux.montant_total.toLocaleString('fr-CI')} FCFA`)
      .align('left')
      .size('normal')
      .newline()
      .newline()
      .bold(false)
      .text(`Paiement: ${data.totaux.mode_paiement}`)
      .newline();

    // Pied de page
    encoder
      .newline()
      .align('center')
      .text('Merci de votre visite!')
      .newline()
      .text('À bientôt chez Maison des Téléphones')
      .newline()
      .newline()
      .cut('partial')
      .cashdrawer();

    return encoder.encode();
  }

  /**
   * Imprime un reçu
   */
  async printReceipt(data: ReceiptData, printerId?: string): Promise<boolean> {
    const printer = printerId 
      ? this.printers.find(p => p.id === printerId)
      : this.defaultPrinter;

    if (!printer || !printer.connected) {
      throw new Error('Aucune imprimante connectée');
    }

    try {
      const content = this.generateReceiptContent(data, printer.width);
      
      if (printer.type === 'usb' && printer.instance) {
        if (printer.id.includes('serial') && 'writable' in printer.instance) {
          const writer = printer.instance.writable.getWriter();
          await writer.write(content);
          writer.releaseLock();
        } else if (printer.id.includes('webusb') && 'transferOut' in printer.instance) {
          await printer.instance.transferOut(1, content);
        }
        return true;
      } else if (printer.type === 'bluetooth' && printer.instance) {
        // Envoi via Bluetooth (implémentation basique)
        console.log('Envoi Bluetooth:', content);
        return true;
      } else {
        // Fallback - ouvrir une fenêtre avec le contenu formaté
        this.printFallback(data, printer.width);
        return true;
      }
    } catch (error) {
      console.error('Erreur impression:', error);
      throw new Error(`Erreur d'impression: ${error}`);
    }
  }

  /**
   * Test d'impression
   */
  async testPrint(printerId: string): Promise<boolean> {
    const testData: ReceiptData = {
      boutique: {
        nom: 'MAISON DES TELEPHONES',
        adresse: 'Test - Abidjan, Côte d\'Ivoire',
        telephone: '+225 XX XX XX XX'
      },
      vente: {
        numero_facture: 'TEST-001',
        date_vente: new Date().toISOString(),
        employe_nom: 'Test Vendeur'
      },
      produits: [
        {
          nom: 'Test Produit',
          quantite: 1,
          prix_unitaire: 1000,
          sous_total: 1000
        }
      ],
      totaux: {
        sous_total: 1000,
        montant_tva: 0,
        montant_total: 1000,
        mode_paiement: 'Espèces'
      }
    };

    return this.printReceipt(testData, printerId);
  }

  /**
   * Ouvre le tiroir-caisse
   */
  async openCashDrawer(printerId?: string): Promise<boolean> {
    const printer = printerId 
      ? this.printers.find(p => p.id === printerId)
      : this.defaultPrinter;

    if (!printer || !printer.connected) return false;

    try {
      const encoder = new EscPosEncoder();
      encoder.cashdrawer();
      const content = encoder.encode();

      if (printer.type === 'usb' && printer.instance) {
        if (printer.id.includes('serial') && 'writable' in printer.instance) {
          const writer = printer.instance.writable.getWriter();
          await writer.write(content);
          writer.releaseLock();
        } else if (printer.id.includes('webusb') && 'transferOut' in printer.instance) {
          await printer.instance.transferOut(1, content);
        }
        return true;
      }
    } catch (error) {
      console.error('Erreur ouverture tiroir:', error);
    }

    return false;
  }

  /**
   * Fallback d'impression via fenêtre popup
   */
  private printFallback(data: ReceiptData, width: 57 | 58 | 80): void {
    const receiptHtml = this.generateReceiptHtml(data, width);
    const printWindow = window.open('', '_blank', 'width=400,height=600');
    if (printWindow) {
      printWindow.document.write(receiptHtml);
      printWindow.document.close();
      printWindow.focus();
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 500);
    }
  }

  /**
   * Génère le HTML pour l'impression fallback
   */
  private generateReceiptHtml(data: ReceiptData, width: 57 | 58 | 80): string {
    const maxChars = width === 80 ? 48 : 32;
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Reçu - ${data.vente.numero_facture}</title>
        <style>
          body { 
            font-family: 'Courier New', monospace; 
            font-size: ${width === 80 ? '12px' : '10px'}; 
            line-height: 1.2; 
            margin: 0; 
            padding: 5mm;
            width: ${width}mm;
          }
          .center { text-align: center; }
          .bold { font-weight: bold; }
          .large { font-size: ${width === 80 ? '16px' : '14px'}; }
          .separator { border-top: 1px solid black; margin: 2px 0; }
          .row { display: flex; justify-content: space-between; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        <div class="center large bold">${data.boutique.nom}</div>
        <div class="center">${data.boutique.adresse}</div>
        ${data.boutique.telephone ? `<div class="center">Tél: ${data.boutique.telephone}</div>` : ''}
        
        <div class="separator"></div>
        
        <div class="bold">Facture: ${data.vente.numero_facture}</div>
        <div>Date: ${new Date(data.vente.date_vente).toLocaleString('fr-CI')}</div>
        ${data.vente.employe_nom ? `<div>Vendeur: ${data.vente.employe_nom}</div>` : ''}
        ${data.vente.client_nom ? `<div>Client: ${data.vente.client_nom}</div>` : ''}
        
        <div class="separator"></div>
        
        ${data.produits.map(p => `
          <div>${p.nom.length > maxChars ? p.nom.substring(0, maxChars - 3) + '...' : p.nom}</div>
          <div class="row">
            <span>${p.quantite} x ${p.prix_unitaire.toLocaleString('fr-CI')} FCFA</span>
            <span class="bold">${p.sous_total.toLocaleString('fr-CI')} FCFA</span>
          </div>
        `).join('')}
        
        <div class="separator"></div>
        
        <div class="row bold"><span>Sous-total:</span><span>${data.totaux.sous_total.toLocaleString('fr-CI')} FCFA</span></div>
        <div class="row bold"><span>TVA:</span><span>${data.totaux.montant_tva.toLocaleString('fr-CI')} FCFA</span></div>
        <div class="row large bold"><span>TOTAL:</span><span>${data.totaux.montant_total.toLocaleString('fr-CI')} FCFA</span></div>
        
        <div class="bold">Paiement: ${data.totaux.mode_paiement}</div>
        
        <div class="separator"></div>
        <div class="center bold">Merci de votre visite!</div>
        <div class="center">À bientôt chez Maison des Téléphones</div>
      </body>
      </html>
    `;
  }

  /**
   * Déconnecte une imprimante
   */
  async disconnectPrinter(printerId: string): Promise<void> {
    const printer = this.printers.find(p => p.id === printerId);
    if (printer && printer.instance) {
      try {
        if (printer.type === 'usb' && printer.id.includes('serial') && 'close' in printer.instance) {
          await printer.instance.close();
        } else if (printer.type === 'usb' && printer.id.includes('webusb') && 'close' in printer.instance) {
          await printer.instance.close();
        } else if (printer.type === 'bluetooth' && 'disconnect' in printer.instance) {
          await printer.instance.disconnect();
        }
      } catch (error) {
        console.error('Erreur déconnexion:', error);
      }
      
      printer.connected = false;
      
      if (this.defaultPrinter?.id === printerId) {
        this.defaultPrinter = this.printers.find(p => p.connected) || null;
      }
    }
  }

  /**
   * Configuration imprimante par boutique
   */
  savePrinterPreference(boutiqueId: string, printerId: string, width: 57 | 58 | 80): void {
    const preferences = JSON.parse(localStorage.getItem('printer-preferences') || '{}');
    preferences[boutiqueId] = { printerId, width };
    localStorage.setItem('printer-preferences', JSON.stringify(preferences));
  }

  getPrinterPreference(boutiqueId: string): { printerId: string; width: 57 | 58 | 80 } | null {
    const preferences = JSON.parse(localStorage.getItem('printer-preferences') || '{}');
    return preferences[boutiqueId] || null;
  }

  // Getters
  getPrinters(): ThermalPrinter[] {
    return this.printers;
  }

  getDefaultPrinter(): ThermalPrinter | null {
    return this.defaultPrinter;
  }

  setDefaultPrinter(printerId: string): boolean {
    const printer = this.printers.find(p => p.id === printerId && p.connected);
    if (printer) {
      this.defaultPrinter = printer;
      return true;
    }
    return false;
  }
}

export const thermalReceiptService = new ThermalReceiptService();