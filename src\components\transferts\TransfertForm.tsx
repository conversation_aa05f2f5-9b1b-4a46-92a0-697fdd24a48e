import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useCreateTransfert, CreateTransfertData } from '@/hooks/useTransferts';
import { useCreateTransfertDetail } from '@/hooks/useTransfertDetails';
import { useBoutiques } from '@/hooks/useBoutiques';
import { useEmployes } from '@/hooks/useEmployes';
import { useStocks } from '@/hooks/useStocks';
import { Package, Trash2, Plus } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const transfertSchema = z.object({
  boutique_source_id: z.string().min(1, 'Boutique source requise'),
  boutique_destination_id: z.string().min(1, 'Boutique destination requise'),
  employe_expediteur_id: z.string().min(1, 'Employé expéditeur requis'),
  commentaires: z.string().optional(),
});

const transfertDetailSchema = z.object({
  produit_id: z.string().min(1, 'Produit requis'),
  quantite: z.number().min(1, 'Quantité doit être supérieure à 0'),
});

type TransfertFormData = CreateTransfertData;
type TransfertDetailData = z.infer<typeof transfertDetailSchema>;

interface TransfertFormProps {
  onSuccess?: () => void;
}

export const TransfertForm = ({ onSuccess }: TransfertFormProps) => {
  const [selectedSourceBoutique, setSelectedSourceBoutique] = useState<string>('');
  const [transfertDetails, setTransfertDetails] = useState<(TransfertDetailData & { id: string })[]>([]);
  const { toast } = useToast();

  const { data: boutiques } = useBoutiques();
  const { data: employes } = useEmployes();
  const { data: stocks } = useStocks(selectedSourceBoutique || undefined);
  
  const createTransfert = useCreateTransfert();
  const createTransfertDetail = useCreateTransfertDetail();

  const form = useForm<TransfertFormData>({
    resolver: zodResolver(transfertSchema),
    defaultValues: {
      boutique_source_id: '',
      boutique_destination_id: '',
      employe_expediteur_id: '',
      commentaires: '',
    },
  });

  const addDetail = () => {
    const newDetail = {
      id: Math.random().toString(36).substr(2, 9),
      produit_id: '',
      quantite: 1,
    };
    setTransfertDetails([...transfertDetails, newDetail]);
  };

  const removeDetail = (id: string) => {
    setTransfertDetails(transfertDetails.filter(detail => detail.id !== id));
  };

  const updateDetail = (id: string, field: keyof TransfertDetailData, value: any) => {
    setTransfertDetails(transfertDetails.map(detail => 
      detail.id === id ? { ...detail, [field]: value } : detail
    ));
  };

  const getAvailableStock = (produitId: string) => {
    return stocks?.find(s => s.produit_id === produitId)?.quantite || 0;
  };

  const getProduitName = (produitId: string) => {
    const stock = stocks?.find(s => s.produit_id === produitId);
    if (!stock?.produits) return 'Produit inconnu';
    return `${stock.produits.marque} ${stock.produits.nom} - ${stock.produits.modele}`;
  };

  const onSubmit = async (data: TransfertFormData) => {
    if (transfertDetails.length === 0) {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Vous devez ajouter au moins un produit au transfert.",
      });
      return;
    }

    // Validation des stocks disponibles
    for (const detail of transfertDetails) {
      if (!detail.produit_id) {
        toast({
          variant: "destructive",
          title: "Erreur",
          description: "Tous les produits doivent être sélectionnés.",
        });
        return;
      }

      const availableStock = getAvailableStock(detail.produit_id);
      if (detail.quantite > availableStock) {
        toast({
          variant: "destructive",
          title: "Stock insuffisant",
          description: `Stock disponible pour ${getProduitName(detail.produit_id)}: ${availableStock}`,
        });
        return;
      }
    }

    try {
      // Créer le transfert
      const transfertResult = await createTransfert.mutateAsync(data);
      
      // Créer les détails
      for (const detail of transfertDetails) {
        await createTransfertDetail.mutateAsync({
          transfert_id: transfertResult.id,
          produit_id: detail.produit_id,
          quantite: detail.quantite,
        });
      }

      toast({
        title: "Transfert créé",
        description: "Le transfert a été créé avec succès.",
      });
      
      onSuccess?.();
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de créer le transfert.",
      });
    }
  };

  const filteredEmployes = employes?.filter(emp => 
    emp.boutique_id === selectedSourceBoutique && emp.statut === 'actif'
  );

  const availableStocks = stocks?.filter(stock => stock.quantite > 0);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="boutique_source_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Boutique source</FormLabel>
                <Select 
                  onValueChange={(value) => {
                    field.onChange(value);
                    setSelectedSourceBoutique(value);
                    setTransfertDetails([]); // Reset détails quand boutique change
                  }} 
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner la boutique source" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {boutiques?.filter(b => b.statut === 'active').map((boutique) => (
                      <SelectItem key={boutique.id} value={boutique.id}>
                        {boutique.nom}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="boutique_destination_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Boutique destination</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner la boutique destination" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {boutiques?.filter(b => 
                      b.statut === 'active' && b.id !== selectedSourceBoutique
                    ).map((boutique) => (
                      <SelectItem key={boutique.id} value={boutique.id}>
                        {boutique.nom}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="employe_expediteur_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Employé expéditeur</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner l'employé" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {filteredEmployes?.map((employe) => (
                      <SelectItem key={employe.id} value={employe.id}>
                        {employe.prenom} {employe.nom}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="commentaires"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Commentaires (optionnel)</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="Commentaires du transfert..." 
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Section des produits */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Produits à transférer
              </CardTitle>
              <Button 
                type="button" 
                variant="outline" 
                size="sm" 
                onClick={addDetail}
                disabled={!selectedSourceBoutique}
              >
                <Plus className="h-4 w-4 mr-2" />
                Ajouter un produit
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {transfertDetails.length === 0 ? (
              <p className="text-muted-foreground text-center py-4">
                {!selectedSourceBoutique 
                  ? "Sélectionnez d'abord une boutique source"
                  : "Aucun produit ajouté. Cliquez sur 'Ajouter un produit' pour commencer."
                }
              </p>
            ) : (
              transfertDetails.map((detail) => (
                <div key={detail.id} className="flex items-center gap-4 p-4 border rounded-lg">
                  <div className="flex-1">
                    <Select 
                      value={detail.produit_id} 
                      onValueChange={(value) => updateDetail(detail.id, 'produit_id', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionner un produit" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableStocks?.map((stock) => (
                          <SelectItem key={stock.produit_id} value={stock.produit_id}>
                            <div className="flex items-center justify-between w-full">
                              <span>
                                {stock.produits?.marque} {stock.produits?.nom} - {stock.produits?.modele}
                              </span>
                              <Badge variant="secondary" className="ml-2">
                                Stock: {stock.quantite}
                              </Badge>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="w-24">
                    <Input
                      type="number"
                      min="1"
                      max={getAvailableStock(detail.produit_id)}
                      value={detail.quantite}
                      onChange={(e) => updateDetail(detail.id, 'quantite', parseInt(e.target.value) || 1)}
                      placeholder="Qté"
                    />
                  </div>
                  
                  {detail.produit_id && (
                    <Badge variant="outline">
                      Stock: {getAvailableStock(detail.produit_id)}
                    </Badge>
                  )}
                  
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeDetail(detail.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))
            )}
          </CardContent>
        </Card>

        <div className="flex justify-end space-x-2">
          <Button
            type="submit"
            disabled={createTransfert.isPending || transfertDetails.length === 0}
          >
            {createTransfert.isPending ? 'Création...' : 'Créer le transfert'}
          </Button>
        </div>
      </form>
    </Form>
  );
};