import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useVenteDetails } from '@/hooks/useVenteDetails';
import { useUpdateVente } from '@/hooks/useVentes';
import { InvoicePrintDialog } from '@/components/invoices/InvoicePrintDialog';
import { thermalReceiptService, ReceiptData } from '@/services/thermalReceiptService';
import { 
  FileText, 
  User, 
  MapPin, 
  Calendar, 
  CreditCard, 
  Package, 
  Edit, 
  Check, 
  X, 
  RotateCcw,
  Printer,
  Download,
  Receipt
} from 'lucide-react';
import { formatCurrency } from '@/lib/formatters';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { toast } from '@/hooks/use-toast';

interface VenteDetailProps {
  venteId: string;
  onClose?: () => void;
}

export const VenteDetail: React.FC<VenteDetailProps> = ({ venteId, onClose }) => {
  const { data: vente, isLoading } = useVenteDetails(venteId);
  const updateVente = useUpdateVente();
  const [showActions, setShowActions] = useState(false);

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  if (!vente) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <div className="text-lg font-medium">Vente introuvable</div>
            <div className="text-muted-foreground">Cette vente n'existe pas ou a été supprimée.</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getStatutVariant = (statut: string) => {
    switch (statut) {
      case 'validee': return 'default';
      case 'en_attente': return 'secondary';
      case 'annulee': return 'destructive';
      case 'remboursee': return 'outline';
      default: return 'secondary';
    }
  };

  const getModeIcon = (mode: string) => {
    switch (mode) {
      case 'especes': return '💵';
      case 'carte': return '💳';
      case 'cheque': return '📝';
      case 'virement': return '🏦';
      case 'mobile_money': return '📱';
      case 'crypto': return '₿';
      default: return '💰';
    }
  };

  const changerStatut = async (nouveauStatut: 'validee' | 'annulee' | 'remboursee' | 'en_attente') => {
    try {
      await updateVente.mutateAsync({
        id: vente.id,
        statut: nouveauStatut,
      });
      setShowActions(false);
    } catch (error) {
      console.error('Erreur lors du changement de statut:', error);
    }
  };

  const imprimerRecuThermique = async () => {
    try {
      // Préparer les données pour l'impression thermique
      const receiptData: ReceiptData = {
        boutique: {
          nom: vente.boutiques?.nom || 'MAISON DES TELEPHONES',
          adresse: 'Abidjan, Côte d\'Ivoire',
          telephone: '+225 XX XX XX XX',
          email: '<EMAIL>'
        },
        vente: {
          numero_facture: vente.numero_facture,
          date_vente: vente.date_vente,
          employe_nom: vente.employes ? `${vente.employes.nom} ${vente.employes.prenom}` : undefined,
          client_nom: vente.clients ? 
            `${vente.clients.nom} ${vente.clients.prenom || ''}`.trim() : 
            vente.client_nom || undefined,
          client_telephone: vente.clients?.telephone || vente.client_telephone || undefined
        },
        produits: vente.vente_details.map(detail => ({
          nom: detail.produits?.nom || 'Produit',
          quantite: detail.quantite,
          prix_unitaire: detail.prix_unitaire,
          sous_total: detail.sous_total
        })),
        totaux: {
          sous_total: vente.montant_total - vente.montant_tva,
          montant_tva: vente.montant_tva,
          montant_total: vente.montant_total,
          mode_paiement: vente.mode_paiement.charAt(0).toUpperCase() + vente.mode_paiement.slice(1).replace('_', ' ')
        }
      };

      // Obtenir l'imprimante préférée pour cette boutique
      const boutiqueId = vente.boutique_id || '';
      const preference = thermalReceiptService.getPrinterPreference(boutiqueId);
      
      await thermalReceiptService.printReceipt(receiptData, preference?.printerId);
      
      toast({
        title: 'Impression réussie',
        description: 'Le reçu a été envoyé à l\'imprimante thermique',
      });
    } catch (error: any) {
      console.error('Erreur impression thermique:', error);
      toast({
        title: 'Erreur d\'impression',
        description: error.message || 'Impossible d\'imprimer le reçu thermique',
        variant: 'destructive',
      });
    }
  };


  return (
    <div className="space-y-6">
      {/* En-tête */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <FileText className="h-6 w-6 text-primary" />
              <div>
                <CardTitle className="text-2xl">{vente.numero_facture}</CardTitle>
                <div className="text-muted-foreground">
                  {format(new Date(vente.date_vente), 'dd MMMM yyyy à HH:mm', { locale: fr })}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={getStatutVariant(vente.statut)}>
                {vente.statut.toUpperCase()}
              </Badge>
              {vente.statut === 'validee' && (
                <Dialog open={showActions} onOpenChange={setShowActions}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4 mr-2" />
                      Actions
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Actions sur la vente</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-2">
                      <Button
                        variant="outline"
                        className="w-full justify-start"
                        onClick={() => changerStatut('annulee')}
                        disabled={updateVente.isPending}
                      >
                        <X className="h-4 w-4 mr-2" />
                        Annuler la vente
                      </Button>
                      <Button
                        variant="outline"
                        className="w-full justify-start"
                        onClick={() => changerStatut('remboursee')}
                        disabled={updateVente.isPending}
                      >
                        <RotateCcw className="h-4 w-4 mr-2" />
                        Rembourser
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Informations générales */}
        <div className="lg:col-span-2 space-y-6">
          {/* Détails de la vente */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Produits vendus
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {vente.vente_details.map((detail) => (
                  <div key={detail.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium">{detail.produits?.nom}</div>
                      <div className="text-sm text-muted-foreground">
                        {detail.produits?.marque} {detail.produits?.modele}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Code: {detail.produits?.code_produit}
                      </div>
                    </div>
                    <div className="text-right space-y-1">
                      <div className="text-sm">
                        {detail.quantite} × {formatCurrency(detail.prix_unitaire)}
                      </div>
                      {detail.remise && detail.remise > 0 && (
                        <div className="text-sm text-green-600">
                          - {formatCurrency(detail.remise)} (remise)
                        </div>
                      )}
                      <div className="font-bold">
                        {formatCurrency(detail.sous_total)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <Separator className="my-4" />

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Sous-total</span>
                  <span>{formatCurrency(vente.montant_total - vente.montant_tva)}</span>
                </div>
                <div className="flex justify-between">
                  <span>TVA (18%)</span>
                  <span>{formatCurrency(vente.montant_tva)}</span>
                </div>
                <Separator />
                <div className="flex justify-between text-xl font-bold">
                  <span>Total</span>
                  <span>{formatCurrency(vente.montant_total)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar avec informations */}
        <div className="space-y-6">
          {/* Informations client */}
          {(vente.clients || vente.client_nom) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Client
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="font-medium">
                  {vente.clients ? 
                    `${vente.clients.nom} ${vente.clients.prenom || ''}`.trim() : 
                    vente.client_nom
                  }
                </div>
                {(vente.clients?.telephone || vente.client_telephone) && (
                  <div className="text-sm text-muted-foreground">
                    📞 {vente.clients?.telephone || vente.client_telephone}
                  </div>
                )}
                {(vente.clients?.email || vente.client_email) && (
                  <div className="text-sm text-muted-foreground">
                    ✉️ {vente.clients?.email || vente.client_email}
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Informations boutique */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Boutique
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="font-medium">{vente.boutiques?.nom}</div>
            </CardContent>
          </Card>

          {/* Informations vendeur */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Vendeur
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="font-medium">
                {vente.employes?.nom} {vente.employes?.prenom}
              </div>
            </CardContent>
          </Card>

          {/* Informations paiement */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Paiement
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <span>{getModeIcon(vente.mode_paiement)}</span>
                <span className="font-medium">
                  {vente.mode_paiement.charAt(0).toUpperCase() + vente.mode_paiement.slice(1).replace('_', ' ')}
                </span>
              </div>
              <div className="text-2xl font-bold text-primary mt-2">
                {formatCurrency(vente.montant_total)}
              </div>
            </CardContent>
          </Card>

          {/* Actions d'impression */}
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button 
                onClick={imprimerRecuThermique}
                className="w-full justify-start"
                variant="default"
              >
                <Receipt className="h-4 w-4 mr-2" />
                Imprimer Reçu
              </Button>
              
              <InvoicePrintDialog vente={vente}>
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                >
                  <Printer className="h-4 w-4 mr-2" />
                  Facture PDF
                </Button>
              </InvoicePrintDialog>
            </CardContent>
          </Card>
        </div>
      </div>

      {onClose && (
        <div className="flex justify-end">
          <Button variant="outline" onClick={onClose}>
            Fermer
          </Button>
        </div>
      )}
    </div>
  );
};