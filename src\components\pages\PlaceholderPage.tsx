import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Construction, ArrowLeft } from "lucide-react";

interface PlaceholderPageProps {
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  onBack: () => void;
}

export const PlaceholderPage = ({ title, description, icon: Icon, onBack }: PlaceholderPageProps) => {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="ghost" onClick={onBack} className="flex items-center gap-2">
          <ArrowLeft className="h-4 w-4" />
          Retour
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-foreground flex items-center gap-2">
            <Icon className="h-6 w-6 text-primary" />
            {title}
          </h1>
          <p className="text-muted-foreground">{description}</p>
        </div>
      </div>
      
      <Card className="max-w-2xl mx-auto">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2 text-xl">
            <Construction className="h-6 w-6 text-warning" />
            Page en Développement
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-muted-foreground">
            Cette fonctionnalité est actuellement en cours de développement dans le cadre 
            du système de gestion multi-boutiques de La Maison des Téléphones.
          </p>
          <div className="space-y-2">
            <h3 className="font-semibold">Fonctionnalités prévues :</h3>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Interface complète de gestion</li>
              <li>• Synchronisation en temps réel</li>
              <li>• Alertes et notifications</li>
              <li>• Rapports détaillés</li>
            </ul>
          </div>
          <Button onClick={onBack} className="mt-4">
            Retour au Dashboard
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};