import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useTransfertById, useUpdateTransfert } from '@/hooks/useTransferts';
import { useTransfertDetails } from '@/hooks/useTransfertDetails';
import { Package, Calendar, User, MapPin, Truck, CheckCircle, XCircle, Clock, ArrowRight } from 'lucide-react';
import { formatDate, formatDateTime } from '@/lib/formatters';
import { useToast } from '@/hooks/use-toast';

interface TransfertDetailProps {
  transfertId: string;
}

export const TransfertDetail = ({ transfertId }: TransfertDetailProps) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const { toast } = useToast();

  const { data: transfert, isLoading: loadingTransfert } = useTransfertById(transfertId);
  const { data: details, isLoading: loadingDetails } = useTransfertDetails(transfertId);
  const updateTransfert = useUpdateTransfert();

  const getStatutVariant = (statut: string) => {
    switch (statut) {
      case 'en_attente': return 'secondary';
      case 'expedie': return 'outline';
      case 'en_transit': return 'outline';
      case 'recu': return 'default';
      case 'annule': return 'destructive';
      default: return 'default';
    }
  };

  const getStatutIcon = (statut: string) => {
    switch (statut) {
      case 'en_attente': return <Clock className="h-4 w-4" />;
      case 'expedie': return <Truck className="h-4 w-4" />;
      case 'en_transit': return <Truck className="h-4 w-4" />;
      case 'recu': return <CheckCircle className="h-4 w-4" />;
      case 'annule': return <XCircle className="h-4 w-4" />;
      default: return <Package className="h-4 w-4" />;
    }
  };

  const getStatutLabel = (statut: string) => {
    const labels = {
      'en_attente': 'En attente',
      'expedie': 'Expédié',
      'en_transit': 'En transit',
      'recu': 'Reçu',
      'annule': 'Annulé'
    };
    return labels[statut as keyof typeof labels] || statut;
  };

  const handleStatusUpdate = async (newStatus: string) => {
    if (!transfert) return;
    
    setIsUpdating(true);
    try {
      await updateTransfert.mutateAsync({
        id: transfert.id,
        statut: newStatus as any,
      });
      
      toast({
        title: "Statut mis à jour",
        description: `Le transfert est maintenant ${getStatutLabel(newStatus)}.`,
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de mettre à jour le statut du transfert.",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const canExpedite = transfert?.statut === 'en_attente';
  const canSetInTransit = transfert?.statut === 'expedie';
  const canReceive = transfert?.statut === 'en_transit' || transfert?.statut === 'expedie';
  const canCancel = transfert?.statut === 'en_attente' || transfert?.statut === 'expedie';

  if (loadingTransfert || loadingDetails) {
    return <div className="flex justify-center p-8">Chargement...</div>;
  }

  if (!transfert) {
    return <div className="text-center p-8">Transfert non trouvé</div>;
  }

  const totalQuantite = details?.reduce((sum, detail) => sum + detail.quantite, 0) || 0;

  return (
    <div className="space-y-6">
      {/* En-tête du transfert */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-3">
              {getStatutIcon(transfert.statut)}
              Transfert #{transfert.id.substring(0, 8)}
            </CardTitle>
            <Badge variant={getStatutVariant(transfert.statut)} className="text-sm">
              {getStatutLabel(transfert.statut)}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Informations du transfert */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Boutique source:</span>
                <span>{transfert.boutique_source?.nom}</span>
              </div>
              <div className="flex items-center gap-2">
                <ArrowRight className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Boutique destination:</span>
                <span>{transfert.boutique_destination?.nom}</span>
              </div>
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Expéditeur:</span>
                <span>
                  {transfert.employe_expediteur?.prenom} {transfert.employe_expediteur?.nom}
                </span>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Créé le:</span>
                <span>{formatDateTime(transfert.created_at)}</span>
              </div>
              {transfert.date_expedition && (
                <div className="flex items-center gap-2">
                  <Truck className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Expédié le:</span>
                  <span>{formatDateTime(transfert.date_expedition)}</span>
                </div>
              )}
              {transfert.date_reception && (
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-success" />
                  <span className="font-medium">Reçu le:</span>
                  <span>{formatDateTime(transfert.date_reception)}</span>
                </div>
              )}
            </div>
          </div>

          {transfert.commentaires && (
            <>
              <Separator />
              <div>
                <span className="font-medium">Commentaires:</span>
                <p className="text-muted-foreground mt-1">{transfert.commentaires}</p>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Actions du workflow */}
      <Card>
        <CardHeader>
          <CardTitle>Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {canExpedite && (
              <Button 
                onClick={() => handleStatusUpdate('expedie')}
                disabled={isUpdating}
              >
                <Truck className="h-4 w-4 mr-2" />
                Marquer comme expédié
              </Button>
            )}
            
            {canSetInTransit && (
              <Button 
                variant="outline"
                onClick={() => handleStatusUpdate('en_transit')}
                disabled={isUpdating}
              >
                <Truck className="h-4 w-4 mr-2" />
                Marquer en transit
              </Button>
            )}
            
            {canReceive && (
              <Button 
                variant="default"
                onClick={() => handleStatusUpdate('recu')}
                disabled={isUpdating}
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Marquer comme reçu
              </Button>
            )}
            
            {canCancel && (
              <Button 
                variant="destructive" 
                onClick={() => handleStatusUpdate('annule')}
                disabled={isUpdating}
              >
                <XCircle className="h-4 w-4 mr-2" />
                Annuler le transfert
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Détails des produits */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Produits transférés ({totalQuantite} articles)
          </CardTitle>
        </CardHeader>
        <CardContent>
          {details && details.length > 0 ? (
            <div className="space-y-3">
              {details.map((detail) => (
                <div key={detail.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1">
                    <div className="font-medium">
                      {detail.produits?.marque} {detail.produits?.nom}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Modèle: {detail.produits?.modele} • Code: {detail.produits?.code_produit}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">Quantité: {detail.quantite}</div>
                    {detail.produits?.prix_vente && (
                      <div className="text-sm text-muted-foreground">
                        Valeur unitaire: {detail.produits.prix_vente}€
                      </div>
                    )}
                  </div>
                </div>
              ))}
              
              <Separator />
              
              <div className="flex justify-between items-center font-medium">
                <span>Total des articles:</span>
                <span>{totalQuantite}</span>
              </div>
              
              {details.some(d => d.produits?.prix_vente) && (
                <div className="flex justify-between items-center font-medium">
                  <span>Valeur totale estimée:</span>
                  <span>
                    {details.reduce((sum, detail) => 
                      sum + (detail.produits?.prix_vente || 0) * detail.quantite, 0
                    ).toFixed(2)}€
                  </span>
                </div>
              )}
            </div>
          ) : (
            <p className="text-muted-foreground text-center py-4">
              Aucun produit dans ce transfert
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};