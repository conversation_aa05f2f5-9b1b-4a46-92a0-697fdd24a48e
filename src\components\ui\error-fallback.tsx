import React from 'react';
import { AlertTriangle, RefreshCw, Home, ArrowLeft } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ErrorFallbackProps {
  error?: Error | null;
  resetError?: () => void;
  title?: string;
  description?: string;
  showRetry?: boolean;
  showHome?: boolean;
  className?: string;
}

export const ErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  resetError,
  title = "Une erreur s'est produite",
  description,
  showRetry = true,
  showHome = false,
  className = ""
}) => {
  const errorMessage = error?.message || description || "Une erreur inattendue s'est produite";

  return (
    <div className={`flex items-center justify-center min-h-[300px] p-4 ${className}`}>
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <AlertTriangle className="h-12 w-12 text-destructive" />
          </div>
          <CardTitle className="text-xl">{title}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="text-sm">
              {errorMessage}
            </AlertDescription>
          </Alert>
          
          <div className="flex flex-col gap-2">
            {showRetry && resetError && (
              <Button onClick={resetError} variant="outline" className="w-full">
                <RefreshCw className="h-4 w-4 mr-2" />
                Réessayer
              </Button>
            )}
            
            {showHome && (
              <Button 
                onClick={() => window.location.href = '/'} 
                variant="default" 
                className="w-full"
              >
                <Home className="h-4 w-4 mr-2" />
                Retour à l'accueil
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Fallback pour les données vides
interface EmptyStateFallbackProps {
  icon?: React.ReactNode;
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
    icon?: React.ReactNode;
  };
  className?: string;
}

export const EmptyStateFallback: React.FC<EmptyStateFallbackProps> = ({
  icon,
  title,
  description,
  action,
  className = ""
}) => {
  return (
    <div className={`flex flex-col items-center justify-center min-h-[300px] p-8 text-center ${className}`}>
      <div className="mb-4 opacity-60">
        {icon || <AlertTriangle className="h-16 w-16" />}
      </div>
      
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <p className="text-muted-foreground mb-6 max-w-md">{description}</p>
      
      {action && (
        <Button onClick={action.onClick} variant="default">
          {action.icon && <span className="mr-2">{action.icon}</span>}
          {action.label}
        </Button>
      )}
    </div>
  );
};

// Fallback pour les données en cours de chargement avec retry
interface LoadingWithRetryProps {
  isLoading: boolean;
  error?: Error | null;
  retry?: () => void;
  data?: any;
  emptyState?: React.ReactNode;
  children: React.ReactNode;
  loadingComponent?: React.ReactNode;
}

export const LoadingWithRetry: React.FC<LoadingWithRetryProps> = ({
  isLoading,
  error,
  retry,
  data,
  emptyState,
  children,
  loadingComponent
}) => {
  if (isLoading && loadingComponent) {
    return <>{loadingComponent}</>;
  }

  if (error) {
    return <ErrorFallback error={error} resetError={retry} showRetry={!!retry} />;
  }

  if (!data || (Array.isArray(data) && data.length === 0)) {
    return emptyState ? <>{emptyState}</> : null;
  }

  return <>{children}</>;
};

// Fallback pour les connexions réseau
export const NetworkErrorFallback: React.FC<{ retry?: () => void }> = ({ retry }) => {
  return (
    <ErrorFallback
      title="Problème de connexion"
      description="Impossible de se connecter au serveur. Vérifiez votre connexion internet."
      resetError={retry}
      showRetry={!!retry}
    />
  );
};

// Fallback pour les permissions insuffisantes
export const PermissionErrorFallback: React.FC = () => {
  return (
    <ErrorFallback
      title="Accès refusé"
      description="Vous n'avez pas les permissions nécessaires pour accéder à cette ressource."
      showRetry={false}
      showHome={true}
    />
  );
};

// Fallback pour les pages non trouvées
export const NotFoundFallback: React.FC<{ onGoBack?: () => void }> = ({ onGoBack }) => {
  return (
    <div className="flex items-center justify-center min-h-[400px] p-4">
      <Card className="w-full max-w-md text-center">
        <CardContent className="pt-6">
          <div className="text-6xl font-bold text-muted-foreground mb-4">404</div>
          <h2 className="text-xl font-semibold mb-2">Page non trouvée</h2>
          <p className="text-muted-foreground mb-6">
            La page que vous recherchez n'existe pas ou a été déplacée.
          </p>
          
          <div className="flex flex-col gap-2">
            {onGoBack && (
              <Button onClick={onGoBack} variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Button>
            )}
            
            <Button onClick={() => window.location.href = '/'}>
              <Home className="h-4 w-4 mr-2" />
              Accueil
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};