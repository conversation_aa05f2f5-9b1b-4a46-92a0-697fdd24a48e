import { z } from "zod";

// Validation pour les boutiques
export const boutiqueSchema = z.object({
  nom: z.string().min(2, "Le nom doit contenir au moins 2 caractères"),
  type: z.enum(["centrale", "boutique"], {
    required_error: "Le type de boutique est requis"
  }),
  adresse: z.string().min(5, "L'adresse doit contenir au moins 5 caractères"),
  telephone: z.string().optional(),
  email: z.string().email("Format d'email invalide").optional().or(z.literal("")),
  statut: z.enum(["active", "inactive", "maintenance"]).default("active")
});

// Validation pour les employés
export const employeSchema = z.object({
  nom: z.string().min(2, "Le nom doit contenir au moins 2 caractères"),
  prenom: z.string().min(2, "Le prénom doit contenir au moins 2 caractères"),
  email: z.string().email("Format d'email invalide"),
  telephone: z.string().optional(),
  poste: z.string().min(2, "Le poste doit contenir au moins 2 caractères"),
  boutique_id: z.string().uuid("ID boutique invalide").optional(),
  salaire: z.number().positive("Le salaire doit être positif").optional(),
  date_embauche: z.date({
    required_error: "La date d'embauche est requise"
  }),
  statut: z.enum(["actif", "inactif", "suspendu"]).default("actif")
});

// Validation pour les produits
export const produitSchema = z.object({
  nom: z.string().min(2, "Le nom doit contenir au moins 2 caractères"),
  marque: z.string().min(1, "La marque est requise"),
  modele: z.string().min(1, "Le modèle est requis"),
  code_produit: z.string().min(3, "Le code produit doit contenir au moins 3 caractères"),
  prix_achat: z.number().positive("Le prix d'achat doit être positif"),
  prix_vente: z.number().positive("Le prix de vente doit être positif"),
  categorie_id: z.string().uuid("ID catégorie invalide").optional(),
  description: z.string().optional(),
  couleur: z.string().optional(),
  imei: z.string().optional(),
  etat: z.enum(["neuf", "occasion", "defectueux"]).default("neuf"),
  stockage: z.string().optional()
});

// Validation pour les stocks
export const stockSchema = z.object({
  produit_id: z.string().uuid("ID produit invalide"),
  boutique_id: z.string().uuid("ID boutique invalide"),
  quantite: z.number().int().min(0, "La quantité ne peut pas être négative"),
  seuil_alerte: z.number().int().min(1, "Le seuil d'alerte doit être au minimum 1"),
  emplacement: z.string().optional()
});

// Validation pour les ventes
export const venteSchema = z.object({
  boutique_id: z.string().uuid("ID boutique invalide"),
  employe_id: z.string().uuid("ID employé invalide"),
  client_nom: z.string().optional(),
  client_email: z.string().email("Format d'email invalide").optional().or(z.literal("")),
  client_telephone: z.string().optional().refine((tel) => {
    if (!tel) return true;
    return /^\+?[0-9\s\-\(\)]{8,20}$/.test(tel);
  }, "Format de téléphone invalide"),
  mode_paiement: z.enum(["especes", "carte", "virement", "cheque", "mobile_money", "crypto"], {
    required_error: "Le mode de paiement est requis"
  }),
  montant_total: z.number().positive("Le montant total doit être positif"),
  montant_tva: z.number().min(0, "La TVA ne peut pas être négative").default(0),
  statut: z.enum(["validee", "annulee", "en_attente"]).default("validee"),
  date_vente: z.string().optional().refine((date) => {
    if (!date) return true;
    const venteDate = new Date(date);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    return venteDate <= tomorrow;
  }, "La date de vente ne peut pas être dans le futur")
});

// Validation pour les détails de vente
export const venteDetailSchema = z.object({
  vente_id: z.string().uuid("ID vente invalide"),
  produit_id: z.string().uuid("ID produit invalide"),
  quantite: z.number().int().positive("La quantité doit être positive"),
  prix_unitaire: z.number().positive("Le prix unitaire doit être positif"),
  remise: z.number().min(0, "La remise ne peut pas être négative").default(0)
});

// Validation pour les transferts
export const transfertSchema = z.object({
  boutique_source_id: z.string().uuid("ID boutique source invalide"),
  boutique_destination_id: z.string().uuid("ID boutique destination invalide"),
  employe_expediteur_id: z.string().uuid("ID employé expéditeur invalide"),
  employe_recepteur_id: z.string().uuid("ID employé récepteur invalide").optional(),
  commentaires: z.string().optional(),
  statut: z.enum(["en_attente", "expediee", "recue", "annulee"]).default("en_attente")
});

// Validation pour les détails de transfert
export const transfertDetailSchema = z.object({
  transfert_id: z.string().uuid("ID transfert invalide"),
  produit_id: z.string().uuid("ID produit invalide"),
  quantite: z.number().int().positive("La quantité doit être positive")
});

// Validation pour les alertes
export const alerteSchema = z.object({
  titre: z.string().min(5, "Le titre doit contenir au moins 5 caractères"),
  message: z.string().min(10, "Le message doit contenir au moins 10 caractères"),
  type: z.enum(["stock", "vente", "transfert", "systeme"], {
    required_error: "Le type d'alerte est requis"
  }),
  niveau: z.enum(["info", "warning", "error", "success"]).default("info"),
  boutique_id: z.string().uuid("ID boutique invalide").optional(),
  employe_id: z.string().uuid("ID employé invalide").optional(),
  produit_id: z.string().uuid("ID produit invalide").optional(),
  date_expiration: z.date().optional()
});

// Types inférés pour TypeScript
export type BoutiqueFormData = z.infer<typeof boutiqueSchema>;
export type EmployeFormData = z.infer<typeof employeSchema>;
export type ProduitFormData = z.infer<typeof produitSchema>;
export type StockFormData = z.infer<typeof stockSchema>;
export type VenteFormData = z.infer<typeof venteSchema>;
export type VenteDetailFormData = z.infer<typeof venteDetailSchema>;
export type TransfertFormData = z.infer<typeof transfertSchema>;
export type TransfertDetailFormData = z.infer<typeof transfertDetailSchema>;
export type AlerteFormData = z.infer<typeof alerteSchema>;

// Validation pour les filtres et recherches
export const searchFiltersSchema = z.object({
  search: z.string().optional(),
  boutique_id: z.string().uuid().optional(),
  categorie_id: z.string().uuid().optional(),
  marque: z.string().optional(),
  etat: z.enum(["neuf", "occasion", "defectueux"]).optional(),
  prix_min: z.number().optional(),
  prix_max: z.number().optional(),
  statut: z.string().optional(),
  date_debut: z.date().optional(),
  date_fin: z.date().optional(),
  limit: z.number().int().positive().max(100).default(50),
  offset: z.number().int().min(0).default(0)
});

export type SearchFilters = z.infer<typeof searchFiltersSchema>;