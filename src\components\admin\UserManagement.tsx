import React, { useState } from 'react';
import { useAuth, UserRole } from '@/hooks/useAuth';
import { useUserManagement, UserManagementProfile } from '@/hooks/useUserManagement';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Plus, Edit, Key, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';


export const UserManagement: React.FC = () => {
  const { profile } = useAuth();
  const { users, isLoading, createUserMutation, updateUserMutation, generatePinMutation } = useUserManagement();
  const [selectedUser, setSelectedUser] = useState<UserManagementProfile | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isPinDialogOpen, setIsPinDialogOpen] = useState(false);
  
  // Formulaire pour créer/modifier un utilisateur
  const [formData, setFormData] = useState({
    employe_id: '',
    role: 'vendeur' as UserRole,
    email: '',
    password: '',
    pin_code: '',
    is_active: true,
  });


  const resetForm = () => {
    setFormData({
      employe_id: '',
      role: 'vendeur',
      email: '',
      password: '',
      pin_code: '',
      is_active: true,
    });
  };

  const handleCreateUser = () => {
    if (!formData.email || !formData.password) {
      toast.error('Veuillez remplir tous les champs obligatoires');
      return;
    }
    createUserMutation.mutate(formData);
    if (!createUserMutation.isPending) {
      setIsCreateDialogOpen(false);
      resetForm();
    }
  };

  const handleEditUser = () => {
    if (!selectedUser) return;
    updateUserMutation.mutate({
      userId: selectedUser.user_id,
      updates: {
        role: formData.role,
        is_active: formData.is_active,
        employe_id: formData.employe_id || null,
      },
    });
    if (!updateUserMutation.isPending) {
      setIsEditDialogOpen(false);
      setSelectedUser(null);
      resetForm();
    }
  };

  const getRoleBadgeVariant = (role: UserRole) => {
    switch (role) {
      case 'admin_system': return 'destructive';
      case 'proprietaire': return 'default';
      case 'manager': return 'secondary';
      default: return 'outline';
    }
  };

  const getRoleLabel = (role: UserRole) => {
    switch (role) {
      case 'admin_system': return 'Admin Système';
      case 'proprietaire': return 'Propriétaire';
      case 'manager': return 'Manager';
      case 'vendeur': return 'Vendeur';
      case 'caissier': return 'Caissier';
    }
  };

  // Vérifier si l'utilisateur actuel peut gérer les utilisateurs
  if (profile?.role !== 'admin_system') {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-destructive" />
            Accès non autorisé
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Seuls les administrateurs système peuvent gérer les utilisateurs.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Gestion des Utilisateurs</CardTitle>
              <CardDescription>
                Créez et gérez les comptes utilisateurs du système
              </CardDescription>
            </div>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={() => { resetForm(); setIsCreateDialogOpen(true); }}>
                  <Plus className="h-4 w-4 mr-2" />
                  Créer un utilisateur
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Créer un nouvel utilisateur</DialogTitle>
                  <DialogDescription>
                    Créer un nouveau compte utilisateur dans le système
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                      placeholder="Email de connexion"
                    />
                  </div>

                  <div>
                    <Label htmlFor="password">Mot de passe</Label>
                    <Input
                      id="password"
                      type="password"
                      value={formData.password}
                      onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                      placeholder="Mot de passe"
                    />
                  </div>

                  <div>
                    <Label htmlFor="role">Rôle</Label>
                    <Select value={formData.role} onValueChange={(value: UserRole) => setFormData({ ...formData, role: value })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="vendeur">Vendeur</SelectItem>
                        <SelectItem value="caissier">Caissier</SelectItem>
                        <SelectItem value="manager">Manager</SelectItem>
                        <SelectItem value="proprietaire">Propriétaire</SelectItem>
                        <SelectItem value="admin_system">Admin Système</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Button 
                    onClick={handleCreateUser} 
                    className="w-full"
                    disabled={createUserMutation.isPending}
                  >
                    {createUserMutation.isPending ? 'Création...' : 'Créer l\'utilisateur'}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-muted-foreground">Chargement des utilisateurs...</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Employé</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Rôle</TableHead>
                  <TableHead>Boutique</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead>PIN</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{user.employe_prenom} {user.employe_nom}</p>
                      </div>
                    </TableCell>
                    <TableCell>{user.employe_email}</TableCell>
                    <TableCell>
                      <Badge variant={getRoleBadgeVariant(user.role)}>
                        {getRoleLabel(user.role)}
                      </Badge>
                    </TableCell>
                    <TableCell>{user.boutique_nom || 'Aucune'}</TableCell>
                    <TableCell>
                      <Badge variant={user.is_active ? 'default' : 'destructive'}>
                        {user.is_active ? 'Actif' : 'Inactif'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={user.pin_code ? 'default' : 'outline'}>
                        {user.pin_code ? 'Configuré' : 'Non configuré'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedUser(user);
                            setFormData({
                              employe_id: user.employe_id,
                              role: user.role,
                              email: user.employe_email,
                              password: '',
                              pin_code: user.pin_code || '',
                              is_active: user.is_active,
                            });
                            setIsEditDialogOpen(true);
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedUser(user);
                            setIsPinDialogOpen(true);
                          }}
                        >
                          <Key className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Dialog d'édition */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Modifier l'utilisateur</DialogTitle>
            <DialogDescription>
              Modifier les permissions et paramètres de l'utilisateur
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit_role">Rôle</Label>
              <Select value={formData.role} onValueChange={(value: UserRole) => setFormData({ ...formData, role: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="vendeur">Vendeur</SelectItem>
                  <SelectItem value="caissier">Caissier</SelectItem>
                  <SelectItem value="manager">Manager</SelectItem>
                  <SelectItem value="proprietaire">Propriétaire</SelectItem>
                  <SelectItem value="admin_system">Admin Système</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button 
              onClick={handleEditUser} 
              className="w-full"
              disabled={updateUserMutation.isPending}
            >
              {updateUserMutation.isPending ? 'Modification...' : 'Modifier'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Dialog de génération de PIN */}
      <Dialog open={isPinDialogOpen} onOpenChange={setIsPinDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Générer un nouveau code PIN</DialogTitle>
            <DialogDescription>
              Générer un nouveau code PIN pour {selectedUser?.employe_prenom} {selectedUser?.employe_nom}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Un nouveau code PIN de 4 chiffres sera généré automatiquement. 
            </p>
            
            <Button 
              onClick={() => {
                if (selectedUser) {
                  generatePinMutation.mutate(selectedUser.user_id);
                  if (!generatePinMutation.isPending) {
                    setIsPinDialogOpen(false);
                  }
                }
              }} 
              className="w-full"
              disabled={generatePinMutation.isPending}
            >
              {generatePinMutation.isPending ? 'Génération...' : 'Générer un nouveau PIN'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};