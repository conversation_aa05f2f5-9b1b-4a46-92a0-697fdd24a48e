import { StatsCards } from "./StatsCards";
import { BoutiquesOverview } from "./BoutiquesOverview";
import { RecentActivity } from "./RecentActivity";
import { ErrorBoundary } from "@/components/ui/error-boundary";

export const Dashboard = () => {
  return (
    <ErrorBoundary>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Tableau de Bord</h1>
          <p className="text-muted-foreground">
            Vue d'ensemble de votre réseau de boutiques - Maison des Téléphones
          </p>
        </div>
        
        <ErrorBoundary>
          <StatsCards />
        </ErrorBoundary>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <ErrorBoundary>
              <BoutiquesOverview />
            </ErrorBoundary>
          </div>
          <div>
            <ErrorBoundary>
              <RecentActivity />
            </ErrorBoundary>
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
};