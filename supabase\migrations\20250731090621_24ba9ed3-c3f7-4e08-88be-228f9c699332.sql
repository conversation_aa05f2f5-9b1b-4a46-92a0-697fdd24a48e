-- Création du schéma complet pour La Maison des Téléphones

-- Table des boutiques
CREATE TABLE public.boutiques (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    nom TEXT NOT NULL,
    adresse TEXT NOT NULL,
    telephone TEXT,
    email TEXT,
    type TEXT NOT NULL CHECK (type IN ('centrale', 'boutique')),
    statut TEXT NOT NULL DEFAULT 'active' CHECK (statut IN ('active', 'inactive', 'maintenance')),
    manager_id UUID,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Table des employés
CREATE TABLE public.employes (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    nom TEXT NOT NULL,
    prenom TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    telephone TEXT,
    poste TEXT NOT NULL CHECK (poste IN ('manager', 'vendeur', 'caissier', 'technicien')),
    boutique_id UUID REFERENCES public.boutiques(id) ON DELETE SET NULL,
    statut TEXT NOT NULL DEFAULT 'actif' CHECK (statut IN ('actif', 'inactif', 'conge')),
    salaire DECIMAL(10,2),
    date_embauche DATE NOT NULL DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Table des catégories de produits
CREATE TABLE public.categories (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    nom TEXT NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Table des produits
CREATE TABLE public.produits (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    nom TEXT NOT NULL,
    marque TEXT NOT NULL,
    modele TEXT NOT NULL,
    prix_achat DECIMAL(10,2) NOT NULL,
    prix_vente DECIMAL(10,2) NOT NULL,
    code_produit TEXT UNIQUE NOT NULL,
    imei TEXT,
    categorie_id UUID REFERENCES public.categories(id),
    description TEXT,
    couleur TEXT,
    stockage TEXT,
    etat TEXT NOT NULL DEFAULT 'neuf' CHECK (etat IN ('neuf', 'reconditionne', 'occasion')),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Table des stocks par boutique
CREATE TABLE public.stocks (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    produit_id UUID REFERENCES public.produits(id) ON DELETE CASCADE,
    boutique_id UUID REFERENCES public.boutiques(id) ON DELETE CASCADE,
    quantite INTEGER NOT NULL DEFAULT 0 CHECK (quantite >= 0),
    seuil_alerte INTEGER NOT NULL DEFAULT 5,
    emplacement TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    UNIQUE(produit_id, boutique_id)
);

-- Table des transferts entre boutiques
CREATE TABLE public.transferts (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    boutique_source_id UUID REFERENCES public.boutiques(id),
    boutique_destination_id UUID REFERENCES public.boutiques(id),
    employe_expediteur_id UUID REFERENCES public.employes(id),
    employe_recepteur_id UUID,
    statut TEXT NOT NULL DEFAULT 'en_attente' CHECK (statut IN ('en_attente', 'expedie', 'en_transit', 'recu', 'annule')),
    date_expedition TIMESTAMP WITH TIME ZONE,
    date_reception TIMESTAMP WITH TIME ZONE,
    commentaires TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Table des détails de transferts
CREATE TABLE public.transfert_details (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    transfert_id UUID REFERENCES public.transferts(id) ON DELETE CASCADE,
    produit_id UUID REFERENCES public.produits(id),
    quantite INTEGER NOT NULL CHECK (quantite > 0),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Table des ventes
CREATE TABLE public.ventes (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    numero_facture TEXT UNIQUE NOT NULL,
    boutique_id UUID REFERENCES public.boutiques(id),
    employe_id UUID REFERENCES public.employes(id),
    client_nom TEXT,
    client_telephone TEXT,
    client_email TEXT,
    montant_total DECIMAL(10,2) NOT NULL,
    montant_tva DECIMAL(10,2) NOT NULL DEFAULT 0,
    mode_paiement TEXT NOT NULL CHECK (mode_paiement IN ('especes', 'carte', 'cheque', 'virement')),
    statut TEXT NOT NULL DEFAULT 'validee' CHECK (statut IN ('validee', 'annulee', 'remboursee')),
    date_vente TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Table des détails de vente
CREATE TABLE public.vente_details (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    vente_id UUID REFERENCES public.ventes(id) ON DELETE CASCADE,
    produit_id UUID REFERENCES public.produits(id),
    quantite INTEGER NOT NULL CHECK (quantite > 0),
    prix_unitaire DECIMAL(10,2) NOT NULL,
    remise DECIMAL(5,2) DEFAULT 0,
    sous_total DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Table des alertes
CREATE TABLE public.alertes (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    type TEXT NOT NULL CHECK (type IN ('stock_bas', 'stock_epuise', 'transfert_retard', 'vente_importante', 'systeme')),
    titre TEXT NOT NULL,
    message TEXT NOT NULL,
    boutique_id UUID REFERENCES public.boutiques(id),
    produit_id UUID REFERENCES public.produits(id),
    employe_id UUID REFERENCES public.employes(id),
    niveau TEXT NOT NULL DEFAULT 'info' CHECK (niveau IN ('info', 'warning', 'error', 'success')),
    lu BOOLEAN NOT NULL DEFAULT false,
    date_expiration TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Ajout de la contrainte pour manager_id après création de la table employes
ALTER TABLE public.boutiques ADD CONSTRAINT fk_boutiques_manager 
FOREIGN KEY (manager_id) REFERENCES public.employes(id);

-- Enable RLS sur toutes les tables
ALTER TABLE public.boutiques ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.employes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.produits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transferts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transfert_details ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ventes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.vente_details ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.alertes ENABLE ROW LEVEL SECURITY;

-- Création des politiques RLS (accès complet pour les utilisateurs authentifiés)
CREATE POLICY "Allow full access for authenticated users" ON public.boutiques FOR ALL USING (true);
CREATE POLICY "Allow full access for authenticated users" ON public.employes FOR ALL USING (true);
CREATE POLICY "Allow full access for authenticated users" ON public.categories FOR ALL USING (true);
CREATE POLICY "Allow full access for authenticated users" ON public.produits FOR ALL USING (true);
CREATE POLICY "Allow full access for authenticated users" ON public.stocks FOR ALL USING (true);
CREATE POLICY "Allow full access for authenticated users" ON public.transferts FOR ALL USING (true);
CREATE POLICY "Allow full access for authenticated users" ON public.transfert_details FOR ALL USING (true);
CREATE POLICY "Allow full access for authenticated users" ON public.ventes FOR ALL USING (true);
CREATE POLICY "Allow full access for authenticated users" ON public.vente_details FOR ALL USING (true);
CREATE POLICY "Allow full access for authenticated users" ON public.alertes FOR ALL USING (true);

-- Fonction pour mettre à jour le timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers pour updated_at
CREATE TRIGGER update_boutiques_updated_at BEFORE UPDATE ON public.boutiques
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_employes_updated_at BEFORE UPDATE ON public.employes
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_produits_updated_at BEFORE UPDATE ON public.produits
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_stocks_updated_at BEFORE UPDATE ON public.stocks
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_transferts_updated_at BEFORE UPDATE ON public.transferts
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_ventes_updated_at BEFORE UPDATE ON public.ventes
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Fonction pour créer des alertes automatiques de stock bas
CREATE OR REPLACE FUNCTION public.check_stock_alert()
RETURNS TRIGGER AS $$
BEGIN
    -- Vérifier si le stock est en dessous du seuil
    IF NEW.quantite <= NEW.seuil_alerte AND (OLD.quantite IS NULL OR OLD.quantite > NEW.seuil_alerte) THEN
        INSERT INTO public.alertes (type, titre, message, boutique_id, produit_id, niveau)
        SELECT 
            CASE WHEN NEW.quantite = 0 THEN 'stock_epuise' ELSE 'stock_bas' END,
            CASE WHEN NEW.quantite = 0 THEN 'Stock épuisé' ELSE 'Stock bas' END,
            CASE WHEN NEW.quantite = 0 
                THEN 'Le produit ' || p.nom || ' est épuisé dans la boutique ' || b.nom
                ELSE 'Le produit ' || p.nom || ' a un stock bas (' || NEW.quantite || ' restant) dans la boutique ' || b.nom
            END,
            NEW.boutique_id,
            NEW.produit_id,
            CASE WHEN NEW.quantite = 0 THEN 'error' ELSE 'warning' END
        FROM public.produits p, public.boutiques b
        WHERE p.id = NEW.produit_id AND b.id = NEW.boutique_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour les alertes de stock
CREATE TRIGGER stock_alert_trigger
    AFTER INSERT OR UPDATE ON public.stocks
    FOR EACH ROW EXECUTE FUNCTION public.check_stock_alert();

-- Fonction pour mettre à jour les stocks après une vente
CREATE OR REPLACE FUNCTION public.update_stock_after_sale()
RETURNS TRIGGER AS $$
BEGIN
    -- Décrémenter le stock
    UPDATE public.stocks 
    SET quantite = quantite - NEW.quantite
    WHERE produit_id = NEW.produit_id 
    AND boutique_id = (SELECT boutique_id FROM public.ventes WHERE id = NEW.vente_id);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour mise à jour stock après vente
CREATE TRIGGER update_stock_after_sale_trigger
    AFTER INSERT ON public.vente_details
    FOR EACH ROW EXECUTE FUNCTION public.update_stock_after_sale();

-- Index pour optimiser les performances
CREATE INDEX idx_stocks_boutique_produit ON public.stocks(boutique_id, produit_id);
CREATE INDEX idx_ventes_date ON public.ventes(date_vente);
CREATE INDEX idx_ventes_boutique ON public.ventes(boutique_id);
CREATE INDEX idx_transferts_statut ON public.transferts(statut);
CREATE INDEX idx_alertes_boutique_type ON public.alertes(boutique_id, type);
CREATE INDEX idx_employes_boutique ON public.employes(boutique_id);

-- Insertion de données de démonstration
-- Catégories
INSERT INTO public.categories (nom, description) VALUES
('Smartphones', 'Téléphones intelligents'),
('Accessoires', 'Accessoires pour téléphones'),
('Tablettes', 'Tablettes tactiles'),
('Réparation', 'Pièces de réparation');

-- Boutiques
INSERT INTO public.boutiques (nom, adresse, telephone, email, type) VALUES
('Centrale Paris', '123 Rue de Rivoli, 75001 Paris', '01 42 33 44 55', '<EMAIL>', 'centrale'),
('Boutique Châtelet', '45 Rue de Châtelet, 75001 Paris', '01 42 36 78 90', '<EMAIL>', 'boutique'),
('Boutique République', '78 Boulevard de la République, 75003 Paris', '01 48 87 65 43', '<EMAIL>', 'boutique'),
('Boutique Bastille', '12 Place de la Bastille, 75004 Paris', '01 43 21 98 76', '<EMAIL>', 'boutique'),
('Boutique Nation', '156 Avenue de la Nation, 75012 Paris', '01 47 97 54 32', '<EMAIL>', 'boutique'),
('Boutique Belleville', '89 Rue de Belleville, 75020 Paris', '01 46 36 25 14', '<EMAIL>', 'boutique'),
('Boutique Montparnasse', '203 Boulevard Montparnasse, 75014 Paris', '01 45 38 72 69', '<EMAIL>', 'boutique');

-- Employés
INSERT INTO public.employes (nom, prenom, email, telephone, poste, boutique_id, salaire) VALUES
('Dubois', 'Jean', '<EMAIL>', '06 12 34 56 78', 'manager', (SELECT id FROM public.boutiques WHERE nom = 'Centrale Paris'), 3500.00),
('Martin', 'Sophie', '<EMAIL>', '06 23 45 67 89', 'manager', (SELECT id FROM public.boutiques WHERE nom = 'Boutique Châtelet'), 3000.00),
('Bernard', 'Pierre', '<EMAIL>', '06 34 56 78 90', 'vendeur', (SELECT id FROM public.boutiques WHERE nom = 'Boutique Châtelet'), 2200.00),
('Durand', 'Marie', '<EMAIL>', '06 45 67 89 01', 'caissier', (SELECT id FROM public.boutiques WHERE nom = 'Boutique République'), 2000.00);

-- Produits
INSERT INTO public.produits (nom, marque, modele, prix_achat, prix_vente, code_produit, categorie_id, couleur, stockage, etat) VALUES
('iPhone 15 Pro', 'Apple', 'A2848', 800.00, 1199.00, 'IPH15P-128-BLK', (SELECT id FROM public.categories WHERE nom = 'Smartphones'), 'Noir', '128GB', 'neuf'),
('Samsung Galaxy S24', 'Samsung', 'SM-S921B', 650.00, 999.00, 'SGS24-256-WHT', (SELECT id FROM public.categories WHERE nom = 'Smartphones'), 'Blanc', '256GB', 'neuf'),
('AirPods Pro 2', 'Apple', 'MQD83', 150.00, 279.00, 'AIRP2-WHT', (SELECT id FROM public.categories WHERE nom = 'Accessoires'), 'Blanc', '', 'neuf'),
('Coque iPhone 15', 'Generic', 'COQUE-15', 5.00, 29.90, 'COQ-IPH15-CLR', (SELECT id FROM public.categories WHERE nom = 'Accessoires'), 'Transparent', '', 'neuf');

-- Stocks initiaux
INSERT INTO public.stocks (produit_id, boutique_id, quantite, seuil_alerte) 
SELECT 
    p.id,
    b.id,
    CASE 
        WHEN b.type = 'centrale' THEN 50
        ELSE 10 + (RANDOM() * 10)::INT
    END,
    5
FROM public.produits p
CROSS JOIN public.boutiques b;

-- Ventes de démonstration
INSERT INTO public.ventes (numero_facture, boutique_id, employe_id, client_nom, client_telephone, montant_total, montant_tva, mode_paiement) VALUES
('FAC-2024-001', (SELECT id FROM public.boutiques WHERE nom = 'Boutique Châtelet'), (SELECT id FROM public.employes WHERE email = '<EMAIL>'), 'Dupont Michel', '06 11 22 33 44', 1199.00, 199.83, 'carte'),
('FAC-2024-002', (SELECT id FROM public.boutiques WHERE nom = 'Boutique République'), (SELECT id FROM public.employes WHERE email = '<EMAIL>'), 'Leroy Anne', '06 55 66 77 88', 999.00, 166.50, 'especes');

-- Détails des ventes
INSERT INTO public.vente_details (vente_id, produit_id, quantite, prix_unitaire, sous_total) VALUES
((SELECT id FROM public.ventes WHERE numero_facture = 'FAC-2024-001'), (SELECT id FROM public.produits WHERE code_produit = 'IPH15P-128-BLK'), 1, 1199.00, 1199.00),
((SELECT id FROM public.ventes WHERE numero_facture = 'FAC-2024-002'), (SELECT id FROM public.produits WHERE code_produit = 'SGS24-256-WHT'), 1, 999.00, 999.00);