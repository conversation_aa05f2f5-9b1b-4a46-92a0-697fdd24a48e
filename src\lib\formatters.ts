/**
 * Utilitaires de formatage pour l'application Maison des Téléphones (Côte d'Ivoire)
 * Fuseau horaire: Africa/Abidjan (GMT+0, sans changement d'heure)
 */

// Configuration du fuseau horaire ivoirien
const COTE_DIVOIRE_TIMEZONE = 'Africa/Abidjan';
const COTE_DIVOIRE_LOCALE = 'fr-CI';

/**
 * Formate un montant en Franc CFA avec la localisation ivoirienne
 */
export const formatCurrency = (amount: number | undefined | null): string => {
  if (amount === undefined || amount === null || isNaN(amount)) {
    return '0 FCFA';
  }
  return `${amount.toLocaleString(COTE_DIVOIRE_LOCALE)} FCFA`;
};

/**
 * Formate un nombre avec la localisation ivoirienne
 */
export const formatNumber = (number: number | undefined | null): string => {
  if (number === undefined || number === null || isNaN(number)) {
    return '0';
  }
  return number.toLocaleString(COTE_DIVOIRE_LOCALE);
};

/**
 * Formate une date en français ivoirien avec le fuseau horaire local
 */
export const formatDate = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString(COTE_DIVOIRE_LOCALE, {
    timeZone: COTE_DIVOIRE_TIMEZONE,
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

/**
 * Formate une date et heure en français ivoirien avec le fuseau horaire local
 */
export const formatDateTime = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString(COTE_DIVOIRE_LOCALE, {
    timeZone: COTE_DIVOIRE_TIMEZONE,
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

/**
 * Formate une heure en français ivoirien avec le fuseau horaire local
 */
export const formatTime = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleTimeString(COTE_DIVOIRE_LOCALE, {
    timeZone: COTE_DIVOIRE_TIMEZONE,
    hour: '2-digit',
    minute: '2-digit'
  });
};

/**
 * Obtient la date/heure actuelle en Côte d'Ivoire
 */
export const getCurrentIvorianTime = (): Date => {
  return new Date();
};