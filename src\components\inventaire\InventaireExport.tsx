import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Download, FileText, Table } from 'lucide-react';
import { useInventaireDetails } from '@/hooks/useInventaire';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

interface InventaireExportProps {
  campagneId: string;
  campagneName: string;
}

export const InventaireExport = ({ campagneId, campagneName }: InventaireExportProps) => {
  const { data: details } = useInventaireDetails(campagneId);

  const exportToCSV = () => {
    if (!details) return;

    const headers = [
      'Produit',
      'Marque',
      'Code Produit',
      'Emplacement',
      'Quantité Théorique',
      'Quantité Physique',
      'Écart',
      'Commentaire',
      'Compté Par'
    ];

    const csvContent = [
      headers.join(','),
      ...details.map(detail => [
        `"${detail.produits?.nom || ''}"`,
        `"${detail.produits?.marque || ''}"`,
        `"${detail.produits?.code_produit || ''}"`,
        `"${detail.stocks?.emplacement || 'Non défini'}"`,
        detail.quantite_theorique,
        detail.quantite_physique || '',
        detail.ecart || '',
        `"${detail.commentaire || ''}"`,
        `"${detail.employes ? `${detail.employes.prenom} ${detail.employes.nom}` : ''}"`
      ].join(','))
    ].join('\n');

    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `inventaire_${campagneName}_${format(new Date(), 'yyyy-MM-dd')}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const exportToPrint = () => {
    if (!details) return;

    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Rapport d'Inventaire - ${campagneName}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { color: #333; border-bottom: 2px solid #333; padding-bottom: 10px; }
            .header { margin-bottom: 30px; }
            .stats { display: flex; gap: 20px; margin: 20px 0; }
            .stat-card { border: 1px solid #ddd; padding: 15px; border-radius: 5px; min-width: 150px; }
            .stat-value { font-size: 24px; font-weight: bold; color: #2563eb; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f5f5f5; font-weight: bold; }
            .ecart-positive { color: #16a34a; font-weight: bold; }
            .ecart-negative { color: #dc2626; font-weight: bold; }
            .ecart-zero { color: #666; }
            @media print { 
              .no-print { display: none; }
              body { margin: 0; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Rapport d'Inventaire</h1>
            <p><strong>Campagne:</strong> ${campagneName}</p>
            <p><strong>Date:</strong> ${format(new Date(), 'dd MMMM yyyy à HH:mm', { locale: fr })}</p>
          </div>

          <div class="stats">
            <div class="stat-card">
              <div class="stat-value">${details.length}</div>
              <div>Articles total</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">${details.filter(d => d.quantite_physique !== null).length}</div>
              <div>Articles comptés</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">${details.filter(d => d.ecart && d.ecart !== 0).length}</div>
              <div>Écarts détectés</div>
            </div>
          </div>

          <table>
            <thead>
              <tr>
                <th>Produit</th>
                <th>Code</th>
                <th>Emplacement</th>
                <th>Qté Théorique</th>
                <th>Qté Physique</th>
                <th>Écart</th>
                <th>Commentaire</th>
              </tr>
            </thead>
            <tbody>
              ${details.map(detail => {
                const ecart = detail.ecart || 0;
                const ecartClass = ecart > 0 ? 'ecart-positive' : ecart < 0 ? 'ecart-negative' : 'ecart-zero';
                return `
                  <tr>
                    <td>${detail.produits?.nom || ''} - ${detail.produits?.marque || ''}</td>
                    <td>${detail.produits?.code_produit || ''}</td>
                    <td>${detail.stocks?.emplacement || 'Non défini'}</td>
                    <td>${detail.quantite_theorique}</td>
                    <td>${detail.quantite_physique !== null ? detail.quantite_physique : 'Non compté'}</td>
                    <td class="${ecartClass}">${ecart !== 0 ? (ecart > 0 ? '+' : '') + ecart : '-'}</td>
                    <td>${detail.commentaire || ''}</td>
                  </tr>
                `;
              }).join('')}
            </tbody>
          </table>

          <script>
            window.onload = function() { 
              window.print(); 
              window.onafterprint = function() { 
                window.close(); 
              }
            }
          </script>
        </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();
  };

  const stats = {
    total: details?.length || 0,
    counted: details?.filter(d => d.quantite_physique !== null).length || 0,
    withDiscrepancy: details?.filter(d => d.ecart && d.ecart !== 0).length || 0,
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Export & Rapports
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-primary">{stats.total}</div>
            <div className="text-sm text-muted-foreground">Articles</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-600">{stats.counted}</div>
            <div className="text-sm text-muted-foreground">Comptés</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-orange-600">{stats.withDiscrepancy}</div>
            <div className="text-sm text-muted-foreground">Écarts</div>
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={exportToCSV}
            disabled={!details || details.length === 0}
            className="flex-1"
          >
            <Table className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Button
            variant="outline"
            onClick={exportToPrint}
            disabled={!details || details.length === 0}
            className="flex-1"
          >
            <Download className="h-4 w-4 mr-2" />
            Imprimer
          </Button>
        </div>

        {stats.withDiscrepancy > 0 && (
          <div className="mt-4">
            <Badge variant="secondary" className="w-full justify-center">
              {stats.withDiscrepancy} écart(s) à valider
            </Badge>
          </div>
        )}
      </CardContent>
    </Card>
  );
};