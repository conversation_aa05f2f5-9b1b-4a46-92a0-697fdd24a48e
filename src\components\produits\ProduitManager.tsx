import { useState, useEffect } from 'react';
import { Plus, Filter, Download, Upload, Eye, Edit, Trash2, Package } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { DataTable } from '@/components/ui/data-table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useProduits, useDeleteProduit, useProduitStats, type ProduitWithCategory } from '@/hooks/useProduits';
import { useCategories } from '@/hooks/useCategories';
import { ProduitForm } from './ProduitForm';
import { ProduitDetail } from './ProduitDetail';
import { CategoryManager } from './CategoryManager';
import { formatCurrency } from '@/lib/formatters';
import type { SearchFilters } from '@/lib/validations';

export const ProduitManager = () => {
  const [selectedProduit, setSelectedProduit] = useState<ProduitWithCategory | null>(null);
  const [editingProduit, setEditingProduit] = useState<ProduitWithCategory | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [showDetail, setShowDetail] = useState(false);
  const [showCategories, setShowCategories] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({
    search: '',
    limit: 50,
    offset: 0,
  });

  // Debug logs
  useEffect(() => {
    console.log('ProduitManager mounted');
  }, []);

  const { data: produits = [], isLoading, refetch, error } = useProduits(filters);
  const { data: categories = [], error: categoriesError } = useCategories();
  const { data: stats, error: statsError } = useProduitStats();
  const deleteProduit = useDeleteProduit();

  // Debug logs
  useEffect(() => {
    console.log('Produits data:', produits);
    console.log('Produits loading:', isLoading);
    console.log('Produits error:', error);
  }, [produits, isLoading, error]);

  useEffect(() => {
    console.log('Categories data:', categories);
    console.log('Categories error:', categoriesError);
  }, [categories, categoriesError]);

  // Show error state
  if (error || categoriesError || statsError) {
    console.error('Errors in ProduitManager:', { error, categoriesError, statsError });
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Gestion des Produits</h1>
            <p className="text-muted-foreground">
              Gérez votre catalogue de produits et catégories
            </p>
          </div>
        </div>
        
        <Card>
          <CardContent className="flex items-center justify-center h-64">
            <div className="text-center space-y-4">
              <h2 className="text-lg font-semibold text-red-600">Erreur de chargement des données</h2>
              <div className="text-sm text-muted-foreground space-y-2">
                {error && <p>Produits: {error.message}</p>}
                {categoriesError && <p>Catégories: {categoriesError.message}</p>}
                {statsError && <p>Statistiques: {statsError.message}</p>}
              </div>
              <div className="flex space-x-2 justify-center">
                <Button onClick={() => refetch()} variant="outline">
                  Réessayer
                </Button>
                <Button onClick={() => window.location.reload()}>
                  Recharger la page
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handleSearch = (search: string) => {
    setFilters(prev => ({ ...prev, search, offset: 0 }));
  };

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value, offset: 0 }));
  };

  const handleDelete = async (produit: ProduitWithCategory) => {
    await deleteProduit.mutateAsync(produit.id);
  };

  const handleEdit = (produit: ProduitWithCategory) => {
    setEditingProduit(produit);
    setShowForm(true);
  };

  const handleView = (produit: ProduitWithCategory) => {
    setSelectedProduit(produit);
    setShowDetail(true);
  };

  const handleFormClose = () => {
    setShowForm(false);
    setEditingProduit(null);
  };

  const columns = [
    {
      key: 'code_produit',
      label: 'Code',
      render: (produit: ProduitWithCategory) => (
        <span className="font-mono text-sm">{produit?.code_produit || 'N/A'}</span>
      ),
    },
    {
      key: 'nom',
      label: 'Produit',
      render: (produit: ProduitWithCategory) => (
        <div>
          <div className="font-medium">{produit?.nom || 'Nom non défini'}</div>
          <div className="text-sm text-muted-foreground">
            {produit?.marque || ''} {produit?.modele || ''}
          </div>
        </div>
      ),
    },
    {
      key: 'categorie',
      label: 'Catégorie',
      render: (produit: ProduitWithCategory) => (
        <Badge variant="outline">
          {produit?.categorie?.nom || 'Sans catégorie'}
        </Badge>
      ),
    },
    {
      key: 'prix_vente',
      label: 'Prix',
      render: (produit: ProduitWithCategory) => (
        <div className="text-right">
          <div className="font-medium">{formatCurrency(produit?.prix_vente || 0)}</div>
          <div className="text-sm text-muted-foreground">
            Achat: {formatCurrency(produit?.prix_achat || 0)}
          </div>
        </div>
      ),
    },
    {
      key: 'stock',
      label: 'Stock Total',
      render: (produit: ProduitWithCategory) => {
        // Safety check: ensure _count exists and has the required properties
        const totalQuantity = produit?._count?.total_quantity ?? 0;
        const stockCount = produit?._count?.stocks ?? 0;
        
        return (
          <div className="text-center">
            <div className="font-medium">{totalQuantity}</div>
            <div className="text-sm text-muted-foreground">
              {stockCount} boutique(s)
            </div>
          </div>
        );
      },
    },
    {
      key: 'etat',
      label: 'État',
      render: (produit: ProduitWithCategory) => (
        <Badge 
          variant={produit?.etat === 'neuf' ? 'default' : produit?.etat === 'occasion' ? 'secondary' : 'destructive'}
        >
          {produit?.etat || 'Non défini'}
        </Badge>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (produit: ProduitWithCategory) => (
        <div className="flex space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleView(produit)}
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEdit(produit)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="ghost" size="sm">
                <Trash2 className="h-4 w-4" />
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Confirmer la suppression</AlertDialogTitle>
                <AlertDialogDescription>
                  Êtes-vous sûr de vouloir supprimer le produit "{produit?.nom || 'Produit'}" ?
                  Cette action est irréversible.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Annuler</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => handleDelete(produit)}
                  className="bg-destructive text-destructive-foreground"
                >
                  Supprimer
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* En-tête avec statistiques */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Gestion des Produits</h1>
          <p className="text-muted-foreground">
            Gérez votre catalogue de produits et catégories
          </p>
        </div>
        <div className="flex space-x-4">
          {stats && (
            <div className="flex space-x-4 text-sm">
              <div className="text-center">
                <div className="font-bold text-lg">{typeof stats.total === 'number' ? stats.total : 0}</div>
                <div className="text-muted-foreground">Produits</div>
              </div>
              <div className="text-center">
                <div className="font-bold text-lg">{formatCurrency(stats.valeurStock || 0)}</div>
                <div className="text-muted-foreground">Valeur stock</div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Actions et filtres */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="flex items-center space-x-2">
              <Package className="h-5 w-5" />
              <span>Catalogue Produits</span>
            </CardTitle>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={() => setShowCategories(true)}>
                <Filter className="h-4 w-4 mr-2" />
                Catégories
              </Button>
              <Button variant="outline">
                <Upload className="h-4 w-4 mr-2" />
                Importer
              </Button>
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Exporter
              </Button>
              <Button onClick={() => setShowForm(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Nouveau Produit
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filtres */}
          <div className="flex space-x-4 mb-6">
            <div className="flex-1">
              <Input
                placeholder="Rechercher par nom, marque, modèle ou code..."
                value={filters.search || ''}
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>
            <Select
              value={filters.categorie_id || 'all'}
              onValueChange={(value) => 
                handleFilterChange('categorie_id', value === 'all' ? undefined : value)
              }
            >
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Catégorie" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Toutes les catégories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.nom}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select
              value={filters.etat || 'all'}
              onValueChange={(value) => 
                handleFilterChange('etat', value === 'all' ? undefined : value)
              }
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="État" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous</SelectItem>
                <SelectItem value="neuf">Neuf</SelectItem>
                <SelectItem value="occasion">Occasion</SelectItem>
                <SelectItem value="defectueux">Défectueux</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Table des produits */}
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">Chargement des produits...</p>
              </div>
            </div>
          ) : produits.length === 0 ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Aucun produit trouvé</h3>
                <p className="text-muted-foreground mb-4">
                  {filters.search ? 'Aucun produit ne correspond à votre recherche.' : 'Commencez par ajouter votre premier produit.'}
                </p>
                <Button onClick={() => setShowForm(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Ajouter un produit
                </Button>
              </div>
            </div>
          ) : (
            <DataTable
              data={produits}
              columns={columns}
              isLoading={isLoading}
              onRefresh={refetch}
              searchable={false}
              title="Produits"
              subtitle={`${produits.length} produit(s) trouvé(s)`}
            />
          )}
        </CardContent>
      </Card>

      {/* Dialogs */}
      <Dialog open={showForm} onOpenChange={setShowForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingProduit ? 'Modifier le produit' : 'Nouveau produit'}
            </DialogTitle>
          </DialogHeader>
          <ProduitForm
            produit={editingProduit}
            onClose={handleFormClose}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={showDetail} onOpenChange={setShowDetail}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Détails du produit</DialogTitle>
          </DialogHeader>
          {selectedProduit && (
            <ProduitDetail 
              produitId={selectedProduit.id}
              onEdit={() => {
                setEditingProduit(selectedProduit);
                setShowDetail(false);
                setShowForm(true);
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      <Dialog open={showCategories} onOpenChange={setShowCategories}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Gestion des catégories</DialogTitle>
          </DialogHeader>
          <CategoryManager />
        </DialogContent>
      </Dialog>
    </div>
  );
};