import { useState } from "react";
import { MapPin, Phone, Mail, Users, Package, TrendingUp, Calendar, Edit, X } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { DataTable } from "@/components/ui/data-table";
import { formatCurrency, formatDate } from "@/lib/formatters";
import { useEmployes } from "@/hooks/useEmployes";
import { useStocks } from "@/hooks/useStocks";
import { useVentes } from "@/hooks/useVentes";
import { useTransferts } from "@/hooks/useTransferts";
import type { Boutique } from "@/hooks/useBoutiques";

interface BoutiqueDetailProps {
  boutique: Boutique;
  onClose: () => void;
}

export const BoutiqueDetail = ({ boutique, onClose }: BoutiqueDetailProps) => {
  const { data: employes, isLoading: employesLoading } = useEmployes(boutique.id);
  const { data: stocks, isLoading: stocksLoading } = useStocks(boutique.id);
  const { data: ventes, isLoading: ventesLoading } = useVentes(boutique.id);
  const { data: transferts, isLoading: transfertsLoading } = useTransferts(boutique.id);

  // Statistiques rapides
  const employesActifs = employes?.filter(emp => emp.statut === 'actif').length || 0;
  const stockTotal = stocks?.reduce((total, stock) => total + stock.quantite, 0) || 0;
  const stockFaible = stocks?.filter(stock => stock.quantite <= stock.seuil_alerte).length || 0;
  
  // Ventes du jour
  const today = new Date().toISOString().split('T')[0];
  const ventesToday = ventes?.filter(vente => 
    vente.date_vente.startsWith(today) && vente.statut === 'validee'
  ) || [];
  const caToday = ventesToday.reduce((total, vente) => total + Number(vente.montant_total), 0);

  // Colonnes pour les tables
  const employesColumns = [
    { key: 'nom', label: 'Nom' },
    { key: 'prenom', label: 'Prénom' },
    { key: 'poste', label: 'Poste' },
    { key: 'email', label: 'Email' },
    { 
      key: 'statut', 
      label: 'Statut',
      render: (value: string) => (
        <Badge variant={value === 'actif' ? 'default' : 'secondary'}>
          {value}
        </Badge>
      )
    }
  ];

  const stocksColumns = [
    { key: 'produits.nom', label: 'Produit' },
    { key: 'quantite', label: 'Quantité' },
    { key: 'seuil_alerte', label: 'Seuil alerte' },
    { 
      key: 'status', 
      label: 'Statut',
      render: (_: any, row: any) => {
        const isLow = row.quantite <= row.seuil_alerte;
        return (
          <Badge variant={isLow ? 'destructive' : 'default'}>
            {isLow ? 'Stock bas' : 'OK'}
          </Badge>
        );
      }
    }
  ];

  const ventesColumns = [
    { key: 'numero_facture', label: 'N° Facture' },
    { key: 'client_nom', label: 'Client' },
    { 
      key: 'montant_total', 
      label: 'Montant',
      render: (value: number) => formatCurrency(value)
    },
    { 
      key: 'date_vente', 
      label: 'Date',
      render: (value: string) => formatDate(value)
    },
    { key: 'mode_paiement', label: 'Paiement' }
  ];

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-start justify-between">
        <div className="space-y-2">
          <div className="flex items-center gap-3">
            <h2 className="text-2xl font-bold">{boutique.nom}</h2>
            {boutique.type === "centrale" && <Badge variant="outline">CENTRALE</Badge>}
            <Badge variant={boutique.statut === 'active' ? 'default' : 'secondary'}>
              {boutique.statut}
            </Badge>
          </div>
          <div className="space-y-1 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              {boutique.adresse}
            </div>
            {boutique.telephone && (
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                {boutique.telephone}
              </div>
            )}
            {boutique.email && (
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                {boutique.email}
              </div>
            )}
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Créée le {formatDate(boutique.created_at)}
            </div>
          </div>
        </div>
        <Button variant="ghost" size="sm" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-primary" />
              <div>
                <p className="text-2xl font-bold">{employesActifs}</p>
                <p className="text-sm text-muted-foreground">Employés actifs</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Package className="h-5 w-5 text-primary" />
              <div>
                <p className="text-2xl font-bold">{stockTotal}</p>
                <p className="text-sm text-muted-foreground">Articles en stock</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-primary" />
              <div>
                <p className="text-2xl font-bold">{formatCurrency(caToday)}</p>
                <p className="text-sm text-muted-foreground">CA aujourd'hui</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Package className="h-5 w-5 text-destructive" />
              <div>
                <p className="text-2xl font-bold">{stockFaible}</p>
                <p className="text-sm text-muted-foreground">Stocks faibles</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Contenu détaillé par onglets */}
      <Tabs defaultValue="employes" className="space-y-4">
        <TabsList>
          <TabsTrigger value="employes">Employés ({employes?.length || 0})</TabsTrigger>
          <TabsTrigger value="stocks">Stock ({stocks?.length || 0})</TabsTrigger>
          <TabsTrigger value="ventes">Ventes ({ventes?.length || 0})</TabsTrigger>
          <TabsTrigger value="transferts">Transferts ({transferts?.length || 0})</TabsTrigger>
        </TabsList>

        <TabsContent value="employes">
          <Card>
            <CardHeader>
              <CardTitle>Employés de la boutique</CardTitle>
            </CardHeader>
            <CardContent>
              {employesLoading ? (
                <div className="flex justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : (
                <DataTable
                  data={employes || []}
                  columns={employesColumns}
                  searchable={true}
                  searchPlaceholder="Rechercher un employé..."
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stocks">
          <Card>
            <CardHeader>
              <CardTitle>État des stocks</CardTitle>
            </CardHeader>
            <CardContent>
              {stocksLoading ? (
                <div className="flex justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : (
                <DataTable
                  data={stocks || []}
                  columns={stocksColumns}
                  searchable={true}
                  searchPlaceholder="Rechercher un produit..."
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ventes">
          <Card>
            <CardHeader>
              <CardTitle>Historique des ventes</CardTitle>
            </CardHeader>
            <CardContent>
              {ventesLoading ? (
                <div className="flex justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : (
                <DataTable
                  data={ventes || []}
                  columns={ventesColumns}
                  searchable={true}
                  searchPlaceholder="Rechercher une vente..."
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transferts">
          <Card>
            <CardHeader>
              <CardTitle>Transferts</CardTitle>
            </CardHeader>
            <CardContent>
              {transfertsLoading ? (
                <div className="flex justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : (
                <DataTable
                  data={transferts || []}
                  columns={[
                    { key: 'id', label: 'ID Transfert' },
                    { key: 'statut', label: 'Statut' },
                    { 
                      key: 'date_expedition', 
                      label: 'Date expédition',
                      render: (value: string) => value ? formatDate(value) : '-'
                    },
                    { 
                      key: 'date_reception', 
                      label: 'Date réception',
                      render: (value: string) => value ? formatDate(value) : '-'
                    }
                  ]}
                  searchable={true}
                  searchPlaceholder="Rechercher un transfert..."
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};