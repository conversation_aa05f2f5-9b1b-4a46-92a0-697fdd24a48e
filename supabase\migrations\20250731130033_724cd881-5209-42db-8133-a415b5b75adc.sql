-- Créer la table clients pour la gestion client
CREATE TABLE public.clients (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  nom text NOT NULL,
  prenom text,
  email text,
  telephone text,
  adresse text,
  ville text,
  code_postal text,
  date_naissance date,
  notes text,
  statut text NOT NULL DEFAULT 'actif' CHECK (statut IN ('actif', 'inactif')),
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

-- Activer RLS sur la table clients
ALTER TABLE public.clients ENABLE ROW LEVEL SECURITY;

-- Politiques RLS pour la table clients
CREATE POLICY "Permettre lecture des clients" ON public.clients FOR SELECT USING (true);
CREATE POLICY "Permettre création des clients" ON public.clients FOR INSERT WITH CHECK (true);
CREATE POLICY "Permettre modification des clients" ON public.clients FOR UPDATE USING (true);
CREATE POLICY "Permettre suppression des clients" ON public.clients FOR DELETE USING (true);

-- Ajouter une colonne client_id à la table ventes pour lier aux clients
ALTER TABLE public.ventes ADD COLUMN client_id uuid REFERENCES public.clients(id);

-- Créer des index pour optimiser les performances
CREATE INDEX idx_ventes_date_vente ON public.ventes(date_vente);
CREATE INDEX idx_ventes_boutique_id ON public.ventes(boutique_id);
CREATE INDEX idx_ventes_employe_id ON public.ventes(employe_id);
CREATE INDEX idx_ventes_client_id ON public.ventes(client_id);
CREATE INDEX idx_vente_details_vente_id ON public.vente_details(vente_id);
CREATE INDEX idx_vente_details_produit_id ON public.vente_details(produit_id);
CREATE INDEX idx_clients_email ON public.clients(email);
CREATE INDEX idx_clients_telephone ON public.clients(telephone);

-- Fonction pour créer automatiquement un numéro de facture
CREATE OR REPLACE FUNCTION generate_numero_facture()
RETURNS text AS $$
DECLARE
  current_year text;
  next_number integer;
BEGIN
  current_year := EXTRACT(year FROM now())::text;
  
  SELECT COALESCE(MAX(CAST(SUBSTRING(numero_facture FROM 'FAC-' || current_year || '-(\d+)') AS integer)), 0) + 1
  INTO next_number
  FROM ventes 
  WHERE numero_facture LIKE 'FAC-' || current_year || '-%';
  
  RETURN 'FAC-' || current_year || '-' || LPAD(next_number::text, 6, '0');
END;
$$ LANGUAGE plpgsql;

-- Trigger pour générer automatiquement le numéro de facture
CREATE OR REPLACE FUNCTION set_numero_facture()
RETURNS trigger AS $$
BEGIN
  IF NEW.numero_facture IS NULL THEN
    NEW.numero_facture := generate_numero_facture();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_set_numero_facture
  BEFORE INSERT ON ventes
  FOR EACH ROW
  EXECUTE FUNCTION set_numero_facture();

-- Fonction pour mettre à jour les stocks lors d'une vente
CREATE OR REPLACE FUNCTION update_stock_on_vente()
RETURNS trigger AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Diminuer le stock lors d'un ajout de détail de vente
    UPDATE stocks 
    SET quantite = quantite - NEW.quantite,
        updated_at = now()
    WHERE produit_id = NEW.produit_id 
    AND boutique_id = (SELECT boutique_id FROM ventes WHERE id = NEW.vente_id);
    
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    -- Ajuster le stock si la quantité change
    UPDATE stocks 
    SET quantite = quantite + OLD.quantite - NEW.quantite,
        updated_at = now()
    WHERE produit_id = NEW.produit_id 
    AND boutique_id = (SELECT boutique_id FROM ventes WHERE id = NEW.vente_id);
    
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    -- Remettre le stock lors de la suppression
    UPDATE stocks 
    SET quantite = quantite + OLD.quantite,
        updated_at = now()
    WHERE produit_id = OLD.produit_id 
    AND boutique_id = (SELECT boutique_id FROM ventes WHERE id = OLD.vente_id);
    
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_stock_on_vente
  AFTER INSERT OR UPDATE OR DELETE ON vente_details
  FOR EACH ROW
  EXECUTE FUNCTION update_stock_on_vente();

-- Fonction pour calculer automatiquement le montant total d'une vente
CREATE OR REPLACE FUNCTION calculate_vente_total()
RETURNS trigger AS $$
BEGIN
  UPDATE ventes 
  SET montant_total = (
    SELECT COALESCE(SUM(sous_total), 0)
    FROM vente_details 
    WHERE vente_id = COALESCE(NEW.vente_id, OLD.vente_id)
  ),
  updated_at = now()
  WHERE id = COALESCE(NEW.vente_id, OLD.vente_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_calculate_vente_total
  AFTER INSERT OR UPDATE OR DELETE ON vente_details
  FOR EACH ROW
  EXECUTE FUNCTION calculate_vente_total();