import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useIsMobile } from '@/hooks/use-mobile';
import { 
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line
} from 'recharts';
import { 
  Trophy, 
  TrendingUp, 
  Target, 
  Users,
  Download,
  Medal,
  Award,
  Star
} from 'lucide-react';
import { usePerformanceReports } from '@/hooks/usePerformanceReports';
import { formatCurrency, formatNumber } from '@/lib/formatters';

interface PerformanceReportsProps {
  filters: {
    dateDebut: string;
    dateFin: string;
    boutiqueId: string;
    employeId: string;
    categorieId: string;
  };
}

export const PerformanceReports = ({ filters }: PerformanceReportsProps) => {
  const { 
    data: performanceData, 
    isLoading,
    statsBoutiques,
    statsEmployes,
    evolutionPerformance,
    topVendeurs,
    objectifsVsRealise
  } = usePerformanceReports(filters);
  const isMobile = useIsMobile();

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-8 w-full mb-2" />
                <Skeleton className="h-4 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Skeleton className="h-80" />
          <Skeleton className="h-80" />
        </div>
      </div>
    );
  }

  const getPerformanceBadge = (taux: number) => {
    if (taux >= 100) return <Badge className="bg-green-500">Excellent</Badge>;
    if (taux >= 80) return <Badge className="bg-blue-500">Bon</Badge>;
    if (taux >= 60) return <Badge className="bg-yellow-500">Moyen</Badge>;
    return <Badge variant="destructive">À améliorer</Badge>;
  };

  return (
    <div className="space-y-6">
      {/* KPIs globaux */}
      <div className={`grid gap-4 ${isMobile ? 'grid-cols-2' : 'grid-cols-1 md:grid-cols-4'}`}>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">CA Total Réseau</p>
                <p className="text-2xl font-bold">{formatCurrency(statsBoutiques?.caTotalReseau || 0)}</p>
                <p className="text-xs text-green-600">
                  {statsBoutiques?.nbBoutiquesActives || 0} boutiques actives
                </p>
              </div>
              <Trophy className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Performance Moyenne</p>
                <p className="text-2xl font-bold">{statsBoutiques?.performanceMoyenne || 0}%</p>
                <p className="text-xs text-blue-600">
                  Objectifs vs réalisé
                </p>
              </div>
              <Target className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Vendeurs Actifs</p>
                <p className="text-2xl font-bold">{statsEmployes?.vendeursActifs || 0}</p>
                <p className="text-xs text-purple-600">
                  {formatCurrency(statsEmployes?.caMoyenParVendeur || 0)}/vendeur
                </p>
              </div>
              <Users className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Croissance</p>
                <p className="text-2xl font-bold text-green-600">+{statsBoutiques?.croissance || 0}%</p>
                <p className="text-xs text-green-600">
                  vs période précédente
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Graphiques performance */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance des boutiques */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Performance par Boutique</CardTitle>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={statsBoutiques?.performanceParBoutique} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                <XAxis 
                  type="number"
                  stroke="hsl(var(--muted-foreground))"
                  fontSize={12}
                  tickFormatter={(value) => `${value}%`}
                />
                <YAxis 
                  type="category"
                  dataKey="boutique"
                  stroke="hsl(var(--muted-foreground))"
                  fontSize={12}
                  width={120}
                />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'hsl(var(--background))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px'
                  }}
                  formatter={(value) => [`${value}%`, 'Performance']}
                />
                <Bar 
                  dataKey="performance" 
                  fill="hsl(var(--primary))"
                  radius={[0, 4, 4, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Évolution des objectifs */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Objectifs vs Réalisé</CardTitle>
            <Button variant="outline" size="sm">
              <Target className="h-4 w-4 mr-2" />
              Définir
            </Button>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={objectifsVsRealise}>
                <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                <XAxis 
                  dataKey="mois" 
                  stroke="hsl(var(--muted-foreground))"
                  fontSize={12}
                />
                <YAxis 
                  stroke="hsl(var(--muted-foreground))"
                  fontSize={12}
                  tickFormatter={(value) => formatCurrency(value)}
                />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'hsl(var(--background))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px'
                  }}
                  formatter={(value) => [formatCurrency(value as number)]}
                />
                <Line 
                  type="monotone" 
                  dataKey="objectif" 
                  stroke="hsl(var(--muted-foreground))" 
                  strokeDasharray="5 5"
                  name="Objectif"
                />
                <Line 
                  type="monotone" 
                  dataKey="realise" 
                  stroke="hsl(var(--primary))" 
                  strokeWidth={2}
                  name="Réalisé"
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Classements */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top vendeurs */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Medal className="h-5 w-5 text-yellow-500" />
              Top 10 Vendeurs
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topVendeurs?.map((vendeur, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10">
                      {index < 3 ? (
                        index === 0 ? <Trophy className="h-4 w-4 text-yellow-500" /> :
                        index === 1 ? <Award className="h-4 w-4 text-gray-400" /> :
                        <Star className="h-4 w-4 text-orange-500" />
                      ) : (
                        <span className="text-sm font-bold text-muted-foreground">#{index + 1}</span>
                      )}
                    </div>
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="text-xs">
                        {vendeur.nom?.charAt(0)}{vendeur.prenom?.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium text-sm">{vendeur.nom} {vendeur.prenom}</p>
                      <p className="text-xs text-muted-foreground">{vendeur.boutique}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-sm">{formatCurrency(vendeur.ca)}</p>
                    <p className="text-xs text-muted-foreground">{vendeur.nbVentes} ventes</p>
                  </div>
                </div>
              )) || (
                <p className="text-muted-foreground text-center py-4">
                  Aucune donnée disponible
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Performance individuelle */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-500" />
              Indicateurs de Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[]?.map((indicateur, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium text-sm">{indicateur.nom}</p>
                    <p className="text-xs text-muted-foreground">{indicateur.description}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-sm">{indicateur.valeur}</p>
                    {getPerformanceBadge(indicateur.taux)}
                  </div>
                </div>
              )) || (
                <>
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium text-sm">Taux de conversion moyen</p>
                      <p className="text-xs text-muted-foreground">Prospects → Ventes</p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-sm">73%</p>
                      {getPerformanceBadge(73)}
                    </div>
                  </div>
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium text-sm">Panier moyen</p>
                      <p className="text-xs text-muted-foreground">Valeur moyenne par vente</p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-sm">{formatCurrency(125000)}</p>
                      {getPerformanceBadge(85)}
                    </div>
                  </div>
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium text-sm">Productivité</p>
                      <p className="text-xs text-muted-foreground">Ventes par heure</p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-sm">3.2</p>
                      {getPerformanceBadge(91)}
                    </div>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};