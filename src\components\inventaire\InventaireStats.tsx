import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  ClipboardList, 
  TrendingUp, 
  TrendingDown, 
  Al<PERSON>Triangle,
  CheckCircle 
} from 'lucide-react';

interface InventaireStatsProps {
  details: Array<{
    id: string;
    quantite_theorique: number;
    quantite_physique?: number;
    ecart?: number;
    valide: boolean;
  }>;
}

export const InventaireStats = ({ details }: InventaireStatsProps) => {
  const stats = {
    total: details.length,
    counted: details.filter(d => d.quantite_physique !== null).length,
    withDiscrepancy: details.filter(d => d.ecart && d.ecart !== 0).length,
    validated: details.filter(d => d.valide).length,
    positiveAdjustments: details.filter(d => d.ecart && d.ecart > 0).length,
    negativeAdjustments: details.filter(d => d.ecart && d.ecart < 0).length,
  };

  const progress = stats.total > 0 ? (stats.counted / stats.total) * 100 : 0;
  const hasDiscrepancies = stats.withDiscrepancy > 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Progression
          </CardTitle>
          <ClipboardList className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {stats.counted}/{stats.total}
          </div>
          <div className="flex items-center justify-between mt-2">
            <div className="text-xs text-muted-foreground">
              {progress.toFixed(1)}% complété
            </div>
            <div className="w-full bg-secondary ml-2 rounded-full h-2">
              <div 
                className="bg-primary h-2 rounded-full transition-all duration-500"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Écarts détectés
          </CardTitle>
          <AlertTriangle className={`h-4 w-4 ${hasDiscrepancies ? 'text-warning' : 'text-muted-foreground'}`} />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {stats.withDiscrepancy}
          </div>
          <div className="flex items-center gap-2 mt-2">
            {hasDiscrepancies ? (
              <Badge variant="secondary" className="text-xs">
                Attention requise
              </Badge>
            ) : (
              <Badge variant="default" className="text-xs">
                <CheckCircle className="h-3 w-3 mr-1" />
                Conforme
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Surplus
          </CardTitle>
          <TrendingUp className="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            +{stats.positiveAdjustments}
          </div>
          <p className="text-xs text-muted-foreground">
            Produits en surplus
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Manquants
          </CardTitle>
          <TrendingDown className="h-4 w-4 text-red-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">
            -{stats.negativeAdjustments}
          </div>
          <p className="text-xs text-muted-foreground">
            Produits manquants
          </p>
        </CardContent>
      </Card>
    </div>
  );
};