import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { useCreateEmploye, useUpdateEmploye, type Employe } from '@/hooks/useEmployes';
import { useBoutiques } from '@/hooks/useBoutiques';
import { employeSchema, type EmployeFormData } from '@/lib/validations';
import { format } from 'date-fns';

interface EmployeFormProps {
  employe?: Employe | null;
  onSuccess: () => void;
  onCancel: () => void;
}

export const EmployeForm: React.FC<EmployeFormProps> = ({
  employe,
  onSuccess,
  onCancel,
}) => {
  const { data: boutiques = [] } = useBoutiques();
  const createEmploye = useCreateEmploye();
  const updateEmploye = useUpdateEmploye();

  const form = useForm<EmployeFormData>({
    resolver: zodResolver(employeSchema),
    defaultValues: {
      nom: employe?.nom || '',
      prenom: employe?.prenom || '',
      email: employe?.email || '',
      telephone: employe?.telephone || '',
      poste: employe?.poste || '',
      boutique_id: employe?.boutique_id || '',
      salaire: employe?.salaire || undefined,
      date_embauche: employe ? new Date(employe.date_embauche) : new Date(),
      statut: employe?.statut || 'actif',
    },
  });

  const onSubmit = async (data: EmployeFormData) => {
    try {
      console.log('📝 [EmployeForm] Form data:', data);
      
      const employeData = {
        nom: data.nom,
        prenom: data.prenom,
        email: data.email,
        telephone: data.telephone,
        poste: data.poste,
        boutique_id: data.boutique_id || null,
        salaire: data.salaire,
        statut: data.statut,
        date_embauche: format(data.date_embauche, 'yyyy-MM-dd'),
      };

      console.log('📤 [EmployeForm] Sending data:', employeData);

      if (employe) {
        await updateEmploye.mutateAsync({
          id: employe.id,
          ...employeData,
        });
      } else {
        await createEmploye.mutateAsync(employeData);
      }
      onSuccess();
    } catch (error) {
      console.error('❌ [EmployeForm] Form submission error:', error);
    }
  };

  const isLoading = createEmploye.isPending || updateEmploye.isPending;

  return (
    <Card>
      <CardContent className="pt-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Informations personnelles */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Informations personnelles</h3>
                
                <FormField
                  control={form.control}
                  name="prenom"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Prénom</FormLabel>
                      <FormControl>
                        <Input placeholder="Prénom" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="nom"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nom</FormLabel>
                      <FormControl>
                        <Input placeholder="Nom" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="telephone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Téléphone</FormLabel>
                      <FormControl>
                        <Input placeholder="0123456789" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Informations professionnelles */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Informations professionnelles</h3>

                <FormField
                  control={form.control}
                  name="poste"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Poste</FormLabel>
                      <FormControl>
                        <Input placeholder="Ex: Vendeur, Manager, Caissier" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="boutique_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Boutique</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner une boutique" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {boutiques.map((boutique) => (
                            <SelectItem key={boutique.id} value={boutique.id}>
                              {boutique.nom}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="salaire"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Salaire (optionnel)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Ex: 25000"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="date_embauche"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date d'embauche</FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...field}
                          value={field.value ? format(field.value, 'yyyy-MM-dd') : ''}
                          onChange={(e) => field.onChange(new Date(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="statut"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Statut</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner un statut" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="actif">Actif</SelectItem>
                          <SelectItem value="inactif">Inactif</SelectItem>
                          <SelectItem value="suspendu">Suspendu</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-2 pt-4 border-t">
              <Button type="button" variant="outline" onClick={onCancel}>
                Annuler
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading 
                  ? (employe ? 'Modification...' : 'Création...') 
                  : (employe ? 'Modifier' : 'Créer')
                }
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};