import { 
  Home, 
  Store, 
  Package, 
  ArrowLeftRight, 
  ClipboardList, 
  ShoppingCart, 
  Users, 
  BarChart3,
  Settings,
  Smartphone,
  AlertTriangle,
  Shield
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useAuth } from '@/hooks/useAuth';
import { useState } from "react";

interface SidebarProps {
  isOpen: boolean;
  currentPage: string;
  onPageChange: (page: string) => void;
}

interface MenuItem {
  id: string;
  label: string;
  icon: any;
  description: string;
  adminOnly?: boolean;
}

const menuItems: MenuItem[] = [
  {
    id: "dashboard",
    label: "Tableau de Bord",
    icon: Home,
    description: "Vue d'ensemble"
  },
  {
    id: "boutiques",
    label: "Boutiques",
    icon: Store,
    description: "Gestion des boutiques"
  },
  {
    id: "stocks",
    label: "Gestion Stock",
    icon: Package,
    description: "Inventaire & stocks"
  },
  {
    id: "transferts",
    label: "Transferts",
    icon: ArrowLeftRight,
    description: "Entre boutiques"
  },
  {
    id: "ventes",
    label: "Ventes",
    icon: ShoppingCart,
    description: "Caisse & facturation"
  },
  {
    id: "produits",
    label: "Produits",
    icon: Smartphone,
    description: "Téléphones & accessoires"
  },
  {
    id: "employes",
    label: "Employés",
    icon: Users,
    description: "Vendeurs & caissiers"
  },
  {
    id: "inventaire",
    label: "Inventaire",
    icon: ClipboardList,
    description: "Suivi détaillé"
  },
  {
    id: "rapports",
    label: "Rapports",
    icon: BarChart3,
    description: "Analytiques"
  },
  {
    id: "alertes",
    label: "Alertes",
    icon: AlertTriangle,
    description: "Stock bas & notifications"
  },
  {
    id: "admin",
    label: "Administration",
    icon: Shield,
    description: "Gestion utilisateurs",
    adminOnly: true
  },
  {
    id: "parametres",
    label: "Paramètres",
    icon: Settings,
    description: "Configuration"
  }
];

export const Sidebar = ({ isOpen, currentPage, onPageChange }: SidebarProps) => {
  const { profile } = useAuth();

  // Filtrer les éléments selon les permissions
  const filteredMenuItems = menuItems.filter(item => {
    if (item.adminOnly) {
      return profile?.role === 'admin_system';
    }
    return true;
  });

  return (
    <aside className={cn(
      "bg-card border-r border-border transition-all duration-300 flex flex-col",
      isOpen ? "w-64" : "w-16"
    )}>
      <div className="p-4 border-b border-border">
        <div className="flex items-center gap-3">
          <div className="h-8 w-8 bg-primary rounded-lg flex items-center justify-center flex-shrink-0">
            <Store className="h-5 w-5 text-primary-foreground" />
          </div>
          {isOpen && (
            <div>
              <h2 className="font-semibold text-sm">Boutique Centrale</h2>
              <p className="text-xs text-muted-foreground">Administration</p>
            </div>
          )}
        </div>
      </div>
      
      <nav className="flex-1 p-2 space-y-1">
        {filteredMenuItems.map((item) => {
          const Icon = item.icon;
          const isActive = currentPage === item.id;
          
          return (
            <Button
              key={item.id}
              variant={isActive ? "default" : "ghost"}
              className={cn(
                "w-full justify-start h-auto p-3 transition-all duration-200",
                !isOpen && "justify-center p-2",
                isActive && "bg-primary text-primary-foreground shadow-sm"
              )}
              onClick={() => onPageChange(item.id)}
            >
              <Icon className={cn("h-5 w-5", isOpen && "mr-3")} />
              {isOpen && (
                <div className="flex flex-col items-start">
                  <span className="text-sm font-medium">{item.label}</span>
                  <span className="text-xs opacity-80">{item.description}</span>
                </div>
              )}
            </Button>
          );
        })}
      </nav>
      
      <div className="p-4 border-t border-border">
        <div className={cn("text-center", !isOpen && "hidden")}>
          <p className="text-xs text-muted-foreground">
            v1.0.0 - Maison des Téléphones
          </p>
        </div>
      </div>
    </aside>
  );
};