import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface VenteDetail {
  id: string;
  vente_id: string;
  produit_id: string;
  quantite: number;
  prix_unitaire: number;
  remise?: number;
  sous_total: number;
  created_at: string;
  produits?: {
    nom: string;
    marque: string;
    modele: string;
    code_produit: string;
  };
}

export interface VenteWithDetails {
  id: string;
  numero_facture: string;
  boutique_id: string;
  employe_id: string;
  client_id?: string;
  client_nom?: string;
  client_telephone?: string;
  client_email?: string;
  montant_total: number;
  montant_tva: number;
  mode_paiement: string;
  statut: string;
  date_vente: string;
  created_at: string;
  updated_at: string;
  boutiques?: { nom: string };
  employes?: { nom: string; prenom: string };
  clients?: { nom: string; prenom?: string; email?: string; telephone?: string };
  vente_details: VenteDetail[];
}

export const useVenteDetails = (venteId?: string) => {
  return useQuery({
    queryKey: ['vente-details', venteId],
    queryFn: async () => {
      if (!venteId) return null;
      
      const { data, error } = await supabase
        .from('ventes')
        .select(`
          *,
          boutiques:boutique_id(nom),
          employes:employe_id(nom, prenom),
          clients:client_id(nom, prenom, email, telephone),
          vente_details(
            *,
            produits:produit_id(nom, marque, modele, code_produit)
          )
        `)
        .eq('id', venteId)
        .single();
      
      if (error) throw error;
      return data as VenteWithDetails;
    },
    enabled: !!venteId,
    staleTime: 1 * 60 * 1000,
    gcTime: 3 * 60 * 1000,
  });
};

export const useCreateVenteDetail = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (detail: Omit<VenteDetail, 'id' | 'created_at' | 'sous_total'>) => {
      const sousTotal = detail.quantite * detail.prix_unitaire - (detail.remise || 0);
      
      const { data, error } = await supabase
        .from('vente_details')
        .insert({ ...detail, sous_total: sousTotal })
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['vente-details', variables.vente_id] });
      queryClient.invalidateQueries({ queryKey: ['ventes'] });
      queryClient.invalidateQueries({ queryKey: ['stocks'] });
      toast({
        title: "Produit ajouté",
        description: "Le produit a été ajouté à la vente."
      });
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible d'ajouter le produit à la vente."
      });
    }
  });
};

export const useUpdateVenteDetail = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, vente_id, ...detail }: { id: string; vente_id: string } & Partial<VenteDetail>) => {
      let updateData = { ...detail };
      
      // Recalculer le sous-total si nécessaire
      if (detail.quantite !== undefined || detail.prix_unitaire !== undefined || detail.remise !== undefined) {
        const current = await supabase
          .from('vente_details')
          .select('*')
          .eq('id', id)
          .single();
        
        if (current.data) {
          const quantite = detail.quantite ?? current.data.quantite;
          const prixUnitaire = detail.prix_unitaire ?? current.data.prix_unitaire;
          const remise = detail.remise ?? current.data.remise ?? 0;
          updateData.sous_total = quantite * prixUnitaire - remise;
        }
      }
      
      const { data, error } = await supabase
        .from('vente_details')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['vente-details', variables.vente_id] });
      queryClient.invalidateQueries({ queryKey: ['ventes'] });
      queryClient.invalidateQueries({ queryKey: ['stocks'] });
      toast({
        title: "Produit mis à jour",
        description: "Le produit a été mis à jour."
      });
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de mettre à jour le produit."
      });
    }
  });
};

export const useDeleteVenteDetail = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, vente_id }: { id: string; vente_id: string }) => {
      const { error } = await supabase
        .from('vente_details')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['vente-details', variables.vente_id] });
      queryClient.invalidateQueries({ queryKey: ['ventes'] });
      queryClient.invalidateQueries({ queryKey: ['stocks'] });
      toast({
        title: "Produit supprimé",
        description: "Le produit a été supprimé de la vente."
      });
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de supprimer le produit."
      });
    }
  });
};