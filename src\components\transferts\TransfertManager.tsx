import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useTransferts } from '@/hooks/useTransferts';
import { useBoutiques } from '@/hooks/useBoutiques';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { TransfertForm } from './TransfertForm';
import { TransfertDetail } from './TransfertDetail';
import { ArrowLeftRight, Truck, Package, Calendar, User, Plus } from 'lucide-react';
import { format } from 'date-fns';
import { formatDate, formatDateTime } from '@/lib/formatters';
import { fr } from 'date-fns/locale';

export const TransfertManager = () => {
  const [selectedBoutique, setSelectedBoutique] = useState<string>('');
  const [showForm, setShowForm] = useState(false);

  const { data: boutiques } = useBoutiques();
  const { data: transferts, isLoading } = useTransferts(selectedBoutique === 'all' ? undefined : selectedBoutique);

  const getStatutVariant = (statut: string) => {
    switch (statut) {
      case 'en_attente': return 'secondary';
      case 'expedie': return 'outline';
      case 'en_transit': return 'outline';
      case 'recu': return 'default';
      case 'annule': return 'destructive';
      default: return 'default';
    }
  };

  const getStatutIcon = (statut: string) => {
    switch (statut) {
      case 'en_attente': return <Package className="h-4 w-4" />;
      case 'expedie':
      case 'en_transit': return <Truck className="h-4 w-4" />;
      case 'recu': return <Package className="h-4 w-4" />;
      default: return <ArrowLeftRight className="h-4 w-4" />;
    }
  };

  const getStatutLabel = (statut: string) => {
    const labels = {
      'en_attente': 'En attente',
      'expedie': 'Expédié',
      'en_transit': 'En transit',
      'recu': 'Reçu',
      'annule': 'Annulé'
    };
    return labels[statut as keyof typeof labels] || statut;
  };

  if (isLoading) return <div>Chargement...</div>;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Gestion des Transferts</h1>
        <div className="flex space-x-4">
          <Select value={selectedBoutique} onValueChange={setSelectedBoutique}>
            <SelectTrigger className="w-64">
              <SelectValue placeholder="Toutes les boutiques" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Toutes les boutiques</SelectItem>
              {boutiques?.map((boutique) => (
                <SelectItem key={boutique.id} value={boutique.id}>
                  {boutique.nom}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Dialog open={showForm} onOpenChange={setShowForm}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Nouveau Transfert
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Créer un nouveau transfert</DialogTitle>
              </DialogHeader>
              <TransfertForm onSuccess={() => setShowForm(false)} />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {['en_attente', 'expedie', 'en_transit', 'recu'].map((statut) => {
          const count = transferts?.filter(t => t.statut === statut).length || 0;
          return (
            <Card key={statut}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {getStatutLabel(statut)}
                </CardTitle>
                {getStatutIcon(statut)}
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{count}</div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Liste des transferts */}
      <div className="grid gap-4">
        {transferts?.map((transfert) => (
          <Card key={transfert.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-primary/10">
                    {getStatutIcon(transfert.statut)}
                  </div>
                  <div>
                    <h3 className="font-semibold">
                      {transfert.boutique_source?.nom} → {transfert.boutique_destination?.nom}
                    </h3>
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      <span>
                        Créé le {formatDate(transfert.created_at)}
                      </span>
                      {transfert.employe_expediteur && (
                        <>
                          <span>•</span>
                          <User className="h-3 w-3" />
                          <span>
                            {transfert.employe_expediteur.prenom} {transfert.employe_expediteur.nom}
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Badge variant={getStatutVariant(transfert.statut)}>
                    {getStatutLabel(transfert.statut)}
                  </Badge>
                  
                  <div className="text-right">
                    {transfert.date_expedition && (
                      <div className="text-xs text-muted-foreground">
                        Expédié le {formatDate(transfert.date_expedition)}
                      </div>
                    )}
                    {transfert.date_reception && (
                      <div className="text-xs text-success">
                        Reçu le {formatDate(transfert.date_reception)}
                      </div>
                    )}
                  </div>
                  
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm">
                    Détails
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-6xl max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Détails du transfert</DialogTitle>
                  </DialogHeader>
                  <TransfertDetail transfertId={transfert.id} />
                </DialogContent>
              </Dialog>
                </div>
              </div>
              
              {transfert.commentaires && (
                <div className="mt-3 p-3 bg-muted rounded-md">
                  <p className="text-sm text-muted-foreground">{transfert.commentaires}</p>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};