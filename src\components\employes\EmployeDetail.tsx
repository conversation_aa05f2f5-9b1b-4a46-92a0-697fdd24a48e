import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Edit, Mail, Phone, MapPin, Calendar, DollarSign, User, Building2 } from 'lucide-react';
import { type Employe } from '@/hooks/useEmployes';
import { formatCurrency } from '@/lib/formatters';
import { format, differenceInYears } from 'date-fns';
import { fr } from 'date-fns/locale';

interface EmployeDetailProps {
  employe: Employe;
  onEdit: () => void;
  onClose: () => void;
}

export const EmployeDetail: React.FC<EmployeDetailProps> = ({
  employe,
  onEdit,
  onClose,
}) => {
  const anciennete = differenceInYears(new Date(), new Date(employe.date_embauche));

  const getStatutVariant = (statut: string) => {
    switch (statut) {
      case 'actif': return 'default';
      case 'inactif': return 'secondary';
      case 'suspendu': return 'destructive';
      default: return 'outline';
    }
  };

  const getStatutColor = (statut: string) => {
    switch (statut) {
      case 'actif': return 'text-green-600';
      case 'inactif': return 'text-gray-500';
      case 'suspendu': return 'text-red-600';
      default: return 'text-muted-foreground';
    }
  };

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
            <User className="h-8 w-8 text-primary" />
          </div>
          <div>
            <h2 className="text-2xl font-bold">{employe.prenom} {employe.nom}</h2>
            <p className="text-muted-foreground">{employe.poste}</p>
            <Badge variant={getStatutVariant(employe.statut)} className="mt-1">
              {employe.statut}
            </Badge>
          </div>
        </div>
        <Button onClick={onEdit}>
          <Edit className="h-4 w-4 mr-2" />
          Modifier
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Informations personnelles */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Informations personnelles</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-3">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm text-muted-foreground">Email</p>
                <p className="font-medium">{employe.email}</p>
              </div>
            </div>

            {employe.telephone && (
              <div className="flex items-center space-x-3">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">Téléphone</p>
                  <p className="font-medium">{employe.telephone}</p>
                </div>
              </div>
            )}

            <div className="flex items-center space-x-3">
              <User className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm text-muted-foreground">Statut</p>
                <p className={`font-medium ${getStatutColor(employe.statut)}`}>
                  {employe.statut}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Informations professionnelles */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Informations professionnelles</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-3">
              <Building2 className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm text-muted-foreground">Boutique</p>
                <p className="font-medium">{employe.boutiques?.nom || 'Non assignée'}</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm text-muted-foreground">Date d'embauche</p>
                <p className="font-medium">
                  {format(new Date(employe.date_embauche), 'dd MMMM yyyy', { locale: fr })}
                </p>
                <p className="text-sm text-muted-foreground">
                  {anciennete > 0 ? `${anciennete} an${anciennete > 1 ? 's' : ''} d'ancienneté` : 'Moins d\'un an'}
                </p>
              </div>
            </div>

            {employe.salaire && (
              <div className="flex items-center space-x-3">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">Salaire</p>
                  <p className="font-medium">{formatCurrency(employe.salaire)}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Informations supplémentaires */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Informations système</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">Créé le</p>
              <p className="font-medium">
                {format(new Date(employe.created_at), 'dd/MM/yyyy à HH:mm', { locale: fr })}
              </p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Dernière modification</p>
              <p className="font-medium">
                {format(new Date(employe.updated_at), 'dd/MM/yyyy à HH:mm', { locale: fr })}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex justify-end space-x-2">
        <Button variant="outline" onClick={onClose}>
          Fermer
        </Button>
        <Button onClick={onEdit}>
          <Edit className="h-4 w-4 mr-2" />
          Modifier l'employé
        </Button>
      </div>
    </div>
  );
};