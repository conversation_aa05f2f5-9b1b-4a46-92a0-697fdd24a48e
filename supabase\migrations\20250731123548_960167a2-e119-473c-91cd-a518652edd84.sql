-- Fonction pour traiter automatiquement les transferts et impacter les stocks
CREATE OR REPLACE FUNCTION public.process_transfert_stock_impact()
RETURNS TRIGGER AS $$
DECLARE
  detail_record RECORD;
BEGIN
  -- Quand un transfert passe à "expedie", on retire du stock source
  IF NEW.statut = 'expedie' AND (OLD.statut IS NULL OR OLD.statut != 'expedie') THEN
    -- Parcourir tous les détails du transfert
    FOR detail_record IN 
      SELECT td.produit_id, td.quantite 
      FROM public.transfert_details td 
      WHERE td.transfert_id = NEW.id
    LOOP
      -- Retirer du stock de la boutique source
      UPDATE public.stocks 
      SET quantite = quantite - detail_record.quantite
      WHERE produit_id = detail_record.produit_id 
        AND boutique_id = NEW.boutique_source_id;
      
      -- Enregistrer le mouvement de stock (sortie)
      INSERT INTO public.mouvements_stock (
        produit_id,
        boutique_id,
        type_mouvement,
        quantite_avant,
        quantite_apres,
        quantite_mouvement,
        motif,
        reference_type,
        reference_id,
        created_by
      )
      SELECT 
        detail_record.produit_id,
        NEW.boutique_source_id,
        'sortie',
        s.quantite + detail_record.quantite,
        s.quantite,
        -detail_record.quantite,
        'Transfert expédié vers ' || b.nom,
        'transfert',
        NEW.id,
        NEW.employe_expediteur_id
      FROM public.stocks s, public.boutiques b
      WHERE s.produit_id = detail_record.produit_id 
        AND s.boutique_id = NEW.boutique_source_id
        AND b.id = NEW.boutique_destination_id;
    END LOOP;
  END IF;
  
  -- Quand un transfert passe à "recu", on ajoute au stock destination
  IF NEW.statut = 'recu' AND (OLD.statut IS NULL OR OLD.statut != 'recu') THEN
    -- Parcourir tous les détails du transfert
    FOR detail_record IN 
      SELECT td.produit_id, td.quantite 
      FROM public.transfert_details td 
      WHERE td.transfert_id = NEW.id
    LOOP
      -- Ajouter au stock de la boutique destination (ou créer si n'existe pas)
      INSERT INTO public.stocks (produit_id, boutique_id, quantite, seuil_alerte)
      VALUES (detail_record.produit_id, NEW.boutique_destination_id, detail_record.quantite, 5)
      ON CONFLICT (produit_id, boutique_id) 
      DO UPDATE SET quantite = stocks.quantite + detail_record.quantite;
      
      -- Enregistrer le mouvement de stock (entrée)
      INSERT INTO public.mouvements_stock (
        produit_id,
        boutique_id,
        type_mouvement,
        quantite_avant,
        quantite_apres,
        quantite_mouvement,
        motif,
        reference_type,
        reference_id,
        created_by
      )
      SELECT 
        detail_record.produit_id,
        NEW.boutique_destination_id,
        'entree',
        COALESCE(s.quantite, 0) - detail_record.quantite,
        COALESCE(s.quantite, detail_record.quantite),
        detail_record.quantite,
        'Transfert reçu de ' || b.nom,
        'transfert',
        NEW.id,
        NEW.employe_recepteur_id
      FROM public.boutiques b
      LEFT JOIN public.stocks s ON s.produit_id = detail_record.produit_id 
        AND s.boutique_id = NEW.boutique_destination_id
      WHERE b.id = NEW.boutique_source_id;
    END LOOP;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Créer le trigger pour traiter automatiquement les impacts sur le stock
CREATE TRIGGER trigger_process_transfert_stock_impact
  AFTER UPDATE ON public.transferts
  FOR EACH ROW
  EXECUTE FUNCTION public.process_transfert_stock_impact();

-- Fonction pour valider les transferts
CREATE OR REPLACE FUNCTION public.validate_transfert()
RETURNS TRIGGER AS $$
BEGIN
  -- Vérifier que les boutiques source et destination sont différentes
  IF NEW.boutique_source_id = NEW.boutique_destination_id THEN
    RAISE EXCEPTION 'La boutique source et destination ne peuvent pas être identiques';
  END IF;
  
  -- Automatiquement mettre les dates
  IF NEW.statut = 'expedie' AND NEW.date_expedition IS NULL THEN
    NEW.date_expedition = NOW();
  END IF;
  
  IF NEW.statut = 'recu' AND NEW.date_reception IS NULL THEN
    NEW.date_reception = NOW();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Créer le trigger de validation
CREATE TRIGGER trigger_validate_transfert
  BEFORE INSERT OR UPDATE ON public.transferts
  FOR EACH ROW
  EXECUTE FUNCTION public.validate_transfert();

-- Ajouter une contrainte unique sur (produit_id, boutique_id) pour la table stocks
ALTER TABLE public.stocks 
ADD CONSTRAINT unique_produit_boutique 
UNIQUE (produit_id, boutique_id);