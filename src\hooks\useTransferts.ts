import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface Transfert {
  id: string;
  boutique_source_id: string;
  boutique_destination_id: string;
  employe_expediteur_id: string;
  statut: 'en_attente' | 'expedie' | 'en_transit' | 'recu' | 'annule';
  date_expedition?: string;
  date_reception?: string;
  commentaires?: string;
  created_at: string;
  boutique_source?: { nom: string };
  boutique_destination?: { nom: string };
  employe_expediteur?: { nom: string; prenom: string };
}

export interface CreateTransfertData {
  boutique_source_id: string;
  boutique_destination_id: string;
  employe_expediteur_id: string;
  commentaires?: string;
}

export const useTransferts = (boutiqueId?: string) => {
  return useQuery({
    queryKey: ['transferts', boutiqueId],
    queryFn: async () => {
      let query = supabase
        .from('transferts')
        .select(`
          *,
          boutique_source:boutique_source_id(nom),
          boutique_destination:boutique_destination_id(nom),
          employe_expediteur:employe_expediteur_id(nom, prenom)
        `);
      
      if (boutiqueId) {
        query = query.or(`boutique_source_id.eq.${boutiqueId},boutique_destination_id.eq.${boutiqueId}`);
      }
      
      const { data, error } = await query.order('created_at', { ascending: false });
      
      if (error) throw error;
      return data as any[];
    },
    // Transferts changent modérément
    staleTime: 3 * 60 * 1000,  // 3 minutes
    gcTime: 8 * 60 * 1000,     // 8 minutes
  });
};

export const useTransfertById = (id: string) => {
  return useQuery({
    queryKey: ['transferts', id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('transferts')
        .select(`
          *,
          boutique_source:boutique_source_id(nom),
          boutique_destination:boutique_destination_id(nom),
          employe_expediteur:employe_expediteur_id(nom, prenom),
          employe_recepteur:employe_recepteur_id(nom, prenom)
        `)
        .eq('id', id)
        .single();
      
      if (error) throw error;
      return data as any;
    },
    enabled: !!id,
    staleTime: 3 * 60 * 1000,  // 3 minutes
    gcTime: 8 * 60 * 1000,     // 8 minutes
  });
};

export const useCreateTransfert = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (transfert: CreateTransfertData) => {
      // Ajouter le statut par défaut
      const transfertData = {
        ...transfert,
        statut: 'en_attente' as const
      };
      
      const { data, error } = await supabase
        .from('transferts')
        .insert(transfertData)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['transferts'] });
      toast({
        title: "Transfert créé",
        description: "Le transfert a été créé avec succès."
      });
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de créer le transfert."
      });
    }
  });
};

export const useUpdateTransfert = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, ...updates }: { id: string } & Partial<Omit<Transfert, 'id' | 'created_at'>>) => {
      const { data, error } = await supabase
        .from('transferts')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['transferts'] });
      queryClient.invalidateQueries({ queryKey: ['transferts', data.id] });
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de mettre à jour le transfert."
      });
    }
  });
};