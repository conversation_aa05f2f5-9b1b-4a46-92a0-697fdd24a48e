import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useMemo } from 'react';

interface FinancialReportsFilters {
  dateDebut: string;
  dateFin: string;
  boutiqueId: string;
  employeId: string;
  categorieId: string;
}

export const useFinancialReports = (filters: FinancialReportsFilters) => {
  const { data: ventesData, isLoading: ventesLoading } = useQuery({
    queryKey: ['financial-ventes', filters],
    queryFn: async () => {
      let query = supabase
        .from('ventes')
        .select(`
          *,
          vente_details:vente_details(
            *,
            produits:produit_id(prix_achat, prix_vente, categories:categorie_id(nom))
          )
        `);

      if (filters.dateDebut) {
        query = query.gte('date_vente', filters.dateDebut);
      }
      if (filters.dateFin) {
        query = query.lte('date_vente', filters.dateFin);
      }
      if (filters.boutiqueId) {
        query = query.eq('boutique_id', filters.boutiqueId);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data;
    },
    staleTime: 5 * 60 * 1000,
  });

  const isLoading = ventesLoading;

  // Analyse des marges
  const margesAnalyse = useMemo(() => {
    if (!ventesData) return null;

    let coutTotalAchat = 0;
    let chiffreAffairesTotal = 0;
    const margesParCategorie: Record<string, { ca: number; cout: number; nom: string }> = {};

    ventesData.forEach(vente => {
      vente.vente_details?.forEach(detail => {
        const prixAchat = detail.produits?.prix_achat || 0;
        const prixVente = detail.prix_unitaire || 0;
        const quantite = detail.quantite || 0;
        const categorie = detail.produits?.categories?.nom || 'Non classé';

        const coutDetail = prixAchat * quantite;
        const caDetail = prixVente * quantite;

        coutTotalAchat += coutDetail;
        chiffreAffairesTotal += caDetail;

        if (!margesParCategorie[categorie]) {
          margesParCategorie[categorie] = { ca: 0, cout: 0, nom: categorie };
        }
        margesParCategorie[categorie].ca += caDetail;
        margesParCategorie[categorie].cout += coutDetail;
      });
    });

    const montantMargeBrute = chiffreAffairesTotal - coutTotalAchat;
    const margeBruteGlobale = chiffreAffairesTotal > 0 
      ? Math.round((montantMargeBrute / chiffreAffairesTotal) * 100) 
      : 0;

    const margesParCategorieArray = Object.values(margesParCategorie).map(cat => ({
      nom: cat.nom,
      ca: cat.ca,
      marge: cat.ca > 0 ? Math.round(((cat.ca - cat.cout) / cat.ca) * 100) : 0
    }));

    return {
      margeBruteGlobale,
      montantMargeBrute,
      margesParCategorie: margesParCategorieArray,
      chiffreAffairesTotal,
      coutTotalAchat
    };
  }, [ventesData]);

  // Analyse de rentabilité
  const rentabilite = useMemo(() => {
    if (!margesAnalyse) return null;

    // Simulation des charges opérationnelles (à adapter selon les données réelles)
    const chargesOperationnelles = margesAnalyse.chiffreAffairesTotal * 0.15; // 15% du CA
    const beneficeNet = margesAnalyse.montantMargeBrute - chargesOperationnelles;
    const rentabiliteNette = margesAnalyse.chiffreAffairesTotal > 0 
      ? Math.round((beneficeNet / margesAnalyse.chiffreAffairesTotal) * 100)
      : 0;

    return {
      rentabiliteNette,
      beneficeNet,
      chargesOperationnelles
    };
  }, [margesAnalyse]);

  // Analyse TVA
  const tvaAnalyse = useMemo(() => {
    if (!ventesData) return null;

    const tvaCollectee = ventesData.reduce((sum, vente) => sum + (vente.montant_tva || 0), 0);
    const tvaDeductible = tvaCollectee * 0.3; // Simulation: 30% de TVA déductible
    const tvaADeclarer = Math.max(0, tvaCollectee - tvaDeductible);

    return {
      tvaCollectee,
      tvaDeductible,
      tvaADeclarer
    };
  }, [ventesData]);

  // Coûts opérationnels
  const coutOperationnels = useMemo(() => {
    if (!margesAnalyse) return null;

    const chargesTotales = margesAnalyse.chiffreAffairesTotal * 0.18; // 18% du CA
    const pourcentageCA = 18;

    return {
      chargesTotales,
      pourcentageCA
    };
  }, [margesAnalyse]);

  // Évolution des marges
  const evolutionMarges = useMemo(() => {
    if (!ventesData) return [];

    // Simulation de l'évolution sur 6 mois
    const mois = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'];
    return mois.map((mois, index) => {
      const baseCA = 8000000 + (index * 500000);
      const baseMarge = baseCA * (0.25 + Math.random() * 0.1); // 25-35% de marge
      
      return {
        mois,
        margeMontant: baseMarge,
        margePourcentage: Math.round((baseMarge / baseCA) * 100)
      };
    });
  }, [ventesData]);

  // Répartition des charges
  const repartitionCharges = useMemo(() => {
    if (!coutOperationnels) return [];

    const charges = [
      { name: 'Personnel', montant: coutOperationnels.chargesTotales * 0.6 },
      { name: 'Loyers', montant: coutOperationnels.chargesTotales * 0.2 },
      { name: 'Marketing', montant: coutOperationnels.chargesTotales * 0.1 },
      { name: 'Autres', montant: coutOperationnels.chargesTotales * 0.1 }
    ];

    return charges;
  }, [coutOperationnels]);

  return {
    data: ventesData,
    isLoading,
    stats: {
      margeBruteGlobale: margesAnalyse?.margeBruteGlobale || 0,
      montantMargeBrute: margesAnalyse?.montantMargeBrute || 0,
      rentabiliteNette: rentabilite?.rentabiliteNette || 0,
      beneficeNet: rentabilite?.beneficeNet || 0,
      tvaADeclarer: tvaAnalyse?.tvaADeclarer || 0,
      chargesTotales: coutOperationnels?.chargesTotales || 0,
      pourcentageCA: coutOperationnels?.pourcentageCA || 0
    },
    margesAnalyse,
    rentabilite,
    tvaAnalyse,
    coutOperationnels,
    evolutionMarges,
    repartitionCharges,
    margesParCategorie: margesAnalyse?.margesParCategorie || [],
    analyseTVA: tvaAnalyse,
    alertesFinancieres: []
  };
};