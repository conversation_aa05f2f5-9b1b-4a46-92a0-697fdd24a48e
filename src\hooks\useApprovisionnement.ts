import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface Fournisseur {
  id: string;
  nom: string;
  contact_nom?: string;
  email?: string;
  telephone?: string;
  adresse?: string;
  conditions_paiement?: string;
  delai_livraison?: number;
  statut: 'actif' | 'inactif' | 'suspendu';
  created_at: string;
  updated_at: string;
}

export interface CommandeFournisseur {
  id: string;
  numero_commande: string;
  fournisseur_id: string;
  boutique_id: string;
  date_commande: string;
  date_livraison_prevue?: string;
  date_livraison_reelle?: string;
  statut: 'en_attente' | 'confirmee' | 'expediee' | 'livree' | 'annulee';
  montant_total: number;
  montant_tva: number;
  commentaires?: string;
  responsable_id?: string;
  created_at: string;
  updated_at: string;
  fournisseurs?: {
    nom: string;
    contact_nom?: string;
  };
  boutiques?: {
    nom: string;
  };
  employes?: {
    nom: string;
    prenom: string;
  };
}

export interface CommandeDetail {
  id: string;
  commande_id: string;
  produit_id: string;
  quantite_commandee: number;
  quantite_livree: number;
  prix_unitaire: number;
  prix_total: number;
  created_at: string;
  produits?: {
    nom: string;
    marque: string;
    modele: string;
    code_produit: string;
  };
}

// Hook pour les fournisseurs
export const useFournisseurs = () => {
  return useQuery({
    queryKey: ['fournisseurs'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('fournisseurs')
        .select('*')
        .order('nom', { ascending: true });
      
      if (error) throw error;
      return data as Fournisseur[];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook pour créer/modifier un fournisseur
export const useUpsertFournisseur = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: Omit<Fournisseur, 'created_at' | 'updated_at'>) => {
      const { data: result, error } = await supabase
        .from('fournisseurs')
        .upsert(data)
        .select()
        .single();
      
      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fournisseurs'] });
      toast({
        title: "Fournisseur sauvegardé",
        description: "Les informations du fournisseur ont été mises à jour."
      });
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de sauvegarder le fournisseur."
      });
    }
  });
};

// Hook pour les commandes fournisseur
export const useCommandesFournisseur = (filters?: {
  boutiqueId?: string;
  fournisseurId?: string;
  statut?: string;
}) => {
  return useQuery({
    queryKey: ['commandes_fournisseur', filters],
    queryFn: async () => {
      let query = supabase
        .from('commandes_fournisseur')
        .select(`
          *,
          fournisseurs:fournisseur_id(nom, contact_nom),
          boutiques:boutique_id(nom),
          employes:responsable_id(nom, prenom)
        `);
      
      if (filters?.boutiqueId) {
        query = query.eq('boutique_id', filters.boutiqueId);
      }
      if (filters?.fournisseurId) {
        query = query.eq('fournisseur_id', filters.fournisseurId);
      }
      if (filters?.statut) {
        query = query.eq('statut', filters.statut);
      }
      
      const { data, error } = await query.order('created_at', { ascending: false });
      
      if (error) throw error;
      return data as CommandeFournisseur[];
    },
    staleTime: 30 * 1000, // 30 secondes
  });
};

// Hook pour créer une commande fournisseur
export const useCreateCommandeFournisseur = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: {
      fournisseur_id: string;
      boutique_id: string;
      date_livraison_prevue?: string;
      commentaires?: string;
      responsable_id?: string;
      details: Array<{
        produit_id: string;
        quantite_commandee: number;
        prix_unitaire: number;
      }>;
    }) => {
      // Générer un numéro de commande unique
      const numeroCommande = `CMD-${Date.now()}`;
      
      // Calculer le montant total
      const montantTotal = data.details.reduce((total, detail) => 
        total + (detail.quantite_commandee * detail.prix_unitaire), 0
      );
      
      // Créer la commande
      const { data: commande, error: commandeError } = await supabase
        .from('commandes_fournisseur')
        .insert({
          numero_commande: numeroCommande,
          fournisseur_id: data.fournisseur_id,
          boutique_id: data.boutique_id,
          date_livraison_prevue: data.date_livraison_prevue,
          montant_total: montantTotal,
          montant_tva: montantTotal * 0.2, // 20% TVA
          commentaires: data.commentaires,
          responsable_id: data.responsable_id,
        })
        .select()
        .single();
      
      if (commandeError) throw commandeError;

      // Créer les détails de commande
      const commandeDetails = data.details.map(detail => ({
        ...detail,
        commande_id: commande.id,
      }));

      const { error: detailsError } = await supabase
        .from('commande_details')
        .insert(commandeDetails);
      
      if (detailsError) throw detailsError;
      
      return commande;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['commandes_fournisseur'] });
      toast({
        title: "Commande créée",
        description: "La commande fournisseur a été créée avec succès."
      });
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de créer la commande fournisseur."
      });
    }
  });
};

// Hook pour les détails d'une commande
export const useCommandeDetails = (commandeId: string) => {
  return useQuery({
    queryKey: ['commande_details', commandeId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('commande_details')
        .select(`
          *,
          produits:produit_id(nom, marque, modele, code_produit)
        `)
        .eq('commande_id', commandeId)
        .order('created_at', { ascending: true });
      
      if (error) throw error;
      return data as CommandeDetail[];
    },
    enabled: !!commandeId,
  });
};

// Hook pour réceptionner une livraison
export const useReceptionnerLivraison = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ 
      commandeId, 
      livraisons 
    }: { 
      commandeId: string; 
      livraisons: Array<{
        detail_id: string;
        quantite_livree: number;
      }>;
    }) => {
      // Mettre à jour les quantités livrées
      for (const livraison of livraisons) {
        const { error: updateError } = await supabase
          .from('commande_details')
          .update({ quantite_livree: livraison.quantite_livree })
          .eq('id', livraison.detail_id);
        
        if (updateError) throw updateError;

        // Récupérer les infos du détail pour mettre à jour le stock
        const { data: detail, error: detailError } = await supabase
          .from('commande_details')
          .select(`
            *,
            commandes_fournisseur!inner(boutique_id)
          `)
          .eq('id', livraison.detail_id)
          .single();
        
        if (detailError) throw detailError;

        // Mettre à jour ou créer le stock
        const { data: existingStock, error: stockError } = await supabase
          .from('stocks')
          .select('*')
          .eq('produit_id', detail.produit_id)
          .eq('boutique_id', detail.commandes_fournisseur.boutique_id)
          .maybeSingle();
        
        if (stockError) throw stockError;

        if (existingStock) {
          // Augmenter le stock existant
          await supabase
            .from('stocks')
            .update({ 
              quantite: existingStock.quantite + livraison.quantite_livree 
            })
            .eq('id', existingStock.id);
        } else {
          // Créer un nouveau stock
          await supabase
            .from('stocks')
            .insert({
              produit_id: detail.produit_id,
              boutique_id: detail.commandes_fournisseur.boutique_id,
              quantite: livraison.quantite_livree,
              seuil_alerte: 5, // Valeur par défaut
            });
        }
      }

      // Vérifier si la commande est complètement livrée
      const { data: details, error: checkError } = await supabase
        .from('commande_details')
        .select('quantite_commandee, quantite_livree')
        .eq('commande_id', commandeId);
      
      if (checkError) throw checkError;

      const isComplete = details.every(detail => 
        detail.quantite_livree >= detail.quantite_commandee
      );

      // Mettre à jour le statut de la commande
      const nouveauStatut = isComplete ? 'livree' : 'expediee';
      const { error: statutError } = await supabase
        .from('commandes_fournisseur')
        .update({ 
          statut: nouveauStatut,
          date_livraison_reelle: new Date().toISOString()
        })
        .eq('id', commandeId);
      
      if (statutError) throw statutError;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['commandes_fournisseur'] });
      queryClient.invalidateQueries({ queryKey: ['commande_details'] });
      queryClient.invalidateQueries({ queryKey: ['stocks'] });
      toast({
        title: "Livraison réceptionnée",
        description: "Les stocks ont été mis à jour avec la livraison."
      });
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de réceptionner la livraison."
      });
    }
  });
};