import React from 'react';
import { VenteWithDetails } from '@/hooks/useVenteDetails';
import { formatCurrency, formatDate } from '@/lib/formatters';

interface InvoiceTemplateProps {
  vente: VenteWithDetails;
  companyInfo?: {
    nom: string;
    adresse: string;
    telephone: string;
    email: string;
    siret?: string;
    tva?: string;
    logo?: string;
  };
}

export const InvoiceTemplate: React.FC<InvoiceTemplateProps> = ({ 
  vente, 
  companyInfo = {
    nom: "Maison des Téléphones",
    adresse: "Zone Industrielle, Abidjan, Côte d'Ivoire",
    telephone: "+225 27 20 XX XX XX",
    email: "<EMAIL>",
    siret: "CI-ABJ-2024-B-XXXXX",
    tva: "CI0000000X"
  }
}) => {
  return (
    <div 
      id="invoice-template" 
      className="bg-white p-8 max-w-4xl mx-auto text-sm text-gray-800"
      style={{ minHeight: '297mm', width: '210mm' }}
    >
      {/* En-tête entreprise */}
      <div className="flex justify-between items-start mb-8 border-b border-gray-200 pb-6">
        <div className="flex-1">
          {companyInfo.logo && (
            <img 
              src={companyInfo.logo} 
              alt="Logo" 
              className="h-16 mb-4 object-contain"
            />
          )}
          <h1 className="text-2xl font-bold text-blue-600 mb-2">
            {companyInfo.nom}
          </h1>
          <div className="text-gray-600 space-y-1">
            <p>{companyInfo.adresse}</p>
            <p>Tél: {companyInfo.telephone}</p>
            <p>Email: {companyInfo.email}</p>
            {companyInfo.siret && <p>SIRET: {companyInfo.siret}</p>}
            {companyInfo.tva && <p>N° TVA: {companyInfo.tva}</p>}
          </div>
        </div>
        
        <div className="text-right">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">FACTURE</h2>
          <div className="bg-blue-50 p-4 rounded-lg">
            <p className="font-semibold text-blue-800">
              N° {vente.numero_facture}
            </p>
            <p className="text-gray-600">
              Date: {formatDate(vente.date_vente)}
            </p>
          </div>
        </div>
      </div>

      {/* Informations client et boutique */}
      <div className="grid grid-cols-2 gap-8 mb-8">
        <div>
          <h3 className="font-bold text-gray-800 mb-3 border-b border-gray-200 pb-1">
            FACTURÉ À
          </h3>
          <div className="space-y-1">
            <p className="font-semibold">{vente.client_nom || 'Client anonyme'}</p>
            {vente.client_telephone && <p>Tél: {vente.client_telephone}</p>}
            {vente.client_email && <p>Email: {vente.client_email}</p>}
          </div>
        </div>
        
        <div>
          <h3 className="font-bold text-gray-800 mb-3 border-b border-gray-200 pb-1">
            BOUTIQUE
          </h3>
          <div className="space-y-1">
            <p className="font-semibold">{vente.boutiques?.nom}</p>
            <p>Boutique: {vente.boutiques?.nom}</p>
            <p>Vendeur: {vente.employes?.nom}</p>
          </div>
        </div>
      </div>

      {/* Tableau des produits */}
      <div className="mb-8">
        <table className="w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-blue-50">
              <th className="border border-gray-300 p-3 text-left font-semibold">Produit</th>
              <th className="border border-gray-300 p-3 text-center font-semibold">Qté</th>
              <th className="border border-gray-300 p-3 text-right font-semibold">Prix Unit.</th>
              <th className="border border-gray-300 p-3 text-center font-semibold">Remise</th>
              <th className="border border-gray-300 p-3 text-right font-semibold">Total</th>
            </tr>
          </thead>
          <tbody>
            {vente.vente_details?.map((detail, index) => (
              <tr key={index} className="hover:bg-gray-50">
                <td className="border border-gray-300 p-3">
                  <div>
                    <p className="font-medium">{detail.produits?.nom}</p>
                    {detail.produits?.code_produit && (
                      <p className="text-sm text-gray-500">Code: {detail.produits?.code_produit}</p>
                    )}
                  </div>
                </td>
                <td className="border border-gray-300 p-3 text-center">
                  {detail.quantite}
                </td>
                <td className="border border-gray-300 p-3 text-right">
                  {formatCurrency(detail.prix_unitaire)}
                </td>
                <td className="border border-gray-300 p-3 text-center">
                  {detail.remise}%
                </td>
                <td className="border border-gray-300 p-3 text-right font-medium">
                  {formatCurrency(detail.sous_total)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Totaux */}
      <div className="flex justify-end mb-8">
        <div className="w-80">
          <div className="bg-gray-50 p-4 rounded-lg space-y-2">
            <div className="flex justify-between">
              <span>Sous-total HT:</span>
              <span className="font-medium">
                {formatCurrency((vente.montant_total - vente.montant_tva) || 0)}
              </span>
            </div>
            <div className="flex justify-between">
              <span>TVA:</span>
              <span className="font-medium">{formatCurrency(vente.montant_tva || 0)}</span>
            </div>
            <div className="border-t border-gray-300 pt-2">
              <div className="flex justify-between text-lg font-bold text-blue-600">
                <span>TOTAL TTC:</span>
                <span>{formatCurrency(vente.montant_total)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Informations de paiement */}
      <div className="mb-8 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-bold text-gray-800 mb-2">PAIEMENT</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <span className="text-gray-600">Mode de paiement:</span>
            <span className="ml-2 font-medium">{vente.mode_paiement}</span>
          </div>
          <div>
            <span className="text-gray-600">Statut:</span>
            <span className={`ml-2 px-2 py-1 rounded text-xs font-medium ${
              vente.statut === 'validee' ? 'bg-green-100 text-green-800' :
              vente.statut === 'en_attente' ? 'bg-yellow-100 text-yellow-800' :
              vente.statut === 'annulee' ? 'bg-red-100 text-red-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {vente.statut}
            </span>
          </div>
        </div>
      </div>

      {/* Mentions légales */}
      <div className="text-xs text-gray-500 space-y-1 border-t border-gray-200 pt-4">
        <p className="font-semibold">CONDITIONS DE VENTE:</p>
        <p>• Paiement à réception de facture, sauf accord particulier.</p>
        <p>• Tout retard de paiement entraîne l'application d'intérêts de retard.</p>
        <p>• Les marchandises voyagent aux risques et périls du destinataire.</p>
        <p>• Garantie selon conditions du constructeur.</p>
        <p className="mt-2 text-center font-medium">
          Merci de votre confiance - {companyInfo.nom}
        </p>
      </div>
    </div>
  );
};