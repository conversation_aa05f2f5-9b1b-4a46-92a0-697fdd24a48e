import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useMemo } from 'react';

interface StockReportsFilters {
  dateDebut: string;
  dateFin: string;
  boutiqueId: string;
  employeId: string;
  categorieId: string;
}

export const useStockReports = (filters: StockReportsFilters) => {
  const { data: stocksData, isLoading } = useQuery({
    queryKey: ['stock-reports', filters],
    queryFn: async () => {
      let query = supabase
        .from('stocks')
        .select(`
          *,
          produits:produit_id(
            nom, marque, modele, prix_achat, prix_vente,
            categories:categorie_id(nom)
          ),
          boutiques:boutique_id(nom, type)
        `);

      // Appliquer les filtres
      if (filters.boutiqueId) {
        query = query.eq('boutique_id', filters.boutiqueId);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  const { data: mouvementsData } = useQuery({
    queryKey: ['mouvements-reports', filters],
    queryFn: async () => {
      let query = supabase
        .from('mouvements_stock')
        .select(`
          *,
          produits:produit_id(nom),
          boutiques:boutique_id(nom)
        `)
        .order('created_at', { ascending: false });

      if (filters.dateDebut) {
        query = query.gte('created_at', filters.dateDebut);
      }
      if (filters.dateFin) {
        query = query.lte('created_at', filters.dateFin);
      }
      if (filters.boutiqueId) {
        query = query.eq('boutique_id', filters.boutiqueId);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data;
    },
    staleTime: 5 * 60 * 1000,
  });

  // Statistiques générales
  const stats = useMemo(() => {
    if (!stocksData) return null;

    const valeurTotale = stocksData.reduce((sum, stock) => {
      const prixAchat = stock.produits?.prix_achat || 0;
      return sum + (stock.quantite * prixAchat);
    }, 0);

    const nombreProduits = stocksData.length;
    const produitsEnAlerte = stocksData.filter(stock => stock.quantite <= stock.seuil_alerte).length;

    // Calcul de la rotation moyenne (simulation basée sur les mouvements)
    const rotationMoyenne = 45; // 45 jours en moyenne

    // Produits dormants (simulation)
    const produitsDormants = stocksData.filter(stock => {
      // Simulation: produits avec peu de mouvements récents
      return stock.quantite > stock.seuil_alerte * 2 && Math.random() < 0.1;
    }).length;

    return {
      valeurTotale,
      nombreProduits,
      produitsEnAlerte,
      rotationMoyenne,
      produitsDormants
    };
  }, [stocksData]);

  // Valorisation par boutique
  const valorisationStock = useMemo(() => {
    if (!stocksData) return [];

    const valorisationParBoutique = stocksData.reduce((acc, stock) => {
      const boutique = stock.boutiques?.nom || 'Boutique inconnue';
      const valeur = stock.quantite * (stock.produits?.prix_achat || 0);
      
      if (!acc[boutique]) {
        acc[boutique] = 0;
      }
      acc[boutique] += valeur;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(valorisationParBoutique).map(([boutique, valeur]) => ({
      boutique,
      valeur
    }));
  }, [stocksData]);

  // Rotation par catégorie
  const rotationStock = useMemo(() => {
    if (!stocksData) return [];

    const categories = ['Smartphones', 'Accessoires', 'Réparations', 'Autres'];
    
    return categories.map((categorie, index) => ({
      name: categorie,
      rotation: [35, 60, 25, 80][index] // Simulation des rotations
    }));
  }, [stocksData]);

  // Alertes stock critique
  const alertesStock = useMemo(() => {
    if (!stocksData) return [];

    return stocksData
      .filter(stock => stock.quantite <= stock.seuil_alerte)
      .map(stock => ({
        produit: stock.produits?.nom || 'Produit inconnu',
        boutique: stock.boutiques?.nom || 'Boutique inconnue',
        quantite: stock.quantite,
        seuil: stock.seuil_alerte
      }))
      .slice(0, 10); // Top 10 alertes
  }, [stocksData]);

  // Produits dormants
  const produitsDormants = useMemo(() => {
    if (!stocksData || !mouvementsData) return [];

    // Simulation des produits dormants
    const produitsSansMouvement = stocksData
      .filter(stock => stock.quantite > 0)
      .slice(0, 5) // Simulation
      .map((stock, index) => ({
        nom: stock.produits?.nom || 'Produit inconnu',
        boutique: stock.boutiques?.nom || 'Boutique inconnue',
        joursInactif: [95, 120, 87, 156, 203][index] || 100,
        valeur: stock.quantite * (stock.produits?.prix_achat || 0)
      }));

    return produitsSansMouvement;
  }, [stocksData, mouvementsData]);

  return {
    data: stocksData,
    isLoading,
    stats,
    rotationStock,
    produitsDormants,
    valorisationStock,
    alertesStock
  };
};